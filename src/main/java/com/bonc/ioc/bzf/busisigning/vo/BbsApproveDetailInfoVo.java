package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

import com.bonc.ioc.common.validator.inf.*;

import javax.validation.constraints.*;

/**
 * 审批明细表 实体类
 *
 * <AUTHOR>
 * @date 2023-08-31
 * @change 2023-08-31 by 刘鹏伟 for init
 */
@ApiModel(value = "BbsApproveDetailInfoVo对象", description = "审批明细表")
public class BbsApproveDetailInfoVo extends BaseVo implements Serializable {


	/**
	 * 审批id
	 */
	@ApiModelProperty(value = "审批id")
	@NotBlank(message = "审批id不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
	private String approveDetailId;

	/**
	 * 审批id
	 */
	@ApiModelProperty(value = "审批id")
	@NotBlank(message = "审批id不能为空", groups = {UpdateValidatorGroup.class})
	private String approveId;

	/**
	 * 上级id
	 */
	@ApiModelProperty(value = "上级id")
	private String parentId;

	/**
	 * 意见说明
	 */
	@ApiModelProperty(value = "意见说明")
	private String commentExplanation;

	/**
	 * 审批类型(1.合同审签 2.合同审核)
	 */
	@ApiModelProperty(value = "审批类型(1.合同审签 2.合同审核)")
	private String approveType;

	/**
	 * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
	 */
	@ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
	private String approveStatus;

	/**
	 * 提交时间
	 */
	@ApiModelProperty(value = "提交时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date submitTime;

	/**
	 * 审批时间
	 */
	@ApiModelProperty(value = "审批时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date approveTime;

	/**
	 * 提交人id
	 */
	@ApiModelProperty(value = "提交人id")
	private String submitUserId;

	/**
	 * 审批人id
	 */
	@ApiModelProperty(value = "审批人id")
	private String approverUserId;


	@ApiModelProperty(value = "提交人姓名")
	private String submitUserName;

	@ApiModelProperty(value = "审批人姓名")
	private String approverUserName;
	/**
	 * 删除标识(1.未删除 0.已删除)
	 */
	@ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
	private Integer delFlag;

	public String getSubmitUserName() {
		return submitUserName;
	}

	public void setSubmitUserName(String submitUserName) {
		this.submitUserName = submitUserName;
	}

	public String getApproverUserName() {
		return approverUserName;
	}

	public void setApproverUserName(String approverUserName) {
		this.approverUserName = approverUserName;
	}

	/**
	 * @return 审批id
	 */
	public String getApproveDetailId() {
		return approveDetailId;
	}

	public void setApproveDetailId(String approveDetailId) {
		this.approveDetailId = approveDetailId;
	}

	/**
	 * @return 审批id
	 */
	public String getApproveId() {
		return approveId;
	}

	public void setApproveId(String approveId) {
		this.approveId = approveId;
	}

	/**
	 * @return 上级id
	 */
	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	/**
	 * @return 意见说明
	 */
	public String getCommentExplanation() {
		return commentExplanation;
	}

	public void setCommentExplanation(String commentExplanation) {
		this.commentExplanation = commentExplanation;
	}

	/**
	 * @return 审批类型(1.合同审签 2.合同审核)
	 */
	public String getApproveType() {
		return approveType;
	}

	public void setApproveType(String approveType) {
		this.approveType = approveType;
	}

	/**
	 * @return 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
	 */
	public String getApproveStatus() {
		return approveStatus;
	}

	public void setApproveStatus(String approveStatus) {
		this.approveStatus = approveStatus;
	}

	/**
	 * @return 提交时间
	 */
	public Date getSubmitTime() {
		if (submitTime != null) {
			return (Date) submitTime.clone();
		} else {
			return null;
		}
	}

	public void setSubmitTime(Date submitTime) {
		if (submitTime == null) {
			this.submitTime = null;
		} else {
			this.submitTime = (Date) submitTime.clone();
		}
	}

	/**
	 * @return 审批时间
	 */
	public Date getApproveTime() {
		if (approveTime != null) {
			return (Date) approveTime.clone();
		} else {
			return null;
		}
	}

	public void setApproveTime(Date approveTime) {
		if (approveTime == null) {
			this.approveTime = null;
		} else {
			this.approveTime = (Date) approveTime.clone();
		}
	}

	/**
	 * @return 提交人id
	 */
	public String getSubmitUserId() {
		return submitUserId;
	}

	public void setSubmitUserId(String submitUserId) {
		this.submitUserId = submitUserId;
	}

	/**
	 * @return 审批人id
	 */
	public String getApproverUserId() {
		return approverUserId;
	}

	public void setApproverUserId(String approverUserId) {
		this.approverUserId = approverUserId;
	}

	/**
	 * @return 删除标识(1.未删除 0.已删除)
	 */
	public Integer getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(Integer delFlag) {
		this.delFlag = delFlag;
	}

	@Override
	public String toString() {
		return "BbsApproveDetailInfoVo{" +
				"approveDetailId=" + approveDetailId +
				", approveId=" + approveId +
				", parentId=" + parentId +
				", commentExplanation=" + commentExplanation +
				", approveType=" + approveType +
				", approveStatus=" + approveStatus +
				", submitTime=" + submitTime +
				", approveTime=" + approveTime +
				", submitUserId=" + submitUserId +
				", approverUserId=" + approverUserId +
				", delFlag=" + delFlag +
				"}";
	}
}

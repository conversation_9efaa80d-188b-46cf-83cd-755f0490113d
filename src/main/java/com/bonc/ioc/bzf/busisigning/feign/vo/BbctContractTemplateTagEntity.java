package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 合同模板-标签 实体类
 *
 * <AUTHOR>
 * @date 2023-05-18
 * @change 2023-05-18 by 宋鑫 for init
 */
@TableName("bbct_contract_template_tag")
@ApiModel(value="BbctContractTemplateTagEntity对象", description="合同模板-标签")
public class BbctContractTemplateTagEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_TAG_ID = "tag_id";
    public static final String FIELD_TEMPLATE_ID = "template_id";
    public static final String FIELD_TAG_NAME = "tag_name";
    public static final String FIELD_TAG_VAL = "tag_val";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
                                  private String tagId;

    /**
     * 模板ID
     */
    @ApiModelProperty(value = "模板ID")
                            private String templateId;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
                            private String tagName;

    /**
     * 标签值
     */
    @ApiModelProperty(value = "标签值")
                            private String tagVal;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 主键
     */
    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    /**
     * @return 模板ID
     */
    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * @return 标签名称
     */
    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    /**
     * @return 标签值
     */
    public String getTagVal() {
        return tagVal;
    }

    public void setTagVal(String tagVal) {
        this.tagVal = tagVal;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbctContractTemplateTagEntity{" +
            "tagId=" + tagId +
            ", templateId=" + templateId +
            ", tagName=" + tagName +
            ", tagVal=" + tagVal +
            ", delFlag=" + delFlag +
        "}";
    }
}
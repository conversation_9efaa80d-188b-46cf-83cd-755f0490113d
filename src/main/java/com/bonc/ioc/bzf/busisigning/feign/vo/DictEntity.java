package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="DictEntity", description="DictEntity")
public class DictEntity {
    /**
     * 字典编码
     */
    @ApiModelProperty(value = "字典编码")
    private String typeCode;

    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
    private String typeName;

    /**
     * value"
     */
    @ApiModelProperty(value = "value")
    private String code;

    /**
     * name
     */
    @ApiModelProperty(value = "name")
    private String meaning;
}

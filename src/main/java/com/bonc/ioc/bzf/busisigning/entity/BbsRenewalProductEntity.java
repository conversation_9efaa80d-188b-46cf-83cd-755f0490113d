package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 人-产品：产品表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@TableName("bbs_renewal_product")
@ApiModel(value = "BbsRenewalProductEntity对象", description = "人-产品：产品表")
public class BbsRenewalProductEntity extends McpBaseEntity implements Serializable {

    public static final String FIELD_RP_ID = "rp_id";
    public static final String FIELD_RR_ID = "rr_id";
    public static final String FIELD_PRODUCT_NAME = "product_name";
    public static final String FIELD_PRODUCT_NO = "product_no";
    public static final String FIELD_PROJECT_ID = "project_id";
    public static final String FIELD_PROJECT_NO = "project_no";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_RENT_STANDARD_UNIT = "rent_standard_unit";
    public static final String FIELD_RENT_STANDARD_UNIT_NAME = "rent_standard_unit_name";
    public static final String FIELD_RENT_STANDARD = "rent_standard";
    public static final String FIELD_RENT_STANDARD_NO = "rent_standard_no";
    public static final String FIELD_RENT_STANDARD_NAME = "rent_standard_name";
    public static final String FIELD_RENT = "rent";
    public static final String FIELD_PROP_STANDARD_UNIT = "prop_standard_unit";
    public static final String FIELD_PROP_STANDARD_UNIT_NAME = "prop_standard_unit_name";
    public static final String FIELD_PROP_STANDARD = "prop_standard";
    public static final String FIELD_HOUSE_SELECTION_CHANNELS_NO = "house_selection_channels_no";
    public static final String FIELD_HOUSE_SELECTION_CHANNELS_NAME = "house_selection_channels_name";
    public static final String FIELD_COMMUNITY_BUILDING_NO = "community_building_no";
    public static final String FIELD_COMMUNITY_BUILDING_NAME = "community_building_name";
    public static final String FIELD_GROUP_NO = "group_no";
    public static final String FIELD_GROUP_NAME = "group_name";
    public static final String FIELD_BUILDING_NO = "building_no";
    public static final String FIELD_BUILDING_NAME = "building_name";
    public static final String FIELD_UNIT_NO = "unit_no";
    public static final String FIELD_UNIT_NAME = "unit_name";
    public static final String FIELD_HOUSE_TYPE_NO = "house_type_no";
    public static final String FIELD_HOUSE_TYPE_NAME = "house_type_name";
    public static final String FIELD_ROOM_NO = "room_no";
    public static final String FIELD_HOUSE_STRUCT_AREA = "house_struct_area";
    public static final String FIELD_JACKETED_CODE = "jacketed_code";
    public static final String FIELD_JACKETED = "jacketed";
    public static final String FIELD_ROOM_NAME = "room_name";
    public static final String FIELD_COMMUNITY_ADDRESS = "community_address";
    public static final String FIELD_COMMUNITY_REGION = "community_region";
    public static final String FIELD_DEL_FLAG = "del_flag";
    public static final String FIELD_CURRENT_FLOOR_NO = "current_floor_no";
    public static final String FIELD_TOTAL_FLOOR_NO = "total_floor_no";
    public static final String FIELD_HOUSE_ORIENTATION = "house_orientation";
    public static final String FIELD_PROJECT_SHORT_NAME = "project_short_name";
    public static final String FIELD_OPERATE_ENTITY_TYPE = "operate_entity_type";
    public static final String FIELD_OPERATE_ENTITY_NAME = "operate_entity_name";
    public static final String FIELD_OPERATE_UNIT_BUSINESS_NO = "operate_unit_business_no";
    public static final String FIELD_OPERATE_UNIT_NO = "operate_unit_no";
    public static final String FIELD_OPERATE_UNIT_NAME = "operate_unit_name";
    public static final String FIELD_PROJECT_AREA_BUSINESS_NO = "project_area_business_no";
    public static final String FIELD_PROJECT_AREA_NO = "project_area_no";
    public static final String FIELD_PROJECT_AREA_NAME = "project_area_name";
    public static final String FIELD_PROJECT_FORMAT = "project_format";
    public static final String FIELD_PROJECT_ESTATE = "project_estate";
    public static final String FIELD_HOUSE_NO_NCC = "house_no_ncc";
    public static final String FIELD_PROJECT_NO_NCC = "project_no_ncc";
    public static final String FIELD_HOUSE_HIRE_TYPE = "house_hire_type";
    public static final String FIELD_HOUSE_NO = "house_no";
    public static final String FIELD_BED_NO = "bed_no";
    public static final String FIELD_LEASE_MODE = "lease_mode";
    public static final String FIELD_HISTORY_STATE = "history_state";
    public static final String FIELD_HOUSE_CODE = "house_code";
    public static final String FIELD_BED_ROOM = "bed_room";
    public static final String FIELD_SOURCE_NODE = "source_node";
    public static final String FIELD_PRICE_SIDE_RATIO_NO = "price_side_ratio_no";
    public static final String FIELD_PRICE_SIDE_RATIO_NAME = "price_side_ratio_name";
    public static final String FIELD_MONTHLY_RENT_RULES_NO = "monthly_rent_rules_no";
    public static final String FIELD_MONTHLY_RENT_RULES_NAME = "monthly_rent_rules_name";
    public static final String FIELD_PUBLIC_RENT_STANDARD = "public_rent_standard";
    public static final String FIELD_MARKET_RENT_STANDARD = "market_rent_standard";
    public static final String FIELD_TALENT_RENT_STANDARD = "talent_rent_standard";
    public static final String FIELD_RENT_UNIT = "rent_unit";
    public static final String FIELD_PLANN_BUSINESS_FORMAT_NAME = "plann_business_format_name";
    public static final String FIELD_LOWEST_PRICE = "lowest_price";
    public static final String FIELD_MONTHLY_PROP_RULES_NO = "monthly_prop_rules_no";
    public static final String FIELD_MONTHLY_PROP_RULES_NAME = "monthly_prop_rules_name";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "rp_id", type = IdType.ASSIGN_UUID)
    private String rpId;

    /**
     * 人-产品关系外键
     */
    @ApiModelProperty(value = "人-产品关系外键")
    private String rrId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
    private String productNo;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 项目编号/运营项目
     */
    @ApiModelProperty(value = "项目编号/运营项目")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 租金标准单位
     */
    @ApiModelProperty(value = "租金标准单位")
    private String rentStandardUnit;

    /**
     * 租金标准单位名称
     */
    @ApiModelProperty(value = "租金标准单位名称")
    private String rentStandardUnitName;

    /**
     * 租金标准
     */
    @ApiModelProperty(value = "租金标准")
    private Double rentStandard;

    /**
     * 租金标准（元/m²/月）-废弃
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）-废弃")
    private String rentStandardNo;

    /**
     * 租金标准（元/m²/月）名称-废弃
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）名称-废弃")
    private String rentStandardName;

    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
    private Double rent;

    /**
     * 物业费标准单位
     */
    @ApiModelProperty(value = "物业费标准单位")
    private String propStandardUnit;

    /**
     * 物业费标准单位名称
     */
    @ApiModelProperty(value = "物业费标准单位名称")
    private String propStandardUnitName;

    /**
     * 物业费标准
     */
    @ApiModelProperty(value = "物业费标准")
    private Double propStandard;

    /**
     * 选房渠道(线上，线下)-废弃
     */
    @ApiModelProperty(value = "选房渠道(线上，线下)-废弃")
    private String houseSelectionChannelsNo;

    /**
     * 选房渠道名称(线上，线下)-废弃
     */
    @ApiModelProperty(value = "选房渠道名称(线上，线下)-废弃")
    private String houseSelectionChannelsName;

    /**
     * 小区或楼宇
     */
    @ApiModelProperty(value = "小区或楼宇")
    private String communityBuildingNo;

    /**
     * 小区或楼宇名称
     */
    @ApiModelProperty(value = "小区或楼宇名称")
    private String communityBuildingName;

    /**
     * 组团(组团名称)
     */
    @ApiModelProperty(value = "组团(组团名称)")
    private String groupNo;

    /**
     * 组团名称
     */
    @ApiModelProperty(value = "组团名称")
    private String groupName;

    /**
     * 楼号
     */
    @ApiModelProperty(value = "楼号")
    private String buildingNo;

    /**
     * 楼名称
     */
    @ApiModelProperty(value = "楼名称")
    private String buildingName;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    private String unitNo;

    /**
     * 单元名称
     */
    @ApiModelProperty(value = "单元名称")
    private String unitName;

    /**
     * 户型
     */
    @ApiModelProperty(value = "户型")
    private String houseTypeNo;

    /**
     * 户型名称
     */
    @ApiModelProperty(value = "户型名称")
    private String houseTypeName;

    /**
     * 房间号-屋号-废弃
     */
    @ApiModelProperty(value = "房间号-屋号-废弃")
    private String roomNo;

    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
    private String houseStructArea;

    /**
     * 套型编码
     */
    @ApiModelProperty(value = "套型编码")
    private String jacketedCode;

    /**
     * 套型
     */
    @ApiModelProperty(value = "套型")
    private String jacketed;

    /**
     * 房间名称-屋名称-废弃
     */
    @ApiModelProperty(value = "房间名称-屋名称-废弃")
    private String roomName;

    /**
     * 小区地址-废弃
     */
    @ApiModelProperty(value = "小区地址-废弃")
    private String communityAddress;

    /**
     * 小区所在区
     */
    @ApiModelProperty(value = "小区所在区")
    private String communityRegion;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 所在层
     */
    @ApiModelProperty(value = "所在层")
    private String currentFloorNo;

    /**
     * 总层数
     */
    @ApiModelProperty(value = "总层数")
    private String totalFloorNo;

    /**
     * 房间朝向
     */
    @ApiModelProperty(value = "房间朝向")
    private String houseOrientation;

    /**
     * 项目简称
     */
    @ApiModelProperty(value = "项目简称")
    private String projectShortName;

    /**
     * 运营主体类型
     */
    @ApiModelProperty(value = "运营主体类型")
    private String operateEntityType;

    /**
     * 运营主体名称
     */
    @ApiModelProperty(value = "运营主体名称")
    private String operateEntityName;

    /**
     * 运营单位业务中台编号
     */
    @ApiModelProperty(value = "运营单位业务中台编号")
    private String operateUnitBusinessNo;

    /**
     * 运营单位编号（NCC）
     */
    @ApiModelProperty(value = "运营单位编号（NCC）")
    private String operateUnitNo;

    /**
     * 运营单位名称
     */
    @ApiModelProperty(value = "运营单位名称")
    private String operateUnitName;

    /**
     * 项目区域业务中台编号
     */
    @ApiModelProperty(value = "项目区域业务中台编号")
    private String projectAreaBusinessNo;

    /**
     * 项目区域编号（NCC）
     */
    @ApiModelProperty(value = "项目区域编号（NCC）")
    private String projectAreaNo;

    /**
     * 项目区域名称
     */
    @ApiModelProperty(value = "项目区域名称")
    private String projectAreaName;

    /**
     * 项目业态
     */
    @ApiModelProperty(value = "项目业态")
    private String projectFormat;

    /**
     * 所在小区或楼宇名称
     */
    @ApiModelProperty(value = "所在小区或楼宇名称")
    private String projectEstate;

    /**
     * 房源编号NCC编码
     */
    @ApiModelProperty(value = "房源编号NCC编码")
    private String houseNoNcc;

    /**
     * 项目编号NCC编码
     */
    @ApiModelProperty(value = "项目编号NCC编码")
    private String projectNoNcc;

    /**
     * 房屋租赁类型
     */
    @ApiModelProperty(value = "房屋租赁类型")
    private String houseHireType;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String houseNo;

    /**
     * 床号-废弃
     */
    @ApiModelProperty(value = "床号-废弃")
    private String bedNo;

    /**
     * 租赁类型
     */
    @ApiModelProperty(value = "租赁类型")
    private String leaseMode;

    /**
     * 房态状态-废弃
     */
    @ApiModelProperty(value = "房态状态-废弃")
    private String historyState;

    /**
     * 房源编号
     */
    @ApiModelProperty(value = "房源编号")
    private String houseCode;

    /**
     * 居室
     */
    @ApiModelProperty(value = "居室")
    private String bedRoom;

    /**
     * 来源节点(配租登记，看房，选房，入住)-废弃
     */
    @ApiModelProperty(value = "来源节点(配租登记，看房，选房，入住)-废弃")
    private String sourceNode;

    /**
     * 定价策率编号-废弃
     */
    @ApiModelProperty(value = "定价策率编号-废弃")
    private String priceSideRatioNo;

    /**
     * 定价策率名称-废弃
     */
    @ApiModelProperty(value = "定价策率名称-废弃")
    private String priceSideRatioName;

    /**
     * 月租金规则编号（工银）-废弃
     */
    @ApiModelProperty(value = "月租金规则编号（工银）-废弃")
    private String monthlyRentRulesNo;

    /**
     * 月租金规则名称（工银）-废弃
     */
    @ApiModelProperty(value = "月租金规则名称（工银）-废弃")
    private String monthlyRentRulesName;

    /**
     * 公祖租金标准-废弃
     */
    @ApiModelProperty(value = "公祖租金标准-废弃")
    private Double publicRentStandard;

    /**
     * 市场租金标准-废弃
     */
    @ApiModelProperty(value = "市场租金标准-废弃")
    private Double marketRentStandard;

    /**
     * 人才租金标准-废弃
     */
    @ApiModelProperty(value = "人才租金标准-废弃")
    private Double talentRentStandard;

    /**
     * 租金单位名称（元/㎡/月、元/月）-废弃
     */
    @ApiModelProperty(value = "租金单位名称（元/㎡/月、元/月）-废弃")
    private String rentUnit;

    /**
     * 规划业态名称
     */
    @ApiModelProperty(value = "规划业态名称")
    private String plannBusinessFormatName;

    /**
     * 最低单价（招商方案备案底价单价），单位：元/平方米/天
     */
    @ApiModelProperty(value = "最低单价（招商方案备案底价单价），单位：元/平方米/天")
    private BigDecimal lowestPrice;

    /**
     * 月物业费规则编号（工银）-废弃
     */
    @ApiModelProperty(value = "月物业费规则编号（工银）-废弃")
    private String monthlyPropRulesNo;

    /**
     * 月物业费规则名称（工银）-废弃
     */
    @ApiModelProperty(value = "月物业费规则名称（工银）-废弃")
    private String monthlyPropRulesName;

    @ApiModelProperty(value = "装修状态名称")
    private String decorateState;

    @ApiModelProperty(value = "房屋实际用途名称")
    private String housePurposeName;


    @ApiModelProperty(value = "房屋取得用途名称(工银使用)")
    private String houseObtainPurposeName;

    @ApiModelProperty(value = "房屋取得用途编号(工银使用)")
    private String houseObtainPurposeCode;

    /**
     * 套内建筑面积
     */
    @ApiModelProperty(value = "套内建筑面积")
    private String innerSleeveArea;

    public String getInnerSleeveArea() {
        return innerSleeveArea;
    }

    public void setInnerSleeveArea(String innerSleeveArea) {
        this.innerSleeveArea = innerSleeveArea;
    }

    public String getHouseObtainPurposeName() {
        return houseObtainPurposeName;
    }

    public void setHouseObtainPurposeName(String houseObtainPurposeName) {
        this.houseObtainPurposeName = houseObtainPurposeName;
    }

    public String getHouseObtainPurposeCode() {
        return houseObtainPurposeCode;
    }

    public void setHouseObtainPurposeCode(String houseObtainPurposeCode) {
        this.houseObtainPurposeCode = houseObtainPurposeCode;
    }

    /**
     * @return 主键
     */
    public String getRpId() {
        return rpId;
    }

    public void setRpId(String rpId) {
        this.rpId = rpId;
    }

    /**
     * @return 人-产品关系外键
     */
    public String getRrId() {
        return rrId;
    }

    public void setRrId(String rrId) {
        this.rrId = rrId;
    }

    /**
     * @return 产品名称
     */
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * @return 产品编号
     */
    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 项目编号/运营项目
     */
    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 租金标准单位
     */
    public String getRentStandardUnit() {
        return rentStandardUnit;
    }

    public void setRentStandardUnit(String rentStandardUnit) {
        this.rentStandardUnit = rentStandardUnit;
    }

    /**
     * @return 租金标准单位名称
     */
    public String getRentStandardUnitName() {
        return rentStandardUnitName;
    }

    public void setRentStandardUnitName(String rentStandardUnitName) {
        this.rentStandardUnitName = rentStandardUnitName;
    }

    /**
     * @return 租金标准
     */
    public Double getRentStandard() {
        return rentStandard;
    }

    public void setRentStandard(Double rentStandard) {
        this.rentStandard = rentStandard;
    }

    /**
     * @return 租金标准（元/m²/月）-废弃
     */
    public String getRentStandardNo() {
        return rentStandardNo;
    }

    public void setRentStandardNo(String rentStandardNo) {
        this.rentStandardNo = rentStandardNo;
    }

    /**
     * @return 租金标准（元/m²/月）名称-废弃
     */
    public String getRentStandardName() {
        return rentStandardName;
    }

    public void setRentStandardName(String rentStandardName) {
        this.rentStandardName = rentStandardName;
    }

    /**
     * @return 租金
     */
    public Double getRent() {
        return rent;
    }

    public void setRent(Double rent) {
        this.rent = rent;
    }

    /**
     * @return 物业费标准单位
     */
    public String getPropStandardUnit() {
        return propStandardUnit;
    }

    public void setPropStandardUnit(String propStandardUnit) {
        this.propStandardUnit = propStandardUnit;
    }

    /**
     * @return 物业费标准单位名称
     */
    public String getPropStandardUnitName() {
        return propStandardUnitName;
    }

    public void setPropStandardUnitName(String propStandardUnitName) {
        this.propStandardUnitName = propStandardUnitName;
    }

    /**
     * @return 物业费标准
     */
    public Double getPropStandard() {
        return propStandard;
    }

    public void setPropStandard(Double propStandard) {
        this.propStandard = propStandard;
    }

    /**
     * @return 选房渠道(线上 ， 线下)-废弃
     */
    public String getHouseSelectionChannelsNo() {
        return houseSelectionChannelsNo;
    }

    public void setHouseSelectionChannelsNo(String houseSelectionChannelsNo) {
        this.houseSelectionChannelsNo = houseSelectionChannelsNo;
    }

    /**
     * @return 选房渠道名称(线上 ， 线下)-废弃
     */
    public String getHouseSelectionChannelsName() {
        return houseSelectionChannelsName;
    }

    public void setHouseSelectionChannelsName(String houseSelectionChannelsName) {
        this.houseSelectionChannelsName = houseSelectionChannelsName;
    }

    /**
     * @return 小区或楼宇
     */
    public String getCommunityBuildingNo() {
        return communityBuildingNo;
    }

    public void setCommunityBuildingNo(String communityBuildingNo) {
        this.communityBuildingNo = communityBuildingNo;
    }

    /**
     * @return 小区或楼宇名称
     */
    public String getCommunityBuildingName() {
        return communityBuildingName;
    }

    public void setCommunityBuildingName(String communityBuildingName) {
        this.communityBuildingName = communityBuildingName;
    }

    /**
     * @return 组团(组团名称)
     */
    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    /**
     * @return 组团名称
     */
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    /**
     * @return 楼号
     */
    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    /**
     * @return 楼名称
     */
    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    /**
     * @return 单元号
     */
    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    /**
     * @return 单元名称
     */
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * @return 户型
     */
    public String getHouseTypeNo() {
        return houseTypeNo;
    }

    public void setHouseTypeNo(String houseTypeNo) {
        this.houseTypeNo = houseTypeNo;
    }

    /**
     * @return 户型名称
     */
    public String getHouseTypeName() {
        return houseTypeName;
    }

    public void setHouseTypeName(String houseTypeName) {
        this.houseTypeName = houseTypeName;
    }

    /**
     * @return 房间号-屋号-废弃
     */
    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    /**
     * @return 建筑面积
     */
    public String getHouseStructArea() {
        return houseStructArea;
    }

    public void setHouseStructArea(String houseStructArea) {
        this.houseStructArea = houseStructArea;
    }

    /**
     * @return 套型编码
     */
    public String getJacketedCode() {
        return jacketedCode;
    }

    public void setJacketedCode(String jacketedCode) {
        this.jacketedCode = jacketedCode;
    }

    /**
     * @return 套型
     */
    public String getJacketed() {
        return jacketed;
    }

    public void setJacketed(String jacketed) {
        this.jacketed = jacketed;
    }

    /**
     * @return 房间名称-屋名称-废弃
     */
    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    /**
     * @return 小区地址-废弃
     */
    public String getCommunityAddress() {
        return communityAddress;
    }

    public void setCommunityAddress(String communityAddress) {
        this.communityAddress = communityAddress;
    }

    /**
     * @return 小区所在区
     */
    public String getCommunityRegion() {
        return communityRegion;
    }

    public void setCommunityRegion(String communityRegion) {
        this.communityRegion = communityRegion;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 所在层
     */
    public String getCurrentFloorNo() {
        return currentFloorNo;
    }

    public void setCurrentFloorNo(String currentFloorNo) {
        this.currentFloorNo = currentFloorNo;
    }

    /**
     * @return 总层数
     */
    public String getTotalFloorNo() {
        return totalFloorNo;
    }

    public void setTotalFloorNo(String totalFloorNo) {
        this.totalFloorNo = totalFloorNo;
    }

    /**
     * @return 房间朝向
     */
    public String getHouseOrientation() {
        return houseOrientation;
    }

    public void setHouseOrientation(String houseOrientation) {
        this.houseOrientation = houseOrientation;
    }

    /**
     * @return 项目简称
     */
    public String getProjectShortName() {
        return projectShortName;
    }

    public void setProjectShortName(String projectShortName) {
        this.projectShortName = projectShortName;
    }

    /**
     * @return 运营主体类型
     */
    public String getOperateEntityType() {
        return operateEntityType;
    }

    public void setOperateEntityType(String operateEntityType) {
        this.operateEntityType = operateEntityType;
    }

    /**
     * @return 运营主体名称
     */
    public String getOperateEntityName() {
        return operateEntityName;
    }

    public void setOperateEntityName(String operateEntityName) {
        this.operateEntityName = operateEntityName;
    }

    /**
     * @return 运营单位业务中台编号
     */
    public String getOperateUnitBusinessNo() {
        return operateUnitBusinessNo;
    }

    public void setOperateUnitBusinessNo(String operateUnitBusinessNo) {
        this.operateUnitBusinessNo = operateUnitBusinessNo;
    }

    /**
     * @return 运营单位编号（NCC）
     */
    public String getOperateUnitNo() {
        return operateUnitNo;
    }

    public void setOperateUnitNo(String operateUnitNo) {
        this.operateUnitNo = operateUnitNo;
    }

    /**
     * @return 运营单位名称
     */
    public String getOperateUnitName() {
        return operateUnitName;
    }

    public void setOperateUnitName(String operateUnitName) {
        this.operateUnitName = operateUnitName;
    }

    /**
     * @return 项目区域业务中台编号
     */
    public String getProjectAreaBusinessNo() {
        return projectAreaBusinessNo;
    }

    public void setProjectAreaBusinessNo(String projectAreaBusinessNo) {
        this.projectAreaBusinessNo = projectAreaBusinessNo;
    }

    /**
     * @return 项目区域编号（NCC）
     */
    public String getProjectAreaNo() {
        return projectAreaNo;
    }

    public void setProjectAreaNo(String projectAreaNo) {
        this.projectAreaNo = projectAreaNo;
    }

    /**
     * @return 项目区域名称
     */
    public String getProjectAreaName() {
        return projectAreaName;
    }

    public void setProjectAreaName(String projectAreaName) {
        this.projectAreaName = projectAreaName;
    }

    /**
     * @return 项目业态
     */
    public String getProjectFormat() {
        return projectFormat;
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = projectFormat;
    }

    /**
     * @return 所在小区或楼宇名称
     */
    public String getProjectEstate() {
        return projectEstate;
    }

    public void setProjectEstate(String projectEstate) {
        this.projectEstate = projectEstate;
    }

    /**
     * @return 房源编号NCC编码
     */
    public String getHouseNoNcc() {
        return houseNoNcc;
    }

    public void setHouseNoNcc(String houseNoNcc) {
        this.houseNoNcc = houseNoNcc;
    }

    /**
     * @return 项目编号NCC编码
     */
    public String getProjectNoNcc() {
        return projectNoNcc;
    }

    public void setProjectNoNcc(String projectNoNcc) {
        this.projectNoNcc = projectNoNcc;
    }

    /**
     * @return 房屋租赁类型
     */
    public String getHouseHireType() {
        return houseHireType;
    }

    public void setHouseHireType(String houseHireType) {
        this.houseHireType = houseHireType;
    }

    /**
     * @return 房间号
     */
    public String getHouseNo() {
        return houseNo;
    }

    public void setHouseNo(String houseNo) {
        this.houseNo = houseNo;
    }

    /**
     * @return 床号-废弃
     */
    public String getBedNo() {
        return bedNo;
    }

    public void setBedNo(String bedNo) {
        this.bedNo = bedNo;
    }

    /**
     * @return 租赁类型
     */
    public String getLeaseMode() {
        return leaseMode;
    }

    public void setLeaseMode(String leaseMode) {
        this.leaseMode = leaseMode;
    }

    /**
     * @return 房态状态-废弃
     */
    public String getHistoryState() {
        return historyState;
    }

    public void setHistoryState(String historyState) {
        this.historyState = historyState;
    }

    /**
     * @return 房源编号
     */
    public String getHouseCode() {
        return houseCode;
    }

    public void setHouseCode(String houseCode) {
        this.houseCode = houseCode;
    }

    /**
     * @return 居室
     */
    public String getBedRoom() {
        return bedRoom;
    }

    public void setBedRoom(String bedRoom) {
        this.bedRoom = bedRoom;
    }

    /**
     * @return 来源节点(配租登记 ， 看房 ， 选房 ， 入住)-废弃
     */
    public String getSourceNode() {
        return sourceNode;
    }

    public void setSourceNode(String sourceNode) {
        this.sourceNode = sourceNode;
    }

    /**
     * @return 定价策率编号-废弃
     */
    public String getPriceSideRatioNo() {
        return priceSideRatioNo;
    }

    public void setPriceSideRatioNo(String priceSideRatioNo) {
        this.priceSideRatioNo = priceSideRatioNo;
    }

    /**
     * @return 定价策率名称-废弃
     */
    public String getPriceSideRatioName() {
        return priceSideRatioName;
    }

    public void setPriceSideRatioName(String priceSideRatioName) {
        this.priceSideRatioName = priceSideRatioName;
    }

    /**
     * @return 月租金规则编号（工银）-废弃
     */
    public String getMonthlyRentRulesNo() {
        return monthlyRentRulesNo;
    }

    public void setMonthlyRentRulesNo(String monthlyRentRulesNo) {
        this.monthlyRentRulesNo = monthlyRentRulesNo;
    }

    /**
     * @return 月租金规则名称（工银）-废弃
     */
    public String getMonthlyRentRulesName() {
        return monthlyRentRulesName;
    }

    public void setMonthlyRentRulesName(String monthlyRentRulesName) {
        this.monthlyRentRulesName = monthlyRentRulesName;
    }

    /**
     * @return 公祖租金标准-废弃
     */
    public Double getPublicRentStandard() {
        return publicRentStandard;
    }

    public void setPublicRentStandard(Double publicRentStandard) {
        this.publicRentStandard = publicRentStandard;
    }

    /**
     * @return 市场租金标准-废弃
     */
    public Double getMarketRentStandard() {
        return marketRentStandard;
    }

    public void setMarketRentStandard(Double marketRentStandard) {
        this.marketRentStandard = marketRentStandard;
    }

    /**
     * @return 人才租金标准-废弃
     */
    public Double getTalentRentStandard() {
        return talentRentStandard;
    }

    public void setTalentRentStandard(Double talentRentStandard) {
        this.talentRentStandard = talentRentStandard;
    }

    /**
     * @return 租金单位名称（元/㎡/月、元/月）-废弃
     */
    public String getRentUnit() {
        return rentUnit;
    }

    public void setRentUnit(String rentUnit) {
        this.rentUnit = rentUnit;
    }

    /**
     * @return 规划业态名称
     */
    public String getPlannBusinessFormatName() {
        return plannBusinessFormatName;
    }

    public void setPlannBusinessFormatName(String plannBusinessFormatName) {
        this.plannBusinessFormatName = plannBusinessFormatName;
    }

    /**
     * @return 最低单价（招商方案备案底价单价），单位：元/平方米/天
     */
    public BigDecimal getLowestPrice() {
        return lowestPrice;
    }

    public void setLowestPrice(BigDecimal lowestPrice) {
        this.lowestPrice = lowestPrice;
    }

    /**
     * @return 月物业费规则编号（工银）-废弃
     */
    public String getMonthlyPropRulesNo() {
        return monthlyPropRulesNo;
    }

    public void setMonthlyPropRulesNo(String monthlyPropRulesNo) {
        this.monthlyPropRulesNo = monthlyPropRulesNo;
    }

    /**
     * @return 月物业费规则名称（工银）-废弃
     */
    public String getMonthlyPropRulesName() {
        return monthlyPropRulesName;
    }

    public void setMonthlyPropRulesName(String monthlyPropRulesName) {
        this.monthlyPropRulesName = monthlyPropRulesName;
    }

    public String getDecorateState() {
        return decorateState;
    }

    public void setDecorateState(String decorateState) {
        this.decorateState = decorateState;
    }

    public String getHousePurposeName() {
        return housePurposeName;
    }

    public void setHousePurposeName(String housePurposeName) {
        this.housePurposeName = housePurposeName;
    }

    @Override
    public String toString() {
        return "BbsRenewalProductEntity{" +
                "rpId=" + rpId +
                ", rrId=" + rrId +
                ", productName=" + productName +
                ", productNo=" + productNo +
                ", projectId=" + projectId +
                ", projectNo=" + projectNo +
                ", projectName=" + projectName +
                ", rentStandardUnit=" + rentStandardUnit +
                ", rentStandardUnitName=" + rentStandardUnitName +
                ", rentStandard=" + rentStandard +
                ", rentStandardNo=" + rentStandardNo +
                ", rentStandardName=" + rentStandardName +
                ", rent=" + rent +
                ", propStandardUnit=" + propStandardUnit +
                ", propStandardUnitName=" + propStandardUnitName +
                ", propStandard=" + propStandard +
                ", houseSelectionChannelsNo=" + houseSelectionChannelsNo +
                ", houseSelectionChannelsName=" + houseSelectionChannelsName +
                ", communityBuildingNo=" + communityBuildingNo +
                ", communityBuildingName=" + communityBuildingName +
                ", groupNo=" + groupNo +
                ", groupName=" + groupName +
                ", buildingNo=" + buildingNo +
                ", buildingName=" + buildingName +
                ", unitNo=" + unitNo +
                ", unitName=" + unitName +
                ", houseTypeNo=" + houseTypeNo +
                ", houseTypeName=" + houseTypeName +
                ", roomNo=" + roomNo +
                ", houseStructArea=" + houseStructArea +
                ", jacketedCode=" + jacketedCode +
                ", jacketed=" + jacketed +
                ", roomName=" + roomName +
                ", communityAddress=" + communityAddress +
                ", communityRegion=" + communityRegion +
                ", delFlag=" + delFlag +
                ", currentFloorNo=" + currentFloorNo +
                ", totalFloorNo=" + totalFloorNo +
                ", houseOrientation=" + houseOrientation +
                ", projectShortName=" + projectShortName +
                ", operateEntityType=" + operateEntityType +
                ", operateEntityName=" + operateEntityName +
                ", operateUnitBusinessNo=" + operateUnitBusinessNo +
                ", operateUnitNo=" + operateUnitNo +
                ", operateUnitName=" + operateUnitName +
                ", projectAreaBusinessNo=" + projectAreaBusinessNo +
                ", projectAreaNo=" + projectAreaNo +
                ", projectAreaName=" + projectAreaName +
                ", projectFormat=" + projectFormat +
                ", projectEstate=" + projectEstate +
                ", houseNoNcc=" + houseNoNcc +
                ", projectNoNcc=" + projectNoNcc +
                ", houseHireType=" + houseHireType +
                ", houseNo=" + houseNo +
                ", bedNo=" + bedNo +
                ", leaseMode=" + leaseMode +
                ", historyState=" + historyState +
                ", houseCode=" + houseCode +
                ", bedRoom=" + bedRoom +
                ", sourceNode=" + sourceNode +
                ", priceSideRatioNo=" + priceSideRatioNo +
                ", priceSideRatioName=" + priceSideRatioName +
                ", monthlyRentRulesNo=" + monthlyRentRulesNo +
                ", monthlyRentRulesName=" + monthlyRentRulesName +
                ", publicRentStandard=" + publicRentStandard +
                ", marketRentStandard=" + marketRentStandard +
                ", talentRentStandard=" + talentRentStandard +
                ", rentUnit=" + rentUnit +
                ", plannBusinessFormatName=" + plannBusinessFormatName +
                ", lowestPrice=" + lowestPrice +
                ", monthlyPropRulesNo=" + monthlyPropRulesNo +
                ", monthlyPropRulesName=" + monthlyPropRulesName +
                "}";
    }
}

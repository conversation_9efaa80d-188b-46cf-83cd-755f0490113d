package com.bonc.ioc.bzf.busisigning.feign.vo;


import com.bonc.ioc.bzf.busisigning.vo.BasePageResultVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 合同模板 实体类
 *
 * <AUTHOR>
 * @date 2023-05-04
 * @change 2023-05-04 by fem for init
 */
@Data
@ApiModel(value = "合同模板对象", description = "合同模板")
public class BbctContractTemplatePageResultVo extends BasePageResultVo implements Serializable {


    /**
     * 合同模板id
     */
    @ApiModelProperty(value = "合同模板id")
    @NotBlank(message = "合同模板id不能为空", groups = {UpdateValidatorGroup.class})
    private String contractTemplateId;

    /**
     * 模板分类
     */
    @ApiModelProperty(value = "模板分类")
    private String typeCode;

    @ApiModelProperty(value = "分类名称")
    private String typeName;

    /**
     * 合同模板名称
     */
    @ApiModelProperty(value = "合同模板名称")
    private String name;

    /**
     * 所属机构id
     */
    @ApiModelProperty(value = "所属机构id")
    private String orgId;

    /**
     * 合同模板状态（0:停用，1:启用）
     */
    @ApiModelProperty(value = "合同模板状态（0:停用，1:启用）")
    private String status;

    /**
     * 签章类型
     */
    @ApiModelProperty(value = "签章类型")
    private String signatureType;

    /**
     * 原始文件
     */
    @ApiModelProperty(value = "原始文件")
    private String fileId;

    /**
     * pdf文件
     */
    @ApiModelProperty(value = "pdf文件")
    private String pdfFileId;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String templateDescribe;

    /**
     * 删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 合同编号规则ID
     */
    @ApiModelProperty(value = "合同编号规则ID")
    private String contractModeId;

    @ApiModelProperty(value = "标签数据")
    List<BbctContractTemplateTagVo> list;

    @ApiModelProperty(value = "合同模板属性列表")
    List<BbctContractTemplateSeatInfoVo> seatInfoVoList;
}

package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 续签审批表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by l<PERSON><PERSON>g<PERSON> for init
 */
@Data
@ApiModel(value="BbsRenewalApproveInfoVo对象", description="续签审批表")
public class BbsRenewalApproveInfoVo extends BaseVo implements Serializable{


    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    private String approveId;

    /**
     * 上级id
     */
    @ApiModelProperty(value = "上级id(对应前台signId)")
    private String parentId;

    /**
     * 审批类型(1.合同审签 2.合同审核)
     */
    @ApiModelProperty(value = "审批类型(1.合同审签 2.合同审核)")
    private String approveType;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 提交人id
     */
    @ApiModelProperty(value = "提交人id", hidden = true)
    private String submitUserId;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id", hidden = true)
    private String approverUserId;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)", hidden = true)
    private Integer delFlag;

    /**
     * 意见说明
     */
    @ApiModelProperty(value = "意见说明")
    private String commentExplanation;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitUserName;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    private String approverUserName;

    /**
     * 审批描述
     */
    @ApiModelProperty(value = "审批描述")
    private String approvDesc;

}

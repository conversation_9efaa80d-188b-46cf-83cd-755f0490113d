package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2022-06-08
 * @change 2022-06-08 by ww for init
 */
@ApiModel(value = "BbSystemSigningPageVo对象", description = "")
@Data
public class BbSystemSigningPageVo extends McpBasePageVo implements Serializable {
    @ApiModelProperty(value = "计划ID")
    private String planId;
    @ApiModelProperty(value = "小区ID")
    private String communityId;
    @ApiModelProperty(value = "安排ID")
    private String arrangeId;
    @ApiModelProperty(value = "楼栋号")
    private String buildingNo;
    @ApiModelProperty(value = "单元号")
    private String unitNo;
    @ApiModelProperty(value = "证件类型")
    private String certificateType;
    @ApiModelProperty(value = "证件号码")
    private String certificateNum;
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
    @ApiModelProperty(value = "规则名称")
    private String ruleName;
    @ApiModelProperty(value = "发送状态")
    private String sendState;
    @ApiModelProperty(value = "开始时间")
    private String beginTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "签约状态")
    private String signState;
    @ApiModelProperty(value = "站内信发送状态")
    private String signNoticeState;
    @ApiModelProperty(value = "短信发送状态")
    private String messageNoticeState;
}

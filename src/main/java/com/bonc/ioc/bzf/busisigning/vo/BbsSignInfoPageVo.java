package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 签约表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-06
 * @change 2023-09-06 by l<PERSON><PERSON><PERSON><PERSON> for init
 */
@ApiModel(value = "BbsSignInfoPageVo对象", description = "签约表")
public class BbsSignInfoPageVo extends McpBasePageVo implements Serializable {


    /**
     * 签约id
     */
    @ApiModelProperty(value = "签约id")
    @NotBlank(message = "签约id不能为空", groups = {UpdateValidatorGroup.class})
    private String signId;

    /**
     * 合同费用(1.租金包含物业费 2.租金+物业费 3.仅租金（物业费由物业公司收取）
     */
    @ApiModelProperty(value = "合同费用(1.租金包含物业费 2.租金+物业费 3.仅租金（物业费由物业公司收取）")
    private String contractFees;

    /**
     * 合同模板id
     */
    @ApiModelProperty(value = "合同模板id")
    private String contractTemplateId;

    /**
     * 意向招商附件地址，多个时以逗号分隔
     */
    @ApiModelProperty(value = "意向招商附件地址，多个时以逗号分隔")
    private String contractTemplateAnnexUrls;

    /**
     * 合同模板名称
     */
    @ApiModelProperty(value = "合同模板名称")
    private String contractTemplateName;

    /**
     * 上级合同编号
     */
    @ApiModelProperty(value = "上级合同编号")
    private String parentContractCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractEndTime;

    /**
     * 合同文件地址
     */
    @ApiModelProperty(value = "合同文件地址")
    private String contractUrl;

    /**
     * 合同附件地址，多个时以逗号分隔
     */
    @ApiModelProperty(value = "合同附件地址，多个时以逗号分隔")
    private String contractAnnexUrls;

    /**
     * 合同类型(1.普通合同 2.补充合同 3.续租合同)
     */
    @ApiModelProperty(value = "合同类型(1.普通合同 2.补充合同 3.续租合同)")
    private String contractType;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 签约开始时间
     */
    @ApiModelProperty(value = "签约开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signBeginTime;

    /**
     * 签约结束时间
     */
    @ApiModelProperty(value = "签约结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signEndTime;

    /**
     * 产品来源类型(0.导入方式 1.已登记 2.已选房 3.已验房 4.选择房源 5.房态中心(实时) 6.续租中心)
     */
    @ApiModelProperty(value = "产品来源类型(0.导入方式 1.已登记 2.已选房 3.已验房 4.选择房源 5.房态中心(实时) 6.续租中心)")
    private String productSourceType;

    /**
     * 业态
     */
    @ApiModelProperty(value = "业态")
    private String businessFormat;

    /**
     * 业态名称
     */
    @ApiModelProperty(value = "业态名称")
    private String businessFormatName;

    /**
     * 缴费周期code(01.月付 02.季度付 03.半年付 04.年付,09.自定义)
     */
    @ApiModelProperty(value = "缴费周期code(01.月付 02.季度付 03.半年付 04.年付,09.自定义)")
    private String paymentCycleCode;

    /**
     * 缴费周期数值，当缴费周期code为09时有效
     */
    @ApiModelProperty(value = "缴费周期数值，当缴费周期code为09时有效")
    private Integer paymentCycleValue;

    /**
     * 押金标准code(1.1个月 2.2个月 3.3个月 9:自定义)
     */
    @ApiModelProperty(value = "押金标准code(1.1个月 2.2个月 3.3个月 9:自定义)")
    private String cashPledgeCode;

    /**
     * 押金标准数值，当押金标准code为9时有效
     */
    @ApiModelProperty(value = "押金标准数值，当押金标准code为9时有效")
    private String cashPledgeValue;

    /**
     * 租金税率，百分号前数值
     */
    @ApiModelProperty(value = "租金税率，百分号前数值")
    private Double rentTaxRate;

    /**
     * 租金免租期分类，0：无，1：区间，2：固定值
     */
    @ApiModelProperty(value = "租金免租期分类，0：无，1：区间，2：固定值")
    private String rentFreePeriodType;

    /**
     * 租金免租期固定日期,当租金免租期分类为2时有效
     */
    @ApiModelProperty(value = "租金免租期固定日期,当租金免租期分类为2时有效")
    private String rentFpFixedDate;

    /**
     * 租金免租期值（步长）,当租金免租期分类为2时有效
     */
    @ApiModelProperty(value = "租金免租期值（步长）,当租金免租期分类为2时有效")
    private Integer rentFpFixedValue;

    /**
     * 租金是否递增标识，1是0否
     */
    @ApiModelProperty(value = "租金是否递增标识，1是0否")
    private String rentIncrementalFlag;

    /**
     * 租金是否递增类型，当租金是否递增标识为1时有效
     */
    @ApiModelProperty(value = "租金是否递增类型，当租金是否递增标识为1时有效")
    private String rentIncrementalType;

    /**
     * 物业费税率，百分号前数值
     */
    @ApiModelProperty(value = "物业费税率，百分号前数值")
    private Double propTaxRate;

    /**
     * 物业费免租期分类，0：无，1：区间，2：固定值
     */
    @ApiModelProperty(value = "物业费免租期分类，0：无，1：区间，2：固定值")
    private String propFreePeriodType;

    /**
     * 物业费免租期固定日期,当租金免租期分类为2时有效
     */
    @ApiModelProperty(value = "物业费免租期固定日期,当租金免租期分类为2时有效")
    private String propFpFixedDate;

    /**
     * 物业费免租期值（步长）,当租金免租期分类为2时有效
     */
    @ApiModelProperty(value = "物业费免租期值（步长）,当租金免租期分类为2时有效")
    private Integer propFpFixedValue;

    /**
     * 物业费是否递增标识，1是0否
     */
    @ApiModelProperty(value = "物业费是否递增标识，1是0否")
    private String propIncrementalFlag;

    /**
     * 物业费是否递增类型，当租金是否递增标识为1时有效
     */
    @ApiModelProperty(value = "物业费是否递增类型，当租金是否递增标识为1时有效")
    private String propIncrementalType;

    /**
     * 签约状态(1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止)
     */
    @ApiModelProperty(value = "签约状态(1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止)")
    @NotBlank(message = "签约状态(1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止)不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String signStatus;

    /**
     * 甲方开户行
     */
    @ApiModelProperty(value = "甲方开户行")
    private String firstBankNameCode;

    /**
     * 甲方账户名称
     */
    @ApiModelProperty(value = "甲方账户名称")
    private String firstAccountName;

    /**
     * 甲方开户行名称
     */
    @ApiModelProperty(value = "甲方开户行名称")
    private String firstBankName;

    /**
     * 甲方账户id(帐号)
     */
    @ApiModelProperty(value = "甲方账户id(帐号)")
    private String firstAccountId;

    /**
     * 合同是否带水印(0.无 1.甲 2.乙)
     */
    @ApiModelProperty(value = "合同是否带水印(0.无 1.甲 2.乙)")
    private String contractWatermark;

    /**
     * 乙方水印名称
     */
    @ApiModelProperty(value = "乙方水印名称")
    private String secondWartmarkName;

    /**
     * 乙方水印logo编码
     */
    @ApiModelProperty(value = "乙方水印logo编码")
    private String secondWartmarkLogoId;

    /**
     * 乙方水印logo名称
     */
    @ApiModelProperty(value = "乙方水印logo名称")
    private String secondWartmarkLogoName;

    /**
     * 乙方水印logo地址
     */
    @ApiModelProperty(value = "乙方水印logo地址")
    private String secondWartmarkLogoUrl;

    /**
     * 是否支持先开票后付款	是否支持先开票后付款	是否支持先开票后付款(0.否 1.是)
     */
    @ApiModelProperty(value = "是否支持先开票后付款	是否支持先开票后付款	是否支持先开票后付款(0.否 1.是)")
    private String invoicingBeforePayment;

    /**
     * 系统站内信通知模板组id
     */
    @ApiModelProperty(value = "系统站内信通知模板组id")
    private String systemStationMessageTemplateGroupId;

    /**
     * 短信通知模板组id
     */
    @ApiModelProperty(value = "短信通知模板组id")
    private String mobileMessageTemplateGroupId;

    /**
     * 上传文件说明
     */
    @ApiModelProperty(value = "上传文件说明")
    private String uploadFileNote;

    /**
     * 签约办理人类型(1.本人办理 2.委托人办理)
     */
    @ApiModelProperty(value = "签约办理人类型(1.本人办理 2.委托人办理)")
    private String transactorType;

    /**
     * 是否存在关联合同(0.否 1.是)
     */
    @ApiModelProperty(value = "是否存在关联合同(0.否 1.是)")
    private String relationContract;

    /**
     * 关联合同编号
     */
    @ApiModelProperty(value = "关联合同编号")
    private String relationContractCode;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private Integer delFlag;

    /**
     * 租赁周期
     */
    @ApiModelProperty(value = "租赁周期")
    private String leaseCycle;

    /**
     * 租赁周期自定义
     */
    @ApiModelProperty(value = "租赁周期自定义")
    private String leaseCycleValue;

    /**
     * 签约办理模版组id-消息中心主键-用于编辑回显
     */
    @ApiModelProperty(value = "签约办理模版组id-消息中心主键-用于编辑回显")
    private String signTemplateGroupId;

    /**
     * 面积类型(1.建筑面积 2.套内建筑面积)
     */
    @ApiModelProperty(value = "面积类型(1.建筑面积 2.套内建筑面积)")
    private String areaType;

    public String getAreaType() {
        return areaType;
    }

    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }

    public String getSignTemplateGroupId() {
        return signTemplateGroupId;
    }

    public void setSignTemplateGroupId(String signTemplateGroupId) {
        this.signTemplateGroupId = signTemplateGroupId;
    }

    public String getLeaseCycle() {
        return leaseCycle;
    }

    public void setLeaseCycle(String leaseCycle) {
        this.leaseCycle = leaseCycle;
    }

    public String getLeaseCycleValue() {
        return leaseCycleValue;
    }

    public void setLeaseCycleValue(String leaseCycleValue) {
        this.leaseCycleValue = leaseCycleValue;
    }

    /**
     * @return 签约id
     */
    public String getSignId() {
        return signId;
    }

    public void setSignId(String signId) {
        this.signId = signId;
    }

    /**
     * @return 合同费用(1.租金包含物业费 2.租金 + 物业费 3.仅租金 （ 物业费由物业公司收取 ）
     */
    public String getContractFees() {
        return contractFees;
    }

    public void setContractFees(String contractFees) {
        this.contractFees = contractFees;
    }

    /**
     * @return 合同模板id
     */
    public String getContractTemplateId() {
        return contractTemplateId;
    }

    public void setContractTemplateId(String contractTemplateId) {
        this.contractTemplateId = contractTemplateId;
    }

    /**
     * @return 意向招商附件地址，多个时以逗号分隔
     */
    public String getContractTemplateAnnexUrls() {
        return contractTemplateAnnexUrls;
    }

    public void setContractTemplateAnnexUrls(String contractTemplateAnnexUrls) {
        this.contractTemplateAnnexUrls = contractTemplateAnnexUrls;
    }

    /**
     * @return 合同模板名称
     */
    public String getContractTemplateName() {
        return contractTemplateName;
    }

    public void setContractTemplateName(String contractTemplateName) {
        this.contractTemplateName = contractTemplateName;
    }

    /**
     * @return 上级合同编号
     */
    public String getParentContractCode() {
        return parentContractCode;
    }

    public void setParentContractCode(String parentContractCode) {
        this.parentContractCode = parentContractCode;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 合同开始时间
     */
    public Date getContractBeginTime() {
        if (contractBeginTime != null) {
            return (Date) contractBeginTime.clone();
        } else {
            return null;
        }
    }

    public void setContractBeginTime(Date contractBeginTime) {
        if (contractBeginTime == null) {
            this.contractBeginTime = null;
        } else {
            this.contractBeginTime = (Date) contractBeginTime.clone();
        }
    }

    /**
     * @return 合同结束时间
     */
    public Date getContractEndTime() {
        if (contractEndTime != null) {
            return (Date) contractEndTime.clone();
        } else {
            return null;
        }
    }

    public void setContractEndTime(Date contractEndTime) {
        if (contractEndTime == null) {
            this.contractEndTime = null;
        } else {
            this.contractEndTime = (Date) contractEndTime.clone();
        }
    }

    /**
     * @return 合同文件地址
     */
    public String getContractUrl() {
        return contractUrl;
    }

    public void setContractUrl(String contractUrl) {
        this.contractUrl = contractUrl;
    }

    /**
     * @return 合同附件地址，多个时以逗号分隔
     */
    public String getContractAnnexUrls() {
        return contractAnnexUrls;
    }

    public void setContractAnnexUrls(String contractAnnexUrls) {
        this.contractAnnexUrls = contractAnnexUrls;
    }

    /**
     * @return 合同类型(1.普通合同 2.补充合同 3.续租合同)
     */
    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    /**
     * @return 签约时间
     */
    public Date getSignTime() {
        if (signTime != null) {
            return (Date) signTime.clone();
        } else {
            return null;
        }
    }

    public void setSignTime(Date signTime) {
        if (signTime == null) {
            this.signTime = null;
        } else {
            this.signTime = (Date) signTime.clone();
        }
    }

    /**
     * @return 签约开始时间
     */
    public Date getSignBeginTime() {
        if (signBeginTime != null) {
            return (Date) signBeginTime.clone();
        } else {
            return null;
        }
    }

    public void setSignBeginTime(Date signBeginTime) {
        if (signBeginTime == null) {
            this.signBeginTime = null;
        } else {
            this.signBeginTime = (Date) signBeginTime.clone();
        }
    }

    /**
     * @return 签约结束时间
     */
    public Date getSignEndTime() {
        if (signEndTime != null) {
            return (Date) signEndTime.clone();
        } else {
            return null;
        }
    }

    public void setSignEndTime(Date signEndTime) {
        if (signEndTime == null) {
            this.signEndTime = null;
        } else {
            this.signEndTime = (Date) signEndTime.clone();
        }
    }

    /**
     * @return 产品来源类型(0.导入方式 1.已登记 2.已选房 3.已验房 4.选择房源 5.房态中心 ( 实时) 6.续租中心)
     */
    public String getProductSourceType() {
        return productSourceType;
    }

    public void setProductSourceType(String productSourceType) {
        this.productSourceType = productSourceType;
    }

    /**
     * @return 业态
     */
    public String getBusinessFormat() {
        return businessFormat;
    }

    public void setBusinessFormat(String businessFormat) {
        this.businessFormat = businessFormat;
    }

    /**
     * @return 业态名称
     */
    public String getBusinessFormatName() {
        return businessFormatName;
    }

    public void setBusinessFormatName(String businessFormatName) {
        this.businessFormatName = businessFormatName;
    }

    /**
     * @return 缴费周期code(01.月付 02.季度付 03.半年付 04.年付, 09.自定义)
     */
    public String getPaymentCycleCode() {
        return paymentCycleCode;
    }

    public void setPaymentCycleCode(String paymentCycleCode) {
        this.paymentCycleCode = paymentCycleCode;
    }

    /**
     * @return 缴费周期数值，当缴费周期code为09时有效
     */
    public Integer getPaymentCycleValue() {
        return paymentCycleValue;
    }

    public void setPaymentCycleValue(Integer paymentCycleValue) {
        this.paymentCycleValue = paymentCycleValue;
    }

    /**
     * @return 押金标准code(1.1个月 2.2个月 3.3个月 9 : 自定义)
     */
    public String getCashPledgeCode() {
        return cashPledgeCode;
    }

    public void setCashPledgeCode(String cashPledgeCode) {
        this.cashPledgeCode = cashPledgeCode;
    }

    /**
     * @return 押金标准数值，当押金标准code为9时有效
     */
    public String getCashPledgeValue() {
        return cashPledgeValue;
    }

    public void setCashPledgeValue(String cashPledgeValue) {
        this.cashPledgeValue = cashPledgeValue;
    }

    /**
     * @return 租金税率，百分号前数值
     */
    public Double getRentTaxRate() {
        return rentTaxRate;
    }

    public void setRentTaxRate(Double rentTaxRate) {
        this.rentTaxRate = rentTaxRate;
    }

    /**
     * @return 租金免租期分类，0：无，1：区间，2：固定值
     */
    public String getRentFreePeriodType() {
        return rentFreePeriodType;
    }

    public void setRentFreePeriodType(String rentFreePeriodType) {
        this.rentFreePeriodType = rentFreePeriodType;
    }

    /**
     * @return 租金免租期固定日期, 当租金免租期分类为2时有效
     */
    public String getRentFpFixedDate() {
        return rentFpFixedDate;
    }

    public void setRentFpFixedDate(String rentFpFixedDate) {
        this.rentFpFixedDate = rentFpFixedDate;
    }

    /**
     * @return 租金免租期值（步长）,当租金免租期分类为2时有效
     */
    public Integer getRentFpFixedValue() {
        return rentFpFixedValue;
    }

    public void setRentFpFixedValue(Integer rentFpFixedValue) {
        this.rentFpFixedValue = rentFpFixedValue;
    }

    /**
     * @return 租金是否递增标识，1是0否
     */
    public String getRentIncrementalFlag() {
        return rentIncrementalFlag;
    }

    public void setRentIncrementalFlag(String rentIncrementalFlag) {
        this.rentIncrementalFlag = rentIncrementalFlag;
    }

    /**
     * @return 租金是否递增类型，当租金是否递增标识为1时有效
     */
    public String getRentIncrementalType() {
        return rentIncrementalType;
    }

    public void setRentIncrementalType(String rentIncrementalType) {
        this.rentIncrementalType = rentIncrementalType;
    }

    /**
     * @return 物业费税率，百分号前数值
     */
    public Double getPropTaxRate() {
        return propTaxRate;
    }

    public void setPropTaxRate(Double propTaxRate) {
        this.propTaxRate = propTaxRate;
    }

    /**
     * @return 物业费免租期分类，0：无，1：区间，2：固定值
     */
    public String getPropFreePeriodType() {
        return propFreePeriodType;
    }

    public void setPropFreePeriodType(String propFreePeriodType) {
        this.propFreePeriodType = propFreePeriodType;
    }

    /**
     * @return 物业费免租期固定日期, 当租金免租期分类为2时有效
     */
    public String getPropFpFixedDate() {
        return propFpFixedDate;
    }

    public void setPropFpFixedDate(String propFpFixedDate) {
        this.propFpFixedDate = propFpFixedDate;
    }

    /**
     * @return 物业费免租期值（步长）,当租金免租期分类为2时有效
     */
    public Integer getPropFpFixedValue() {
        return propFpFixedValue;
    }

    public void setPropFpFixedValue(Integer propFpFixedValue) {
        this.propFpFixedValue = propFpFixedValue;
    }

    /**
     * @return 物业费是否递增标识，1是0否
     */
    public String getPropIncrementalFlag() {
        return propIncrementalFlag;
    }

    public void setPropIncrementalFlag(String propIncrementalFlag) {
        this.propIncrementalFlag = propIncrementalFlag;
    }

    /**
     * @return 物业费是否递增类型，当租金是否递增标识为1时有效
     */
    public String getPropIncrementalType() {
        return propIncrementalType;
    }

    public void setPropIncrementalType(String propIncrementalType) {
        this.propIncrementalType = propIncrementalType;
    }

    /**
     * @return 签约状态(1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止)
     */
    public String getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(String signStatus) {
        this.signStatus = signStatus;
    }

    /**
     * @return 甲方开户行
     */
    public String getFirstBankNameCode() {
        return firstBankNameCode;
    }

    public void setFirstBankNameCode(String firstBankNameCode) {
        this.firstBankNameCode = firstBankNameCode;
    }

    /**
     * @return 甲方账户名称
     */
    public String getFirstAccountName() {
        return firstAccountName;
    }

    public void setFirstAccountName(String firstAccountName) {
        this.firstAccountName = firstAccountName;
    }

    /**
     * @return 甲方开户行名称
     */
    public String getFirstBankName() {
        return firstBankName;
    }

    public void setFirstBankName(String firstBankName) {
        this.firstBankName = firstBankName;
    }

    /**
     * @return 甲方账户id(帐号)
     */
    public String getFirstAccountId() {
        return firstAccountId;
    }

    public void setFirstAccountId(String firstAccountId) {
        this.firstAccountId = firstAccountId;
    }

    /**
     * @return 合同是否带水印(0.无 1.甲 2.乙)
     */
    public String getContractWatermark() {
        return contractWatermark;
    }

    public void setContractWatermark(String contractWatermark) {
        this.contractWatermark = contractWatermark;
    }

    /**
     * @return 乙方水印名称
     */
    public String getSecondWartmarkName() {
        return secondWartmarkName;
    }

    public void setSecondWartmarkName(String secondWartmarkName) {
        this.secondWartmarkName = secondWartmarkName;
    }

    /**
     * @return 乙方水印logo编码
     */
    public String getSecondWartmarkLogoId() {
        return secondWartmarkLogoId;
    }

    public void setSecondWartmarkLogoId(String secondWartmarkLogoId) {
        this.secondWartmarkLogoId = secondWartmarkLogoId;
    }

    /**
     * @return 乙方水印logo名称
     */
    public String getSecondWartmarkLogoName() {
        return secondWartmarkLogoName;
    }

    public void setSecondWartmarkLogoName(String secondWartmarkLogoName) {
        this.secondWartmarkLogoName = secondWartmarkLogoName;
    }

    /**
     * @return 乙方水印logo地址
     */
    public String getSecondWartmarkLogoUrl() {
        return secondWartmarkLogoUrl;
    }

    public void setSecondWartmarkLogoUrl(String secondWartmarkLogoUrl) {
        this.secondWartmarkLogoUrl = secondWartmarkLogoUrl;
    }

    /**
     * @return 是否支持先开票后付款    是否支持先开票后付款	是否支持先开票后付款(0.否 1.是)
     */
    public String getInvoicingBeforePayment() {
        return invoicingBeforePayment;
    }

    public void setInvoicingBeforePayment(String invoicingBeforePayment) {
        this.invoicingBeforePayment = invoicingBeforePayment;
    }

    /**
     * @return 系统站内信通知模板组id
     */
    public String getSystemStationMessageTemplateGroupId() {
        return systemStationMessageTemplateGroupId;
    }

    public void setSystemStationMessageTemplateGroupId(String systemStationMessageTemplateGroupId) {
        this.systemStationMessageTemplateGroupId = systemStationMessageTemplateGroupId;
    }

    /**
     * @return 短信通知模板组id
     */
    public String getMobileMessageTemplateGroupId() {
        return mobileMessageTemplateGroupId;
    }

    public void setMobileMessageTemplateGroupId(String mobileMessageTemplateGroupId) {
        this.mobileMessageTemplateGroupId = mobileMessageTemplateGroupId;
    }

    /**
     * @return 上传文件说明
     */
    public String getUploadFileNote() {
        return uploadFileNote;
    }

    public void setUploadFileNote(String uploadFileNote) {
        this.uploadFileNote = uploadFileNote;
    }

    /**
     * @return 签约办理人类型(1.本人办理 2.委托人办理)
     */
    public String getTransactorType() {
        return transactorType;
    }

    public void setTransactorType(String transactorType) {
        this.transactorType = transactorType;
    }

    /**
     * @return 是否存在关联合同(0.否 1.是)
     */
    public String getRelationContract() {
        return relationContract;
    }

    public void setRelationContract(String relationContract) {
        this.relationContract = relationContract;
    }

    /**
     * @return 关联合同编号
     */
    public String getRelationContractCode() {
        return relationContractCode;
    }

    public void setRelationContractCode(String relationContractCode) {
        this.relationContractCode = relationContractCode;
    }

    /**
     * @return 删除标识(1.未删除 0.已删除)
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "BbsSignInfoPageVo{" +
                "signId=" + signId +
                ", contractFees=" + contractFees +
                ", contractTemplateId=" + contractTemplateId +
                ", contractTemplateAnnexUrls=" + contractTemplateAnnexUrls +
                ", contractTemplateName=" + contractTemplateName +
                ", parentContractCode=" + parentContractCode +
                ", contractCode=" + contractCode +
                ", contractBeginTime=" + contractBeginTime +
                ", contractEndTime=" + contractEndTime +
                ", contractUrl=" + contractUrl +
                ", contractAnnexUrls=" + contractAnnexUrls +
                ", contractType=" + contractType +
                ", signTime=" + signTime +
                ", signBeginTime=" + signBeginTime +
                ", signEndTime=" + signEndTime +
                ", productSourceType=" + productSourceType +
                ", businessFormat=" + businessFormat +
                ", businessFormatName=" + businessFormatName +
                ", paymentCycleCode=" + paymentCycleCode +
                ", paymentCycleValue=" + paymentCycleValue +
                ", cashPledgeCode=" + cashPledgeCode +
                ", cashPledgeValue=" + cashPledgeValue +
                ", rentTaxRate=" + rentTaxRate +
                ", rentFreePeriodType=" + rentFreePeriodType +
                ", rentFpFixedDate=" + rentFpFixedDate +
                ", rentFpFixedValue=" + rentFpFixedValue +
                ", rentIncrementalFlag=" + rentIncrementalFlag +
                ", rentIncrementalType=" + rentIncrementalType +
                ", propTaxRate=" + propTaxRate +
                ", propFreePeriodType=" + propFreePeriodType +
                ", propFpFixedDate=" + propFpFixedDate +
                ", propFpFixedValue=" + propFpFixedValue +
                ", propIncrementalFlag=" + propIncrementalFlag +
                ", propIncrementalType=" + propIncrementalType +
                ", signStatus=" + signStatus +
                ", firstBankNameCode=" + firstBankNameCode +
                ", firstAccountName=" + firstAccountName +
                ", firstBankName=" + firstBankName +
                ", firstAccountId=" + firstAccountId +
                ", contractWatermark=" + contractWatermark +
                ", secondWartmarkName=" + secondWartmarkName +
                ", secondWartmarkLogoId=" + secondWartmarkLogoId +
                ", secondWartmarkLogoName=" + secondWartmarkLogoName +
                ", secondWartmarkLogoUrl=" + secondWartmarkLogoUrl +
                ", invoicingBeforePayment=" + invoicingBeforePayment +
                ", systemStationMessageTemplateGroupId=" + systemStationMessageTemplateGroupId +
                ", mobileMessageTemplateGroupId=" + mobileMessageTemplateGroupId +
                ", uploadFileNote=" + uploadFileNote +
                ", transactorType=" + transactorType +
                ", relationContract=" + relationContract +
                ", relationContractCode=" + relationContractCode +
                ", delFlag=" + delFlag +
                "}";
    }
}

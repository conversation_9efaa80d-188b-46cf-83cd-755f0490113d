package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 签署合同 实体类
 *
 * <AUTHOR>
 * @date 2022-12-30
 * @change 2022-12-30 by binghong.tang for init
 */
@Data
@ApiModel(value = "BbcSignContractVo对象", description = "签署合同")
public class BbcSignContractVo implements Serializable {

    @ApiModelProperty(value = "签约id")
    @NotNull(message = "签约id不能为空")
    private String signId;

    @ApiModelProperty(value = "合同编号")
    @NotNull(message = "合同编号不能为空")
    private String contractNo;

    @ApiModelProperty(value = "base64编码的印章图片")
    @NotNull(message = "图片不能为空")
    private String imgBase;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "签约办理人类型(1.承租人本人办理 2.承租人委托人办理)")
    @NotNull(message = "签约办理人类型不能为空")
    private String transactionType;

    @ApiModelProperty(value = "项目id-法人章")
    private String projectIdFr;

}

package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>功能描述</p>
 *
 * @author: wangw
 * @date: 2022-6-11
 */
@Data

public class BbSystemSignResultVo extends McpBaseVo implements Serializable {

    private static final long serialVersionUID = -769443249346441495L;

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID")
    private String arrangeCustomerId;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    private String planId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String communityId;


    /**
     * 房源编号
     */
    @ApiModelProperty(value = "房源编号")
    private String assetId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String customerName;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @McpDictPoint(dictCode = "GENDER", overTransCopyTo = "customerGender")
    private String customerGender;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    @McpDictPoint(dictCode = "CERTIFICATE-TYPE", overTransCopyTo = "certificateType")
    private String certificateType;
    /**
     * 证件编号
     */
    @ApiModelProperty(value = "身份证号")
    private String certificateNum;
    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String selResult;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractEndTime;

    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式")
    private String payMode;

    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
    private String houseRent;

    /**
     * 签约状态
     */
    @ApiModelProperty(value = "签约状态")
    private String signState;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    private String fileId;

}

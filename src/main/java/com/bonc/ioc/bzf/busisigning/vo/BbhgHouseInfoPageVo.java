package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/6
 */
@Data
@ApiModel(value = "BbhgHouseInfoPageVo对象", description = "BbhgHouseInfoPageVo对象")
public class BbhgHouseInfoPageVo extends McpBasePageVo implements Serializable {

    @ApiModelProperty(value = "签约id")
    private String signId;

    @ApiModelProperty(value = "楼栋标识")
    private String buildCode;

    @ApiModelProperty(value = "小区标识")
    private String communityCode;

    @ApiModelProperty(value = "小区或楼宇名称")
    private String communityName;

    @ApiModelProperty(value = "区域标识")
    private String countyCode;

    @ApiModelProperty(value = "组团名称")
    private String groupName;

    @ApiModelProperty(value = "房源产品地址")
    private String houseAddress;

    @ApiModelProperty(value = "房源号")
    private String houseNum;

    @ApiModelProperty(value = "最大面积")
    private String maxBuildArea;

    @ApiModelProperty(value = "最小面积")
    private String minBuildArea;
    
    @ApiModelProperty(value = "单元标识")
    private String unitCode;
}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> 开户支行 返回实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class ChargeBankBranchResultVo {
	@ApiModelProperty(value = "分行银行编号")
	private String bankBranchCode;
	@ApiModelProperty(value = "分行银行名称")
	private  String bankBranchName;
	@ApiModelProperty(value = "银行编号")
	private  String bankCode;
	@ApiModelProperty(value = "银行名称")
	private String bankName;
	@ApiModelProperty(value = "行别代码")
	private String clsCode;


}

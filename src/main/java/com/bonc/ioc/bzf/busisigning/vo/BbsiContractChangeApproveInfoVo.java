package com.bonc.ioc.bzf.busisigning.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 合并变更表 客户信息实体类
 *
 * <AUTHOR>
 * @date 2023-08-30
 * @change 2023-08-30 by fzq for init
 */
@ApiModel(value = "BbsiContractChangeApproveInfoVo对象", description = "合并变更-审批表")
public class BbsiContractChangeApproveInfoVo extends BbsApproveInfoVo {


    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    private String approveId;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String ccId;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id", hidden = true)
    private String approverUserId;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    private String approverUserName;

    /**
     * @return 审批时间
     */
    public Date getApproveTime() {
        if (approveTime != null) {
            return (Date) approveTime.clone();
        } else {
            return null;
        }
    }

    public void setApproveTime(Date approveTime) {
        if (approveTime == null) {
            this.approveTime = null;
        } else {
            this.approveTime = (Date) approveTime.clone();
        }
    }

    @Override
    public String getApproveId() {
        return approveId;
    }

    @Override
    public void setApproveId(String approveId) {
        this.approveId = approveId;
    }

    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    @Override
    public String getApproveStatus() {
        return approveStatus;
    }

    @Override
    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    @Override
    public String getApproverUserId() {
        return approverUserId;
    }

    @Override
    public void setApproverUserId(String approverUserId) {
        this.approverUserId = approverUserId;
    }

    @Override
    public String getApproverUserName() {
        return approverUserName;
    }

    @Override
    public void setApproverUserName(String approverUserName) {
        this.approverUserName = approverUserName;
    }

    @Override
    public String toString() {
        return "BbsiContractChangeApproveInfoVo{" +
                "approveId='" + approveId + '\'' +
                ", ccId='" + ccId + '\'' +
                ", contractCode='" + contractCode + '\'' +
                ", approveStatus='" + approveStatus + '\'' +
                ", approveTime=" + approveTime +
                ", approverUserId='" + approverUserId + '\'' +
                ", approverUserName='" + approverUserName + '\'' +
                '}';
    }
}


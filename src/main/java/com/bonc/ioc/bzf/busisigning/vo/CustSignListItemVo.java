package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>功能描述</p>
 *
 * @author: wangw
 * @date: 2022-6-9
 */
@ApiModel(value = "CustSignListItemVo", description = "客户签约列表记录")
@Data
public class CustSignListItemVo extends McpBaseVo implements Serializable {

    private static final long serialVersionUID = -9072510266989685128L;

    /**
     * 签约ID
     */
    @ApiModelProperty(value = "签约ID")
    private String signId;

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
    private String customerName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String customerGender;

    /**
     * 证件编号
     */
    @ApiModelProperty(value = "证件编号")
    private String certificateNum;

    /**
     * 房源
     */
    @ApiModelProperty(value = "房源")
    private String houseSource;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
    private Date signTime;

    /**
     * 签约状态
     */
    @ApiModelProperty(value = "签约状态")
    private String signState;
}

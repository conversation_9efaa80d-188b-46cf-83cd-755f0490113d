package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 签约结果-紧急联系人中间表 实体类
 *
 * <AUTHOR>
 * @date 2023-06-13
 * @change 2023-06-13 by sx for init
 */
@ApiModel(value="BbsSigningResultEmergencyPageResultVo对象", description="签约结果-紧急联系人中间表")
public class BbsSigningResultEmergencyPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 签约结果ID
     */
    @ApiModelProperty(value = "签约结果ID")
    @NotBlank(message = "签约结果ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String resultId;

    /**
     * 紧急联系联系人ID（客户中心）
     */
    @ApiModelProperty(value = "紧急联系联系人ID（客户中心）")
                            private String emergencyContactId;

    /**
     * 删除标识(1未删除 0已删除)
     */
    @ApiModelProperty(value = "删除标识(1未删除 0已删除)")
                            private Integer delFlag;

    /**
     * 创建人名字
     */
    @ApiModelProperty(value = "创建人名字")
                            private String createUserName;

    /**
     * 修改人名字
     */
    @ApiModelProperty(value = "修改人名字")
                            private String modifyUserName;

    /**
     * @return 签约结果ID
     */
    public String getResultId() {
        return resultId;
    }

    public void setResultId(String resultId) {
        this.resultId = resultId;
    }

    /**
     * @return 紧急联系联系人ID（客户中心）
     */
    public String getEmergencyContactId() {
        return emergencyContactId;
    }

    public void setEmergencyContactId(String emergencyContactId) {
        this.emergencyContactId = emergencyContactId;
    }

    /**
     * @return 删除标识(1未删除 0已删除)
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 创建人名字
     */
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    /**
     * @return 修改人名字
     */
    public String getModifyUserName() {
        return modifyUserName;
    }

    public void setModifyUserName(String modifyUserName) {
        this.modifyUserName = modifyUserName;
    }

      @Override
    public String toString() {
        return "BbsSigningResultEmergencyPageResultVo{" +
            "resultId=" + resultId +
            ", emergencyContactId=" + emergencyContactId +
            ", delFlag=" + delFlag +
            ", createUserName=" + createUserName +
            ", modifyUserName=" + modifyUserName +
        "}";
    }
}

package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 合同变更退回银行卡信息 实体类
 *
 * <AUTHOR>
 * @date 2024-09-06
 * @change 2024-09-06 by King-Y for init
 */
@TableName("bbs_change_return_bank_card")
@ApiModel(value="BbsChangeReturnBankCardEntity对象", description="合同变更退回银行卡信息")
public class BbsChangeReturnBankCardEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_CARD_ID = "card_id";
    public static final String FIELD_CC_ID = "cc_id";
    public static final String FIELD_SOURCE_TYPE = "source_type";
    public static final String FIELD_BACK_TYPE = "back_type";
    public static final String FIELD_BANK_ACCOUNT_ID = "bank_account_id";
    public static final String FIELD_BANK_ACCOUNT_NAME = "bank_account_name";
    public static final String FIELD_BANK_CODE = "bank_code";
    public static final String FIELD_BANK_NAME = "bank_name";
    public static final String FIELD_SUB_BANK_CODE = "sub_bank_code";
    public static final String FIELD_SUB_BANK_NAME = "sub_bank_name";
    public static final String FIELD_BANK_PROVINCE_CODE = "bank_province_code";
    public static final String FIELD_BANK_PROVINCE_NAME = "bank_province_name";
    public static final String FIELD_BANK_CITY_CODE = "bank_city_code";
    public static final String FIELD_BANK_CITY_NAME = "bank_city_name";
    public static final String FIELD_CUSTOMER_ID_TYPE = "customer_id_type";
    public static final String FIELD_CUSTOMER_ID_NUMBER = "customer_id_number";
    public static final String FIELD_CHANGE_REASON = "change_reason";
    public static final String FIELD_RELATED_FILE_ID = "related_file_id";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
                                @TableId(value = "card_id", type = IdType.ASSIGN_UUID)
                                  private String cardId;

    /**
     * 合同变更表主键id
     */
    @ApiModelProperty(value = "合同变更表主键id")
                            private String ccId;

    /**
     * 01押金退回,02租金退回
     */
    @ApiModelProperty(value = "01押金退回,02租金退回")
                            private String sourceType;

    /**
     * 退回路径
     */
    @ApiModelProperty(value = "退回路径")
                            private String backType;

    /**
     * 银行卡卡号
     */
    @ApiModelProperty(value = "银行卡卡号")
                            private String bankAccountId;

    /**
     * 银行卡户名
     */
    @ApiModelProperty(value = "银行卡户名")
                            private String bankAccountName;

    /**
     * 开户行编码
     */
    @ApiModelProperty(value = "开户行编码")
                            private String bankCode;

    /**
     * 开户行名称
     */
    @ApiModelProperty(value = "开户行名称")
                            private String bankName;

    /**
     * 开户支行编码
     */
    @ApiModelProperty(value = "开户支行编码")
                            private String subBankCode;

    /**
     * 开户支行名称
     */
    @ApiModelProperty(value = "开户支行名称")
                            private String subBankName;

    /**
     * 开户行所在省编码
     */
    @ApiModelProperty(value = "开户行所在省编码")
                            private String bankProvinceCode;

    /**
     * 开户行所在省名称
     */
    @ApiModelProperty(value = "开户行所在省名称")
                            private String bankProvinceName;

    /**
     * 开户行所在市编码
     */
    @ApiModelProperty(value = "开户行所在市编码")
                            private String bankCityCode;

    /**
     * 开户行所在市名称
     */
    @ApiModelProperty(value = "开户行所在市名称")
                            private String bankCityName;

    /**
     * 开户人证件类型
     */
    @ApiModelProperty(value = "开户人证件类型")
                            private String customerIdType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
                            private String customerIdNumber;

    /**
     * 修改原因
     */
    @ApiModelProperty(value = "修改原因")
                            private String changeReason;

    /**
     * 相关材料
     */
    @ApiModelProperty(value = "相关材料")
                            private String relatedFileId;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 主键
     */
    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    /**
     * @return 合同变更表主键id
     */
    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }

    /**
     * @return 01押金退回,02租金退回
     */
    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    /**
     * @return 退回路径
     */
    public String getBackType() {
        return backType;
    }

    public void setBackType(String backType) {
        this.backType = backType;
    }

    /**
     * @return 银行卡卡号
     */
    public String getBankAccountId() {
        return bankAccountId;
    }

    public void setBankAccountId(String bankAccountId) {
        this.bankAccountId = bankAccountId;
    }

    /**
     * @return 银行卡户名
     */
    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    /**
     * @return 开户行编码
     */
    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    /**
     * @return 开户行名称
     */
    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    /**
     * @return 开户支行编码
     */
    public String getSubBankCode() {
        return subBankCode;
    }

    public void setSubBankCode(String subBankCode) {
        this.subBankCode = subBankCode;
    }

    /**
     * @return 开户支行名称
     */
    public String getSubBankName() {
        return subBankName;
    }

    public void setSubBankName(String subBankName) {
        this.subBankName = subBankName;
    }

    /**
     * @return 开户行所在省编码
     */
    public String getBankProvinceCode() {
        return bankProvinceCode;
    }

    public void setBankProvinceCode(String bankProvinceCode) {
        this.bankProvinceCode = bankProvinceCode;
    }

    /**
     * @return 开户行所在省名称
     */
    public String getBankProvinceName() {
        return bankProvinceName;
    }

    public void setBankProvinceName(String bankProvinceName) {
        this.bankProvinceName = bankProvinceName;
    }

    /**
     * @return 开户行所在市编码
     */
    public String getBankCityCode() {
        return bankCityCode;
    }

    public void setBankCityCode(String bankCityCode) {
        this.bankCityCode = bankCityCode;
    }

    /**
     * @return 开户行所在市名称
     */
    public String getBankCityName() {
        return bankCityName;
    }

    public void setBankCityName(String bankCityName) {
        this.bankCityName = bankCityName;
    }

    /**
     * @return 开户人证件类型
     */
    public String getCustomerIdType() {
        return customerIdType;
    }

    public void setCustomerIdType(String customerIdType) {
        this.customerIdType = customerIdType;
    }

    /**
     * @return 证件号码
     */
    public String getCustomerIdNumber() {
        return customerIdNumber;
    }

    public void setCustomerIdNumber(String customerIdNumber) {
        this.customerIdNumber = customerIdNumber;
    }

    /**
     * @return 修改原因
     */
    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason;
    }

    /**
     * @return 相关材料
     */
    public String getRelatedFileId() {
        return relatedFileId;
    }

    public void setRelatedFileId(String relatedFileId) {
        this.relatedFileId = relatedFileId;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsChangeReturnBankCardEntity{" +
            "cardId=" + cardId +
            ", ccId=" + ccId +
            ", sourceType=" + sourceType +
            ", backType=" + backType +
            ", bankAccountId=" + bankAccountId +
            ", bankAccountName=" + bankAccountName +
            ", bankCode=" + bankCode +
            ", bankName=" + bankName +
            ", subBankCode=" + subBankCode +
            ", subBankName=" + subBankName +
            ", bankProvinceCode=" + bankProvinceCode +
            ", bankProvinceName=" + bankProvinceName +
            ", bankCityCode=" + bankCityCode +
            ", bankCityName=" + bankCityName +
            ", customerIdType=" + customerIdType +
            ", customerIdNumber=" + customerIdNumber +
            ", changeReason=" + changeReason +
            ", relatedFileId=" + relatedFileId +
            ", delFlag=" + delFlag +
        "}";
    }
}
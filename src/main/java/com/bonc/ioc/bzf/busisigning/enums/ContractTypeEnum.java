package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 合同类型 枚举类
 *
 * <AUTHOR>
 * @since 2023/5/11
 */
public enum ContractTypeEnum {

    /**
     * 普通合同
     */
    COMMON_CONTRACT("1", "普通合同"),

    /**
     * 补充合同
     */
    REPLENISH_CONTRACT("2", "补充合同"),

    /**
     * 续租合同
     */
    RELET_CONTRACT("3", "续租合同"),

    /**
     * 商业乙方变更
     */
    LESSEE_CHANGE("19", "商业乙方变更"),

    /**
     * 其他信息变更
     */
    OTHER_CHANGE("20", "其他信息变更");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    ContractTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    ContractTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        ContractTypeEnum[] enums = values();
        for (ContractTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}

package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户消息发送日志表  实体类
 *
 * <AUTHOR>
 * @date 2022-08-08
 * @change 2022-08-08 by wtl for init
 */
@Data
@ApiModel(value = "BbsiCustSeeMessageSendLogVo", description = "客户消息发送日志表 ")
public class BbsiCustSeeMessageSendLogVo extends McpBaseVo implements Serializable {

    /**
     * 通知ID
     */
    @ApiModelProperty(value = "通知ID")
    private String noticeId;
    /**
     * 签约id
     */
    @ApiModelProperty(value = "签约id")
    private String arrangeCustomerId;

    /**
     * 通知类型 1、通知发送 2、催约通知发送
     */
    @ApiModelProperty(value = "通知类型 1、通知发送 2、催约通知发送")
    private String noticeType;

    /**
     * 通知方式 1、短信 2、站内信
     */
    @ApiModelProperty(value = "通知方式 1、短信 2、站内信")
    private String noticeMethod;

    /**
     * 通知结果 1、成功 2、失败
     */
    @ApiModelProperty(value = "通知结果 1、成功 2、失败")
    private String noticeResult;

    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
    private String noticeContent;

    @ApiModelProperty(value = "签约开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signInBeginTime;

    @ApiModelProperty(value = "签约结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signInEndTime;

    /**
     * 签约地址
     */
    @ApiModelProperty(value = "签约地址")
    private String signAddress;

    @ApiModelProperty(value = "告知书名称")
    private String templeteName;

    /**
     * 签约发送时间
     */
    @ApiModelProperty(value = "签约发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    @ApiModelProperty(value = "签约发送方式")
    @McpDictPoint(dictCode = "SIGN_SEND_METHOD", overTransCopyTo = "signSendMethod")
    private String signSendMethod;

    /**
     * 催约发送时间
     */
    @ApiModelProperty(value = "催约发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date urgeSendTime;

    @ApiModelProperty(value = "催约发送方式")
    @McpDictPoint(dictCode = "URGE_SEND_METHOD", overTransCopyTo = "urgeSendMethod")
    private String urgeSendMethod;
}

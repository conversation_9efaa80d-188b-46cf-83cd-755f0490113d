package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 审批 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/10
 */
@Data
@ApiModel(value="OrgCustomerInsertVo", description="OrgCustomerInsertVo")
public class OrgCustomerInsertVo implements Serializable {

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /**
     * 客户注册来源,字典表编码:CUSTOMER_SOURCE
     */
    @ApiModelProperty(value = "客户注册来源,字典表编码:CUSTOMER_SOURCE")
    private String customerRegisterSource;
    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalRepresentative;
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgCustomerName;
}

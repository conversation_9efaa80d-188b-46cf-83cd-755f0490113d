package com.bonc.ioc.bzf.busisigning.dao;

import com.bonc.ioc.bzf.busisigning.entity.BbsSignIncrementalConfigEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.busisigning.vo.*;
import java.util.List;

/**
 * 签约-递增设置（当租金/物业费是否递增标识为1时有效） Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-09-01
 * @change 2023-09-01 by l<PERSON><PERSON><PERSON><PERSON> for init
 */
@Mapper
public interface BbsSignIncrementalConfigMapper extends McpBaseMapper<BbsSignIncrementalConfigEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-01
     * @change 2023-09-01 by l<PERSON><PERSON><PERSON><PERSON> for init
     */
    List<BbsSignIncrementalConfigPageResultVo> selectByPageCustom(@Param("vo") BbsSignIncrementalConfigPageVo vo );
}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2022-06-08
 * @change 2022-06-08 by ww for init
 */
@ApiModel(value = "BbctContractVo对象", description = "")
public class BbctContractVo extends McpBaseVo implements Serializable {


    @NotBlank(message = "不能为空", groups = {UpdateValidatorGroup.class})
    private String contractId;

    private String contractCode;

    private String planId;

    private String communityId;

    private String projectName;

    private String area;

    private String houseResource;

    private String houseRent;

    private String signTime;

    private String payType;

    private String payMode;

    private String deposit;

    private String bankType;

    private String bankCardNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * @return
     */
    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    /**
     * @return
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return
     */
    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    /**
     * @return
     */
    public String getcommunityId() {
        return communityId;
    }

    public void setcommunityId(String communityId) {
        this.communityId = communityId;
    }

    /**
     * @return
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return
     */
    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    /**
     * @return
     */
    public String getHouseResource() {
        return houseResource;
    }

    public void setHouseResource(String houseResource) {
        this.houseResource = houseResource;
    }

    /**
     * @return
     */
    public String getHouseRent() {
        return houseRent;
    }

    public void setHouseRent(String houseRent) {
        this.houseRent = houseRent;
    }

    /**
     * @return
     */
    public String getSignTime() {
        return signTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }

    /**
     * @return
     */
    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    /**
     * @return
     */
    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    /**
     * @return
     */
    public String getDeposit() {
        return deposit;
    }

    public void setDeposit(String deposit) {
        this.deposit = deposit;
    }

    /**
     * @return
     */
    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    /**
     * @return
     */
    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    /**
     * @return
     */
    public Date getCreateDate() {
        if (createDate != null) {
            return (Date) createDate.clone();
        } else {
            return null;
        }
    }

    public void setCreateDate(Date createDate) {
        if (createDate == null) {
            this.createDate = null;
        } else {
            this.createDate = (Date) createDate.clone();
        }
    }

    @Override
    public String toString() {
        return "BbctContractVo{" +
                "contractId=" + contractId +
                ", contractCode=" + contractCode +
                ", planId=" + planId +
                ", communityId=" + communityId +
                ", projectName=" + projectName +
                ", area=" + area +
                ", houseResource=" + houseResource +
                ", houseRent=" + houseRent +
                ", signTime=" + signTime +
                ", payType=" + payType +
                ", payMode=" + payMode +
                ", deposit=" + deposit +
                ", bankType=" + bankType +
                ", bankCardNo=" + bankCardNo +
                ", createDate=" + createDate +
                "}";
    }
}

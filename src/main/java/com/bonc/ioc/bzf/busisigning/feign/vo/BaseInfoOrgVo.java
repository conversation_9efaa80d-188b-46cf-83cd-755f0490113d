package com.bonc.ioc.bzf.busisigning.feign.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * BaseInfoOrgVo
 *
 * <AUTHOR>
 * @date 2021/5/20 11:56
 * @change: 2021/5/20 11:56 by wtl for init
 */
@Data
public class BaseInfoOrgVo implements Serializable {
    private static final long serialVersionUID = -8309690769795890968L;
    /**
     * 部门Id
     */
    private String orgId;

    /**
     * 上级部门Id
     */
    private String parentOrgId;

    /**
     * 部门编码
     */
    private String orgCode;

    /**
     * 部门全称
     */
    private String orgName;

    /**
     * 部门简称
     */
    private String orgSimpleName;

    /**
     * 部门类型
     */
    private String orgType;

    /**
     * 行政区划
     */
    private String orgAreaDivision;

    /**
     * 部门行政区划级别编码
     */
    private String orgAreaDivisionLevelCode;

    /**
     * 子节点个数
     */
    private Integer orgChildNum;
}

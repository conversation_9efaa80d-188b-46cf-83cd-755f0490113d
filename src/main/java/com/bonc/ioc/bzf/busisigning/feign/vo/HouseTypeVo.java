package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel
public class HouseTypeVo {
    /**
     * 户型名称
     */
    @ApiModelProperty("户型名称")
    private String houseTypeName;
    /**
     * 最大建筑面积
     */
    @ApiModelProperty("最大建筑面积")
    /**
     *
     */
    private String floorageMax;
    /**
     * 最小建筑面积
     */
    @ApiModelProperty("最小建筑面积")
    private String floorageMin;
    /**
     * 最大使用面积
     */
    @ApiModelProperty("最大使用面积")
    private String usableAreaMax;
    /**
     * 最小使用面积
     */
    @ApiModelProperty("最小使用面积")
    private String usableAreaMin;
    /**
     * 朝向
     */
    @ApiModelProperty("朝向")
    private String orientation;
}

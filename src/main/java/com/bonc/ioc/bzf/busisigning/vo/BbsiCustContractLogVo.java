package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2022-08-18
 * @change 2022-08-18 by wtl for init
 */
@Data
@ApiModel(value = "BbsiCustContractLogVo对象", description = "")
public class BbsiCustContractLogVo extends McpBaseVo implements Serializable {


    /**
     * 导入ID
     */
    @ApiModelProperty(value = "导入ID")
    @NotBlank(message = "导入ID不能为空", groups = {UpdateValidatorGroup.class})
    private String importId;

    /**
     * 文件名称(合同编码)
     */
    @ApiModelProperty(value = "文件名称(合同编码)")
    @NotBlank(message = "文件名称(合同编码)不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String fileName;

    /**
     * 签约id
     */
    @ApiModelProperty(value = "签约id")
    private String arrangeCustomerId;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private String planId;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id")
    private String communityId;

    /**
     * 文件id(上传文件服务器返回id)
     */
    @ApiModelProperty(value = "文件id(上传文件服务器返回id)")
    private Integer fileId;

    /**
     * 解密密钥
     */
    @ApiModelProperty(value = "解密密钥")
    private String secretKey;

    /**
     * 导入文件类型(1.docx2.pdf 3.rar 4zip)
     */
    @ApiModelProperty(value = "导入文件类型(1.docx2.pdf 3.rar 4zip)")
    private String fileType;

    /**
     * 1.成功 2.失败
     */
    @ApiModelProperty(value = "1.成功 2.失败")
    private String importState;


    @Override
    public String toString() {
        return "BbsiCustContractLogVo{" +
                "importId=" + importId +
                ", fileName=" + fileName +
                ", arrangeCustomerId=" + arrangeCustomerId +
                ", planId=" + planId +
                ", communityId=" + communityId +
                ", fileId=" + fileId +
                ", secretKey=" + secretKey +
                ", fileType=" + fileType +
                ", importState=" + importState +
                "}";
    }
}

package com.bonc.ioc.bzf.busisigning.vo.tz;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jruby.RubyBoolean;

import java.util.Date;

@Data
public class TzParamVo {

    @ExcelProperty(index = 0)
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ExcelProperty(index = 1)
    @ApiModelProperty(value = "合同费用(02-07.租金包含物业费 02-08.租金+物业费 02-09.仅租金（物业费由物业公司收取）")
    private String contractFees;

    @ExcelProperty(index = 2)
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ExcelProperty(index = 3)
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;


    /**
     * 缴费周期code(01.月付 02.季度付 03.半年付 04.年付,09.自定义)
     */
    @ApiModelProperty(value = "缴费周期code(01.月付 02.季度付 03.半年付 04.年付,09.自定义)")
    private String paymentCycleCode;

    @ApiModelProperty(value = "缴费周期")
    @ExcelProperty(index = 4)
    private String paymentCycleName;

    /**
     * 业态名称
     */
    @ApiModelProperty(value = "业态名称")
    @ExcelProperty(index = 5)
    private String businessFormatName;

    @ApiModelProperty(value = "自定义押金")
    @ExcelProperty(index = 6)
    private Double cachePleadgeValue;

    @ApiModelProperty(value = "商铺地址")
    @ExcelProperty(index = 7)
    private String productName;

    @ApiModelProperty(value = "房源产品编号")
    @ExcelProperty(index = 8)
    private String productNo;

    @ApiModelProperty(value = "租金标准单位名称")
    @ExcelProperty(index = 9)
    private String rentStandardUnitName;

    @ApiModelProperty(value = "租金标准")
    @ExcelProperty(index = 10)
    private Double rentStandard;

    @ApiModelProperty(value = "物业费标准单位名称")
    @ExcelProperty(index = 11)
    private String propStandardUnitName;

    @ApiModelProperty(value = "物业费标准")
    @ExcelProperty(index = 12)
    private Double propStandard;

    /**
     * 调节点名称
     */
    @ApiModelProperty(value = "调节点名称")
    @ExcelProperty(index = 13)
    private String adjustmentPointName;

    /**
     * 时间点
     */
    @ApiModelProperty(value = "时间点")
    @ExcelProperty(index = 14)
    private Integer timePoint;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @ExcelProperty(index = 15)
    private String unitName;

    /**
     * 涨幅，百分号前数值
     */
    @ApiModelProperty(value = "涨幅，百分号前数值")
    @ExcelProperty(index = 16)
    private Double increase;


    /**
     * 调节点名称
     */
    @ApiModelProperty(value = "调节点名称")
    @ExcelProperty(index = 17)
    private String propAdjustmentPointName;

    /**
     * 时间点
     */
    @ApiModelProperty(value = "时间点")
    @ExcelProperty(index = 18)
    private Integer propTimePoint;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @ExcelProperty(index = 19)
    private String propUnitName;

    /**
     * 涨幅，百分号前数值
     */
    @ApiModelProperty(value = "涨幅，百分号前数值")
    @ExcelProperty(index = 20)
    private Double propIncrease;




    @ApiModelProperty(value = "租金免租期")
    @ExcelProperty(index = 21)
    private String freeRent;

    @ApiModelProperty(value = "物业费免租期")
    @ExcelProperty(index = 22)
    private String freeProp;


    /**
     * 甲方开户行
     */
    @ApiModelProperty(value = "甲方总行编号")
    @ExcelProperty(index = 23)
    private String firstBankNameCode;

    /**
     * 甲方开户行名称
     */
    @ApiModelProperty(value = "甲方开户行名称")
    @ExcelProperty(index = 24)
    private String firstBankName;

    /**
     * 甲方账户名称
     */
//    @ApiModelProperty(value = "甲方账户名称")
//    @ExcelProperty(index = 25)
//    private String firstAccountName;



    /**
     * 甲方账户id(帐号)
     */
    @ApiModelProperty(value = "甲方账户id(帐号)")
    @ExcelProperty(index = 26)
    private String firstAccountId;




    @ExcelProperty(index = 27)
    @ApiModelProperty(value = "产权机构名称（出租（单位）名称）")
    private String firstName;

    @ExcelProperty(index = 28)
    @ApiModelProperty(value = "出租单位统一信用代码")
    private String unifiedCreditCode;


    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(index = 29)
    private String projectName;


    @ApiModelProperty(value = "租赁方类型")
    @ExcelProperty(index = 30)
    private String customerType;


    @ApiModelProperty(value = "租赁方类型")
    @ExcelProperty(index = 31)
    private String customerName;

    @ExcelProperty(index = 32)
    @ApiModelProperty(value = "证件号码")
    private String customerNumberCode;

    @ExcelProperty(index = 33)
    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ExcelProperty(index = 34)
    @ApiModelProperty(value = "联系人电话")
    private String contactTel;

    @ExcelProperty(index = 35)
    private String customerBankCode;

    @ExcelProperty(index = 36)
    private String customerAccountName;

    @ExcelProperty(index = 37)
    private String customerBankName;

    @ExcelProperty(index = 38)
    private String customerAccountId;


    @ApiModelProperty(value = "面积类型(1.建筑面积 2.套内建筑面积)")
    private String areaType="1";


    @ApiModelProperty(value = "是否是台账")
    private Boolean isTz = true;

    @ApiModelProperty(value = "客户ID")
    private String customerNo;

    @ApiModelProperty(value = "个人客户证件类型")
    private String customerIdType = "01";//默认身份证

    @ApiModelProperty(value = "房源数据来源 1：商业（意向登记），2：房态）")
    private String sourceNode = "2";

    @ApiModelProperty(value = "1:台账，2：其他）")
    private String type;
}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 项目经过的中心实体类
 *
 * <AUTHOR>
 * @date 2022-06-09
 * @change 2022-06-09 by ya<PERSON><PERSON><PERSON> for init
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ProjectPassCenterVo对象", description = "项目经过的中心信息")
public class ProjectPassCenterVo extends McpBaseVo implements Serializable {

    private static final long serialVersionUID = 856473565297345907L;


    /**
     * 主键
     */
    private String projectPassCenterId;


    /**
     * 中心ID
     */
    private String centerId;

    /**
     * 小区ID
     */
    private String communityId;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 计划ID
     */
    private String planId;

    /**
     * 计划名
     */
    private String planName;


}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 合同模板占位符表 实体类
 *
 * <AUTHOR>
 * @date 2022-12-04
 * @change 2022-12-04 by 宋鑫 for init
 */
@ApiModel(value="BbctContractSeatPageResultVo对象", description="合同模板占位符表")
public class BbctContractSeatPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 占位符ID
     */
    @ApiModelProperty(value = "占位符ID")
    @NotBlank(message = "占位符ID不能为空",groups = {UpdateValidatorGroup.class})
    private String seatId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String seatTitle;

    /**
     * 占位符分类（string,image）
     */
    @ApiModelProperty(value = "占位符分类（string,image）")
    private String seatType;

    /**
     * 占位符KEY
     */
    @ApiModelProperty(value = "占位符KEY")
    private String seatKey;

    /**
     * 对应合同字段
     */
    @ApiModelProperty(value = "对应合同字段")
    private String fieldName;

    /**
     * 默认值
     */
    @ApiModelProperty(value = "默认值")
    private String defaultVal;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String remark;



    /**
     * @return 标题
     */
    public String getSeatTitle() {
        return seatTitle;
    }

    public void setSeatTitle(String seatTitle) {
        this.seatTitle = seatTitle;
    }

    /**
     * @return 占位符分类（string,image）
     */
    public String getSeatType() {
        return seatType;
    }

    public void setSeatType(String seatType) {
        this.seatType = seatType;
    }

    /**
     * @return 占位符KEY
     */
    public String getSeatKey() {
        return seatKey;
    }

    public void setSeatKey(String seatKey) {
        this.seatKey = seatKey;
    }

    /**
     * @return 对应合同字段
     */
    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    /**
     * @return 默认值
     */
    public String getDefaultVal() {
        return defaultVal;
    }

    public void setDefaultVal(String defaultVal) {
        this.defaultVal = defaultVal;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 描述
     */
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSeatId() {
        return seatId;
    }

    public void setSeatId(String seatId) {
        this.seatId = seatId;
    }

    @Override
    public String toString() {
        return "BbctContractSeatPageResultVo{" +
            "seatId=" + seatId +
            ", seatTitle=" + seatTitle +
            ", seatType=" + seatType +
            ", seatKey=" + seatKey +
            ", fieldName=" + fieldName +
            ", defaultVal=" + defaultVal +
            ", delFlag=" + delFlag +
            ", remark=" + remark +
        "}";
    }
}

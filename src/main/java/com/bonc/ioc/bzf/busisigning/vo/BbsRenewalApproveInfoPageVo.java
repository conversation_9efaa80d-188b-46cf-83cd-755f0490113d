package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 续签审批表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by <PERSON><PERSON><PERSON><PERSON><PERSON> for init
 */
@Data
@ApiModel(value="BbsRenewalApproveInfoPageVo对象", description="续签审批表")
public class BbsRenewalApproveInfoPageVo extends McpBasePageVo implements Serializable{


    /**
     * 审批类型(1.合同审签 2.合同审核)
     */
    @ApiModelProperty(value = "审批类型(1.合同审签 2.合同审核)",hidden = true)
    private String approveType;
    /**
     * 页面类型(1.待办页面,2已办页面)
     */
    @ApiModelProperty(value = "页面类型(1.待办页面,2已办页面)",hidden = true)
    private String pageStatus;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)",hidden = true)
    private String approveStatus;

    /**
     * 小区名称 (模糊搜索)
     */
    @ApiModelProperty(value = "小区名称")
    private String communityBuildingName;
    /**
     * 租户（模糊搜索、按租户姓名或单位名称)
     */
    @ApiModelProperty(value = "租户")
    private String customerName;
    /**
     * 商铺地址（级联搜索）请输入
     */
    @ApiModelProperty(value = "商铺地址-请输入")
    private String productName;
    /**
     * 小区（级联搜索）
     */
    @ApiModelProperty(value = "小区")
    private String communityName;
    /**
     * 坐落（级联搜索）
     */
    @ApiModelProperty(value = "坐落")
    private String groupName;
    /**
     * 楼号（级联搜索）
     */
    @ApiModelProperty(value = "楼号")
    private String buildingNo;
    /**
     * 单元（级联搜索）
     */
    @ApiModelProperty("单元")
    private String unitName;
    /**
     * 联系电话（精确搜索）
     */
    @ApiModelProperty(value = "联系电话")
    private String customerTel;

    /**
     * 业态(01.餐饮 02.生活服务)
     */
    @ApiModelProperty(value = "业态(01.餐饮 02.生活服务)")
    private String businessFormat;

    /**
     * 业态名称
     */
    @ApiModelProperty(value = "业态名称")
    private String businessFormatName;

    /**
     * 租户性质（企业、个人）
     */
    @ApiModelProperty(value = "租户性质")
    private String customerType;
    /**
     * 合同编号（精确搜索）
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;
    /**
     * 提交/申请人（模糊搜索）
     */
    @ApiModelProperty(value = "提交/申请人姓名")
    private String submitUserName;
    /**
     * 审核人（模糊搜索）
     */
    @ApiModelProperty(value = "审核人姓名")
    private String approverUserName;
    /**
     * 审核时间（可以取时间范围内，或者具体某一天）
     */
    @ApiModelProperty(value = "审核时间-开始")
    private String approveStartTime;
    /**
     * 审核时间（可以取时间范围内，或者具体某一天）
     */
    @ApiModelProperty(value = "审核时间-结束")
    private String approveEndTime;
    /**
     * 提交/申请时间（可以取时间范围内，或者具体某一天）
     */
    @ApiModelProperty(value = "提交/申请时间-开始")
    private String submitStartTime;
    /**
     * 提交/申请时间（可以取时间范围内，或者具体某一天）
     */
    @ApiModelProperty(value = "提交/申请时间-结束")
    private String submitEndTime;

}

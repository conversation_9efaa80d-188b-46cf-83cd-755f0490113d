package com.bonc.ioc.bzf.busisigning.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/16
 */
@Data
@ApiModel(value = "ContractingProgressVo", description = "签约进度实体")
public class ContractingProgressVo {

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "联系人")
    private String ContactPerson;

    @ApiModelProperty(value = "联系电话")
    private String ContactPhone;

    @ApiModelProperty(value = "承租人")
    private String tenantry;

    @ApiModelProperty(value = "证件号码")
    private String certificateNumber;

    @ApiModelProperty(value = "客户类型 00:个人  01：企业")
    private String customerType;

    @ApiModelProperty(value = "签约流程时间节点")
    private List<BscIntentionTimeNodeVo> bscIntentionTimeNodeVos;

}

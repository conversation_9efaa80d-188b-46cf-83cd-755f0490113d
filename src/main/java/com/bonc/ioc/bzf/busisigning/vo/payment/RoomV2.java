package com.bonc.ioc.bzf.busisigning.vo.payment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * roomList	商铺信息
 */
@Data
public class RoomV2 implements Serializable {

    @ApiModelProperty(value = "商铺id")
    private String houseId;	//商铺ID	 String	是 (如果是新的houseId,需要传商铺相关信息，具体参数与商业签约一致)

    @ApiModelProperty(value = "计费科目列表")
    private List<ChargeSubject> chargeSubjectList;  //计费科目列表 是 递增、免租等规则不变就传原来的，变了就传新的

}

package com.bonc.ioc.bzf.busisigning.vo.adjust;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BbpmReceivableAdjustContractPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 调整单编号
     */
    @ApiModelProperty(value = "调整单编号")
                            private String contractId;

    /**
     * 产品地址
     */
    @ApiModelProperty(value = "产品地址")
                            private String productAddress;

    /**
     * 小区名称
     */
    @ApiModelProperty(value = "小区名称")
    private String communityBuildingName;


    /**
     * 承租人姓名
     */
    @ApiModelProperty(value = "承租人姓名")
                            private String customerName;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    @McpDictPoint(dictCode = "CERTIFICATE_TYPE", overTransCopyTo = "customerIdTypeName")
                            private String customerIdType;


    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型名称")
    private String customerIdTypeName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
                            private String customerIdNumber;

    /**
     * 趸租单位
     */
    @ApiModelProperty(value = "趸租单位")
                            private String company;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
                            private String customerCreditCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractNo;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @McpDictPoint(dictCode = "BUSINESS_TYPE_CODE2", overTransCopyTo = "contractTypeName")
                            private String contractType;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型名称")
    private String contractTypeName;

    /**
     * 签约房源套数
     */
    @ApiModelProperty(value = "签约房源套数")
    private String qyts;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date contractBeginTime;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date contractEndTime;

    /**
     * 缴费周期
     */
    @ApiModelProperty(value = "缴费周期")
    @McpDictPoint(dictCode = "PAYMENT_CYCLE", overTransCopyTo = "paymentCycleCodeName")
    private String paymentCycleCode;

    /**
     * 缴费周期
     */
    @ApiModelProperty(value = "缴费周期名称")
    private String paymentCycleCodeName;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    @ApiModelProperty(value = "开户人姓名")
    private String accountHolder;
    @ApiModelProperty(value = "银行卡号")
    private String bankCard;
    @ApiModelProperty(value = "开户行名称")
    private String openingBankName;
    @ApiModelProperty(value = "开户行编码")
    private String openingBank;
    @ApiModelProperty(value = "开户行网点名称")
    private String bankBranchName;
    @ApiModelProperty(value = "开户行网点行号")
    private String bankBranchCode;
    @ApiModelProperty(value = "开户行所在省市：例（{'proCode':'1100000000000000000','cityName':'北京市','cityCode':'1101000000000000000','proName':'北京'}）")
    private String customerExtend;
    @ApiModelProperty(value = "企业开户人姓名")
    private String qyAccountHolder;
    @ApiModelProperty(value = "企业银行卡号")
    private String qyBankCard;
    @ApiModelProperty(value = "企业开户行名称")
    private String qyOpeningBankName;
    @ApiModelProperty(value = "企业开户行编码")
    private String qyOpeningBank;
    @ApiModelProperty(value = "企业开户行网点名称")
    private String qyBankBranchName;
    @ApiModelProperty(value = "企业开户行网点行号")
    private String qyBankBranchCode;
    @ApiModelProperty(value = "企业开户行所在省市：例（{'proCode':'1100000000000000000','cityName':'北京市','cityCode':'1101000000000000000','proName':'北京'}）")
    private String qyCustomerExtend;

    @ApiModelProperty(value = "产品类型")
    private String productType;

    @ApiModelProperty(value = "合同文件id")
    private String newPdfFileId;
}

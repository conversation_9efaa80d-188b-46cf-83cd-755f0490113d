package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 人-产品：客户表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-05
 * @change 2023-09-05 by l<PERSON><PERSON><PERSON><PERSON> for init
 */
@ApiModel(value = "BbsResultCustomerPageVo对象", description = "人-产品：客户表")
public class BbsResultCustomerPageVo extends McpBasePageVo implements Serializable {


    @NotBlank(message = "不能为空", groups = {UpdateValidatorGroup.class})
    private String rcId;

    /**
     * 人房关系中间表（弃用）
     */
    @ApiModelProperty(value = "人房关系中间表（弃用）")
    private String relationId;

    /**
     * 人-产品关系外键
     */
    @ApiModelProperty(value = "人-产品关系外键")
    private String rrId;

    /**
     * 租户方：乙、丙、丁（弃用）
     */
    @ApiModelProperty(value = "租户方：乙、丙、丁（弃用）")
    private String type;

    /**
     * 散户或者企业编号
     */
    @ApiModelProperty(value = "散户或者企业编号")
    private String customerNo;

    /**
     * 散户或者企业名称
     */
    @ApiModelProperty(value = "散户或者企业名称")
    private String customerName;

    /**
     * 00:个人  01：企业
     */
    @ApiModelProperty(value = "00:个人  01：企业")
    private String customerType;

    /**
     * 租户客商编号
     */
    @ApiModelProperty(value = "租户客商编号")
    private String customerSupplierNo;

    /**
     * 缴费占比（0-1）
     */
    @ApiModelProperty(value = "缴费占比（0-1）")
    private Float customerRatio;

    /**
     * 社会统一信息用代码(企业使用)
     */
    @ApiModelProperty(value = "社会统一信息用代码(企业使用)")
    private String customerCreditCode;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String customerIdType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String customerGender;

    /**
     * 单位电话
     */
    @ApiModelProperty(value = "单位电话")
    private String customerWorkTel;

    /**
     * 租户电话
     */
    @ApiModelProperty(value = "租户电话")
    private String customerTel;

    /**
     * 公租房备案号（弃用）
     */
    @ApiModelProperty(value = "公租房备案号（弃用）")
    private String customerPublicRecordNo;

    /**
     * 源住址
     */
    @ApiModelProperty(value = "源住址")
    private String customerHouseAddress;

    /**
     * 开户银行编码
     */
    @ApiModelProperty(value = "开户银行编码")
    private String bankNameCode;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    private String bankName;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
    private String bankCard;

    /**
     * 开户行手机号
     */
    @ApiModelProperty(value = "开户行手机号")
    private String bankPhone;

    /**
     * 是否鉴权（1 是0 否）（弃用）
     */
    @ApiModelProperty(value = "是否鉴权（1 是0 否）（弃用）")
    private String bankIsAuthentication;

    /**
     * 是否签署代扣代缴协议（1 是0 否）（弃用）
     */
    @ApiModelProperty(value = "是否签署代扣代缴协议（1 是0 否）（弃用）")
    private String bankIsAgreement;

    /**
     * 协议编号（弃用）
     */
    @ApiModelProperty(value = "协议编号（弃用）")
    private String bankAgreementNo;

    /**
     * 签署类型（1 手机号开通 2 线下开通）（弃用）
     */
    @ApiModelProperty(value = "签署类型（1 手机号开通 2 线下开通）（弃用）")
    private String bankAgreementType;

    /**
     * 支行编码
     */
    @ApiModelProperty(value = "支行编码")
    private String bankSubbranchCode;

    /**
     * 支行名称
     */
    @ApiModelProperty(value = "支行名称")
    private String bankSubbranchName;

    /**
     * 银行分类代码
     */
    @ApiModelProperty(value = "银行分类代码")
    private String bankNccCategoryCode;

    /**
     * 开户名称
     */
    @ApiModelProperty(value = "开户名称")
    private String bankUserName;

    /**
     * 开通代扣代缴服务资料地址（弃用）
     */
    @ApiModelProperty(value = "开通代扣代缴服务资料地址（弃用）")
    private String bankDataUrls;

    /**
     * 电子邮箱地址
     */
    @ApiModelProperty(value = "电子邮箱地址")
    private String emailAddress;

    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
    private String mailAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalName;

    /**
     * 法定代表人联系电话
     */
    @ApiModelProperty(value = "法定代表人联系电话")
    private String legalMobile;

    /**
     * 委托代理人姓名
     */
    @ApiModelProperty(value = "委托代理人姓名")
    private String consignorName;

    /**
     * 委托代理人联系电话
     */
    @ApiModelProperty(value = "委托代理人联系电话")
    private String consignorMobile;

    /**
     * 委托人与签约方关系(1.朋友 2.亲属)
     */
    @ApiModelProperty(value = "委托人与签约方关系(1.朋友 2.亲属)")
    private String consignorSignatoryRelationType;

    /**
     * 委托人证件类型(1.身份证 2.户口本)
     */
    @ApiModelProperty(value = "委托人证件类型(1.身份证 2.户口本)")
    private String consignorCertificationType;

    /**
     * 委托人证件号码
     */
    @ApiModelProperty(value = "委托人证件号码")
    private String consignorIdvoCard;

    /**
     * 委托人资料地址
     */
    @ApiModelProperty(value = "委托人资料地址")
    private String consignorDataUrls;

    /**
     * 委托人照片地址
     */
    @ApiModelProperty(value = "委托人照片地址")
    private String consignorIdvoCardPhotoUrl;

    /**
     * 办理人照片地址
     */
    @ApiModelProperty(value = "办理人照片地址")
    private String transactorIdvoCardPhotoUrl;

    /**
     * 委托人身份证正面照片（弃用）
     */
    @ApiModelProperty(value = "委托人身份证正面照片（弃用）")
    private String consignorIdvoCardFrontPhotoUrl;

    /**
     * 委托人身份证背面面照片（弃用）
     */
    @ApiModelProperty(value = "委托人身份证背面面照片（弃用）")
    private String consignorIdvoCardBackPhotoUrl;

    /**
     * 办理人身份证正面照片（弃用）
     */
    @ApiModelProperty(value = "办理人身份证正面照片（弃用）")
    private String transactorIdvoCardFrontPhotoUrl;

    /**
     * 办理人身份证背面照片（弃用）
     */
    @ApiModelProperty(value = "办理人身份证背面照片（弃用）")
    private String transactorIdvoCardBackPhotoUrl;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @NotNull(message = "是否删除不能为空", groups = {UpdateValidatorGroup.class})
    private Integer delFlag;

    /**
     * 租户客户编号
     */
    @ApiModelProperty(value = "租户客户编号")
    private String tenantCustomerNo;

    /**
     * 租户客商编号
     */
    @ApiModelProperty(value = "租户客商编号")
    private String tenantSupplierNo;

    /**
     * 租户客商名称
     */
    @ApiModelProperty(value = "租户客商名称")
    private String tenantSupplierName;

    /**
     * 企业客商编号
     */
    @ApiModelProperty(value = "企业客商编号")
    private String companySupplierNo;

    /**
     * 企业客商名称
     */
    @ApiModelProperty(value = "企业客商名称")
    private String companySupplierName;

    /**
     * 企业证照类型
     */
    @ApiModelProperty(value = "企业证照类型")
    private String companyIdType;

    /**
     * 缴费金额
     */
    @ApiModelProperty(value = "缴费金额")
    private String customerPaymentAmount;

    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    private String customerNationality;

    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 申请业态
     */
    @ApiModelProperty(value = "申请业态")
    private String applyBusinessType;

    /**
     * 申请业态名称
     */
    @ApiModelProperty(value = "申请业态名称")
    private String applyBusinessTypeName;

    /**
     * 意向登记主键id
     */
    @ApiModelProperty(value = "意向登记主键id")
    private String intentionId;

    /**
     * @return
     */
    public String getRcId() {
        return rcId;
    }

    public void setRcId(String rcId) {
        this.rcId = rcId;
    }

    /**
     * @return 人房关系中间表（弃用）
     */
    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    /**
     * @return 人-产品关系外键
     */
    public String getRrId() {
        return rrId;
    }

    public void setRrId(String rrId) {
        this.rrId = rrId;
    }

    /**
     * @return 租户方：乙、丙、丁（弃用）
     */
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return 散户或者企业编号
     */
    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    /**
     * @return 散户或者企业名称
     */
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * @return 00:个人  01：企业
     */
    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    /**
     * @return 租户客商编号
     */
    public String getCustomerSupplierNo() {
        return customerSupplierNo;
    }

    public void setCustomerSupplierNo(String customerSupplierNo) {
        this.customerSupplierNo = customerSupplierNo;
    }

    /**
     * @return 缴费占比（0-1）
     */
    public Float getCustomerRatio() {
        return customerRatio;
    }

    public void setCustomerRatio(Float customerRatio) {
        this.customerRatio = customerRatio;
    }

    /**
     * @return 社会统一信息用代码(企业使用)
     */
    public String getCustomerCreditCode() {
        return customerCreditCode;
    }

    public void setCustomerCreditCode(String customerCreditCode) {
        this.customerCreditCode = customerCreditCode;
    }

    /**
     * @return 证件类型
     */
    public String getCustomerIdType() {
        return customerIdType;
    }

    public void setCustomerIdType(String customerIdType) {
        this.customerIdType = customerIdType;
    }

    /**
     * @return 证件号码
     */
    public String getCustomerIdNumber() {
        return customerIdNumber;
    }

    public void setCustomerIdNumber(String customerIdNumber) {
        this.customerIdNumber = customerIdNumber;
    }

    /**
     * @return 性别
     */
    public String getCustomerGender() {
        return customerGender;
    }

    public void setCustomerGender(String customerGender) {
        this.customerGender = customerGender;
    }

    /**
     * @return 单位电话
     */
    public String getCustomerWorkTel() {
        return customerWorkTel;
    }

    public void setCustomerWorkTel(String customerWorkTel) {
        this.customerWorkTel = customerWorkTel;
    }

    /**
     * @return 租户电话
     */
    public String getCustomerTel() {
        return customerTel;
    }

    public void setCustomerTel(String customerTel) {
        this.customerTel = customerTel;
    }

    /**
     * @return 公租房备案号（弃用）
     */
    public String getCustomerPublicRecordNo() {
        return customerPublicRecordNo;
    }

    public void setCustomerPublicRecordNo(String customerPublicRecordNo) {
        this.customerPublicRecordNo = customerPublicRecordNo;
    }

    /**
     * @return 源住址
     */
    public String getCustomerHouseAddress() {
        return customerHouseAddress;
    }

    public void setCustomerHouseAddress(String customerHouseAddress) {
        this.customerHouseAddress = customerHouseAddress;
    }

    /**
     * @return 开户银行编码
     */
    public String getBankNameCode() {
        return bankNameCode;
    }

    public void setBankNameCode(String bankNameCode) {
        this.bankNameCode = bankNameCode;
    }

    /**
     * @return 开户银行
     */
    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    /**
     * @return 银行卡号
     */
    public String getBankCard() {
        return bankCard;
    }

    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    /**
     * @return 开户行手机号
     */
    public String getBankPhone() {
        return bankPhone;
    }

    public void setBankPhone(String bankPhone) {
        this.bankPhone = bankPhone;
    }

    /**
     * @return 是否鉴权（1 是0 否）（弃用）
     */
    public String getBankIsAuthentication() {
        return bankIsAuthentication;
    }

    public void setBankIsAuthentication(String bankIsAuthentication) {
        this.bankIsAuthentication = bankIsAuthentication;
    }

    /**
     * @return 是否签署代扣代缴协议（1 是0 否）（弃用）
     */
    public String getBankIsAgreement() {
        return bankIsAgreement;
    }

    public void setBankIsAgreement(String bankIsAgreement) {
        this.bankIsAgreement = bankIsAgreement;
    }

    /**
     * @return 协议编号（弃用）
     */
    public String getBankAgreementNo() {
        return bankAgreementNo;
    }

    public void setBankAgreementNo(String bankAgreementNo) {
        this.bankAgreementNo = bankAgreementNo;
    }

    /**
     * @return 签署类型（1 手机号开通 2 线下开通）（弃用）
     */
    public String getBankAgreementType() {
        return bankAgreementType;
    }

    public void setBankAgreementType(String bankAgreementType) {
        this.bankAgreementType = bankAgreementType;
    }

    /**
     * @return 支行编码
     */
    public String getBankSubbranchCode() {
        return bankSubbranchCode;
    }

    public void setBankSubbranchCode(String bankSubbranchCode) {
        this.bankSubbranchCode = bankSubbranchCode;
    }

    /**
     * @return 支行名称
     */
    public String getBankSubbranchName() {
        return bankSubbranchName;
    }

    public void setBankSubbranchName(String bankSubbranchName) {
        this.bankSubbranchName = bankSubbranchName;
    }

    /**
     * @return 银行分类代码
     */
    public String getBankNccCategoryCode() {
        return bankNccCategoryCode;
    }

    public void setBankNccCategoryCode(String bankNccCategoryCode) {
        this.bankNccCategoryCode = bankNccCategoryCode;
    }

    /**
     * @return 开户名称
     */
    public String getBankUserName() {
        return bankUserName;
    }

    public void setBankUserName(String bankUserName) {
        this.bankUserName = bankUserName;
    }

    /**
     * @return 开通代扣代缴服务资料地址（弃用）
     */
    public String getBankDataUrls() {
        return bankDataUrls;
    }

    public void setBankDataUrls(String bankDataUrls) {
        this.bankDataUrls = bankDataUrls;
    }

    /**
     * @return 电子邮箱地址
     */
    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    /**
     * @return 通讯地址
     */
    public String getMailAddress() {
        return mailAddress;
    }

    public void setMailAddress(String mailAddress) {
        this.mailAddress = mailAddress;
    }

    /**
     * @return 邮政编码
     */
    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    /**
     * @return 法定代表人
     */
    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    /**
     * @return 法定代表人联系电话
     */
    public String getLegalMobile() {
        return legalMobile;
    }

    public void setLegalMobile(String legalMobile) {
        this.legalMobile = legalMobile;
    }

    /**
     * @return 委托代理人姓名
     */
    public String getConsignorName() {
        return consignorName;
    }

    public void setConsignorName(String consignorName) {
        this.consignorName = consignorName;
    }

    /**
     * @return 委托代理人联系电话
     */
    public String getConsignorMobile() {
        return consignorMobile;
    }

    public void setConsignorMobile(String consignorMobile) {
        this.consignorMobile = consignorMobile;
    }

    /**
     * @return 委托人与签约方关系(1.朋友 2.亲属)
     */
    public String getConsignorSignatoryRelationType() {
        return consignorSignatoryRelationType;
    }

    public void setConsignorSignatoryRelationType(String consignorSignatoryRelationType) {
        this.consignorSignatoryRelationType = consignorSignatoryRelationType;
    }

    /**
     * @return 委托人证件类型(1.身份证 2.户口本)
     */
    public String getConsignorCertificationType() {
        return consignorCertificationType;
    }

    public void setConsignorCertificationType(String consignorCertificationType) {
        this.consignorCertificationType = consignorCertificationType;
    }

    /**
     * @return 委托人证件号码
     */
    public String getConsignorIdvoCard() {
        return consignorIdvoCard;
    }

    public void setConsignorIdvoCard(String consignorIdvoCard) {
        this.consignorIdvoCard = consignorIdvoCard;
    }

    /**
     * @return 委托人资料地址
     */
    public String getConsignorDataUrls() {
        return consignorDataUrls;
    }

    public void setConsignorDataUrls(String consignorDataUrls) {
        this.consignorDataUrls = consignorDataUrls;
    }

    /**
     * @return 委托人照片地址
     */
    public String getConsignorIdvoCardPhotoUrl() {
        return consignorIdvoCardPhotoUrl;
    }

    public void setConsignorIdvoCardPhotoUrl(String consignorIdvoCardPhotoUrl) {
        this.consignorIdvoCardPhotoUrl = consignorIdvoCardPhotoUrl;
    }

    /**
     * @return 办理人照片地址
     */
    public String getTransactorIdvoCardPhotoUrl() {
        return transactorIdvoCardPhotoUrl;
    }

    public void setTransactorIdvoCardPhotoUrl(String transactorIdvoCardPhotoUrl) {
        this.transactorIdvoCardPhotoUrl = transactorIdvoCardPhotoUrl;
    }

    /**
     * @return 委托人身份证正面照片（弃用）
     */
    public String getConsignorIdvoCardFrontPhotoUrl() {
        return consignorIdvoCardFrontPhotoUrl;
    }

    public void setConsignorIdvoCardFrontPhotoUrl(String consignorIdvoCardFrontPhotoUrl) {
        this.consignorIdvoCardFrontPhotoUrl = consignorIdvoCardFrontPhotoUrl;
    }

    /**
     * @return 委托人身份证背面面照片（弃用）
     */
    public String getConsignorIdvoCardBackPhotoUrl() {
        return consignorIdvoCardBackPhotoUrl;
    }

    public void setConsignorIdvoCardBackPhotoUrl(String consignorIdvoCardBackPhotoUrl) {
        this.consignorIdvoCardBackPhotoUrl = consignorIdvoCardBackPhotoUrl;
    }

    /**
     * @return 办理人身份证正面照片（弃用）
     */
    public String getTransactorIdvoCardFrontPhotoUrl() {
        return transactorIdvoCardFrontPhotoUrl;
    }

    public void setTransactorIdvoCardFrontPhotoUrl(String transactorIdvoCardFrontPhotoUrl) {
        this.transactorIdvoCardFrontPhotoUrl = transactorIdvoCardFrontPhotoUrl;
    }

    /**
     * @return 办理人身份证背面照片（弃用）
     */
    public String getTransactorIdvoCardBackPhotoUrl() {
        return transactorIdvoCardBackPhotoUrl;
    }

    public void setTransactorIdvoCardBackPhotoUrl(String transactorIdvoCardBackPhotoUrl) {
        this.transactorIdvoCardBackPhotoUrl = transactorIdvoCardBackPhotoUrl;
    }

    /**
     * @return 是否删除
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 租户客户编号
     */
    public String getTenantCustomerNo() {
        return tenantCustomerNo;
    }

    public void setTenantCustomerNo(String tenantCustomerNo) {
        this.tenantCustomerNo = tenantCustomerNo;
    }

    /**
     * @return 租户客商编号
     */
    public String getTenantSupplierNo() {
        return tenantSupplierNo;
    }

    public void setTenantSupplierNo(String tenantSupplierNo) {
        this.tenantSupplierNo = tenantSupplierNo;
    }

    /**
     * @return 租户客商名称
     */
    public String getTenantSupplierName() {
        return tenantSupplierName;
    }

    public void setTenantSupplierName(String tenantSupplierName) {
        this.tenantSupplierName = tenantSupplierName;
    }

    /**
     * @return 企业客商编号
     */
    public String getCompanySupplierNo() {
        return companySupplierNo;
    }

    public void setCompanySupplierNo(String companySupplierNo) {
        this.companySupplierNo = companySupplierNo;
    }

    /**
     * @return 企业客商名称
     */
    public String getCompanySupplierName() {
        return companySupplierName;
    }

    public void setCompanySupplierName(String companySupplierName) {
        this.companySupplierName = companySupplierName;
    }

    /**
     * @return 企业证照类型
     */
    public String getCompanyIdType() {
        return companyIdType;
    }

    public void setCompanyIdType(String companyIdType) {
        this.companyIdType = companyIdType;
    }

    /**
     * @return 缴费金额
     */
    public String getCustomerPaymentAmount() {
        return customerPaymentAmount;
    }

    public void setCustomerPaymentAmount(String customerPaymentAmount) {
        this.customerPaymentAmount = customerPaymentAmount;
    }

    /**
     * @return 国籍
     */
    public String getCustomerNationality() {
        return customerNationality;
    }

    public void setCustomerNationality(String customerNationality) {
        this.customerNationality = customerNationality;
    }

    /**
     * @return 营业执照
     */
    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }

    /**
     * @return 申请业态
     */
    public String getApplyBusinessType() {
        return applyBusinessType;
    }

    public void setApplyBusinessType(String applyBusinessType) {
        this.applyBusinessType = applyBusinessType;
    }

    /**
     * @return 申请业态名称
     */
    public String getApplyBusinessTypeName() {
        return applyBusinessTypeName;
    }

    public void setApplyBusinessTypeName(String applyBusinessTypeName) {
        this.applyBusinessTypeName = applyBusinessTypeName;
    }

    /**
     * @return 意向登记主键id
     */
    public String getIntentionId() {
        return intentionId;
    }

    public void setIntentionId(String intentionId) {
        this.intentionId = intentionId;
    }

    @Override
    public String toString() {
        return "BbsResultCustomerPageVo{" +
                "rcId=" + rcId +
                ", relationId=" + relationId +
                ", rrId=" + rrId +
                ", type=" + type +
                ", customerNo=" + customerNo +
                ", customerName=" + customerName +
                ", customerType=" + customerType +
                ", customerSupplierNo=" + customerSupplierNo +
                ", customerRatio=" + customerRatio +
                ", customerCreditCode=" + customerCreditCode +
                ", customerIdType=" + customerIdType +
                ", customerIdNumber=" + customerIdNumber +
                ", customerGender=" + customerGender +
                ", customerWorkTel=" + customerWorkTel +
                ", customerTel=" + customerTel +
                ", customerPublicRecordNo=" + customerPublicRecordNo +
                ", customerHouseAddress=" + customerHouseAddress +
                ", bankNameCode=" + bankNameCode +
                ", bankName=" + bankName +
                ", bankCard=" + bankCard +
                ", bankPhone=" + bankPhone +
                ", bankIsAuthentication=" + bankIsAuthentication +
                ", bankIsAgreement=" + bankIsAgreement +
                ", bankAgreementNo=" + bankAgreementNo +
                ", bankAgreementType=" + bankAgreementType +
                ", bankSubbranchCode=" + bankSubbranchCode +
                ", bankSubbranchName=" + bankSubbranchName +
                ", bankNccCategoryCode=" + bankNccCategoryCode +
                ", bankUserName=" + bankUserName +
                ", bankDataUrls=" + bankDataUrls +
                ", emailAddress=" + emailAddress +
                ", mailAddress=" + mailAddress +
                ", postalCode=" + postalCode +
                ", legalName=" + legalName +
                ", legalMobile=" + legalMobile +
                ", consignorName=" + consignorName +
                ", consignorMobile=" + consignorMobile +
                ", consignorSignatoryRelationType=" + consignorSignatoryRelationType +
                ", consignorCertificationType=" + consignorCertificationType +
                ", consignorIdvoCard=" + consignorIdvoCard +
                ", consignorDataUrls=" + consignorDataUrls +
                ", consignorIdvoCardPhotoUrl=" + consignorIdvoCardPhotoUrl +
                ", transactorIdvoCardPhotoUrl=" + transactorIdvoCardPhotoUrl +
                ", consignorIdvoCardFrontPhotoUrl=" + consignorIdvoCardFrontPhotoUrl +
                ", consignorIdvoCardBackPhotoUrl=" + consignorIdvoCardBackPhotoUrl +
                ", transactorIdvoCardFrontPhotoUrl=" + transactorIdvoCardFrontPhotoUrl +
                ", transactorIdvoCardBackPhotoUrl=" + transactorIdvoCardBackPhotoUrl +
                ", delFlag=" + delFlag +
                ", tenantCustomerNo=" + tenantCustomerNo +
                ", tenantSupplierNo=" + tenantSupplierNo +
                ", tenantSupplierName=" + tenantSupplierName +
                ", companySupplierNo=" + companySupplierNo +
                ", companySupplierName=" + companySupplierName +
                ", companyIdType=" + companyIdType +
                ", customerPaymentAmount=" + customerPaymentAmount +
                ", customerNationality=" + customerNationality +
                ", businessLicense=" + businessLicense +
                ", applyBusinessType=" + applyBusinessType +
                ", applyBusinessTypeName=" + applyBusinessTypeName +
                ", intentionId=" + intentionId +
                "}";
    }
}

package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 缩租面积变更变更后商品信息表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-25
 * @change 2024-10-25 by tbh for init
 */
@ApiModel(value="BbsChangeShopInfoPageVo对象", description="缩租面积变更变更后商品信息表")
public class BbsChangeShopInfoPageVo extends McpBasePageVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String shopInfoId;

    /**
     * 合同变更表主键id
     */
    @ApiModelProperty(value = "合同变更表主键id")
                            private String ccId;

    /**
     * 1已选商铺,2已退商铺
     */
    @ApiModelProperty(value = "1已选商铺,2已退商铺")
                            private String type;

    /**
     * 是否是拆分的商铺(1是)
     */
    @ApiModelProperty(value = "是否是拆分的商铺(1是)")
                            private String isSplit;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
                            private String productName;

    /**
     * 产品地址
     */
    @ApiModelProperty(value = "产品地址")
                            private String productAddress;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
                            private String productNo;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
                            private String area;

    /**
     * 面积类型(1.建筑面积 2.套内建筑面积)
     */
    @ApiModelProperty(value = "面积类型(1.建筑面积 2.套内建筑面积)")
                            private String areaType;

    /**
     * 拆分前的产品编号(存在拆分时)
     */
    @ApiModelProperty(value = "拆分前的产品编号(存在拆分时)")
                            private String productNoOld;

    @ApiModelProperty(value = "租金标准单位(拆分后的有)")
    private String rentStandardUnit;
    @ApiModelProperty(value = "租金标准(拆分后的有)")
    private Double rentStandard;

    @ApiModelProperty(value = "拆分前的产品名称(存在拆分时)")
    private String productNameOld;

    @ApiModelProperty(value = "物业费租金标准单位(拆分后的有)")
    private String propertyStandardUnit;
    @ApiModelProperty(value = "物业费租金标准金额(拆分后的有)")
    private Double propertyStandard;


    public String getPropertyStandardUnit() {
        return propertyStandardUnit;
    }

    public void setPropertyStandardUnit(String propertyStandardUnit) {
        this.propertyStandardUnit = propertyStandardUnit;
    }

    public Double getPropertyStandard() {
        return propertyStandard;
    }

    public void setPropertyStandard(Double propertyStandard) {
        this.propertyStandard = propertyStandard;
    }

    public String getProductNameOld() {
        return productNameOld;
    }

    public void setProductNameOld(String productNameOld) {
        this.productNameOld = productNameOld;
    }

    public String getRentStandardUnit() {
        return rentStandardUnit;
    }

    public void setRentStandardUnit(String rentStandardUnit) {
        this.rentStandardUnit = rentStandardUnit;
    }

    public Double getRentStandard() {
        return rentStandard;
    }

    public void setRentStandard(Double rentStandard) {
        this.rentStandard = rentStandard;
    }

    /**
     * @return 主键
     */
    public String getShopInfoId() {
        return shopInfoId;
    }

    public void setShopInfoId(String shopInfoId) {
        this.shopInfoId = shopInfoId;
    }

    /**
     * @return 合同变更表主键id
     */
    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }

    /**
     * @return 1已选商铺,2已退商铺
     */
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return 是否是拆分的商铺(1是)
     */
    public String getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(String isSplit) {
        this.isSplit = isSplit;
    }

    /**
     * @return 产品名称
     */
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * @return 产品地址
     */
    public String getProductAddress() {
        return productAddress;
    }

    public void setProductAddress(String productAddress) {
        this.productAddress = productAddress;
    }

    /**
     * @return 产品编号
     */
    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    /**
     * @return 面积
     */
    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    /**
     * @return 面积类型(1.建筑面积 2.套内建筑面积)
     */
    public String getAreaType() {
        return areaType;
    }

    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }

    /**
     * @return 拆分前的产品编号(存在拆分时)
     */
    public String getProductNoOld() {
        return productNoOld;
    }

    public void setProductNoOld(String productNoOld) {
        this.productNoOld = productNoOld;
    }

      @Override
    public String toString() {
        return "BbsChangeShopInfoPageVo{" +
            "shopInfoId=" + shopInfoId +
            ", ccId=" + ccId +
            ", type=" + type +
            ", isSplit=" + isSplit +
            ", productName=" + productName +
            ", productAddress=" + productAddress +
            ", productNo=" + productNo +
            ", area=" + area +
            ", areaType=" + areaType +
            ", productNoOld=" + productNoOld +
        "}";
    }
}

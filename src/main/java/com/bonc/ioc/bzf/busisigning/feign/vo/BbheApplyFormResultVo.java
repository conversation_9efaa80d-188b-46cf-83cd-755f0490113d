package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 租户信息表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-20
 * @change 2023-05-20 by sx for init
 */
@Data
@ApiModel(value = "BbctContractSignerVo对象", description = "租户信息表")
public class BbheApplyFormResultVo extends McpBaseVo implements Serializable {

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private String applyDate;

    /**
     * 申请单编号
     */
    @ApiModelProperty(value = "申请单编号")
    private String applyNum;

    /**
     * 申请阶段
     */
    @ApiModelProperty(value = "申请阶段")
    private String applyStage;

    /**
     * 申请阶段名称
     */
    @ApiModelProperty(value = "申请阶段名称")
    private String applyStageName;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态")
    private String applyStatus;

    /**
     * 申请状态名称
     */
    @ApiModelProperty(value = "申请状态名称")
    private String applyStatusName;

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractCode;


    @ApiModelProperty(value = "申请单编号")
    private String exchangeApplyId;

}

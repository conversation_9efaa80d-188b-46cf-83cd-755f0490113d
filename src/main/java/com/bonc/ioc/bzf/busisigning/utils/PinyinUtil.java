package com.bonc.ioc.bzf.busisigning.utils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
public class PinyinUtil {
//    private final static String  NUM = "0123456789";

    /**
     * 将汉字转成拼音首字母
     * @param hanzi
     * @return
     */
    public static String convertHanzi2PinyinShouZhiMu(String hanzi) {
        return  convertHanzi2Pinyin(hanzi,false);
    }
    /***
     * 将汉字转成拼音(取首字母或全拼)
     * @param hanzi
     * @param full 是否全拼
     * @return
     */
    public static String convertHanzi2Pinyin(String hanzi,boolean full)
    {
        /***
         * ^[\u2E80-\u9FFF]+$ 匹配所有东亚区的语言
         * ^[\u4E00-\u9FFF]+$ 匹配简体和繁体
         * ^[\u4E00-\u9FA5]+$ 匹配简体
         */
        String regExp="^[\u4E00-\u9FFF]+$";
        StringBuffer sb=new StringBuffer();
        if(hanzi==null||"".equals(hanzi.trim()))
        {
            return "";
        }
        String pinyin="";
        for(int i=0;i<hanzi.length();i++)
        {
            char unit=hanzi.charAt(i);
            if(match(String.valueOf(unit),regExp))//是汉字，则转拼音
            {
                pinyin=convertSingleHanzi2Pinyin(unit);
                if(full)
                {
                    sb.append(pinyin);
                }
                else
                {
                    sb.append(pinyin.charAt(0));
                }
            }
            else
            {
                sb.append(unit);
            }
        }
        return sb.toString();
    }
    /***
     * 将单个汉字转成拼音
     * @param hanzi
     * @return
     */
    private static String convertSingleHanzi2Pinyin(char hanzi)
    {
        HanyuPinyinOutputFormat outputFormat = new HanyuPinyinOutputFormat();
        outputFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        String[] res;
        StringBuffer sb=new StringBuffer();
        try {
            res = PinyinHelper.toHanyuPinyinStringArray(hanzi,outputFormat);
            sb.append(res[0]);//对于多音字，只用第一个拼音
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
        return sb.toString();
    }

    /***
     * @param str 源字符串
     * @param regex 正则表达式
     * @return 是否匹配
     */
    public static boolean match(String str,String regex)
    {
        Pattern pattern=Pattern.compile(regex);
        Matcher matcher=pattern.matcher(str);
        return matcher.find();
    }

//    /**
//     * 获取汉字首拼音，包含数字字符
//     * @param hanzi  源字符串
//     * @return
//     */
//    public static String getFirstPinyin(String hanzi) {
//        String s = convertHanzi2Pinyin(hanzi, false).substring(0, 1).toUpperCase();
//        if(NUM.contains(s)) {
//            s = numBerToPinyin(s);
//        }
//        return  s;
//    }


    /**
     * 将数字[0-9]转化为拼音首字母大写
     * @param str
     * @return
     */
    private static String numBerToPinyin(String str) {
        String pinyin = null;
        switch (str) {
            case "0":
                pinyin = "L";
                break;
            case "1":
                pinyin = "Y";
                break;
            case "2":
                pinyin = "E";
                break;
            case "3":
                pinyin = "S";
                break;
            case "4":
                pinyin = "S";
                break;
            case "5":
                pinyin = "W";
                break;
            case "6":
                pinyin = "L";
                break;
            case "7":
                pinyin = "Q";
                break;
            case "8":
                pinyin = "B";
                break;
            case "9":
                pinyin = "J";
                break;
        }
        return pinyin;

    }

//    public static void main(String[] args) {
//        String str="南宫嘉园";
//        String pinyinStr = convertHanzi2PinyinShouZhiMu(str.substring(0,2)).toUpperCase();
//        System.out.println(pinyinStr);
//    }
}

/**
  * Copyright 2023 json.cn 
  */
package com.bonc.ioc.bzf.busisigning.vo.JsonVo;
import java.util.List;

/**
 * Auto-generated: 2023-08-30 9:34:15
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class RentFreePeriodType {

    private String type;
    private List<IntervalArray> intervalArray;
    private FixedValueInfo fixedValueInfo;
    public void setType(String type) {
         this.type = type;
     }
     public String getType() {
         return type;
     }

    public void setIntervalArray(List<IntervalArray> intervalArray) {
         this.intervalArray = intervalArray;
     }
     public List<IntervalArray> getIntervalArray() {
         return intervalArray;
     }

    public void setFixedValueInfo(FixedValueInfo fixedValueInfo) {
         this.fixedValueInfo = fixedValueInfo;
     }
     public FixedValueInfo getFixedValueInfo() {
         return fixedValueInfo;
     }

}
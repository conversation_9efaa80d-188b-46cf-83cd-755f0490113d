package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 签约入住规则表v3.0 推送入住数据接口
 * <AUTHOR>
 * @date 2022-12-03
 * @change 2022-12-03 by ly for init
 */
@ApiModel(value="BbciCustCheckInVo", description="推送入住数据接口")
@Data
public class BbciCustCheckInVo extends McpBaseVo implements Serializable{
    /**
     * 入住方式1-查验入住2-只入住
     */
    @ApiModelProperty(value = "入住方式1-查验入住2-只入住")
    private String checkInWay;

    /**
     * 入住类型 0-签约同时办理入住 查验-同时办理 其他传1
     */
    @ApiModelProperty(value = "入住类型 0-签约同时办理入住")
    private String checkInType;

    /**
     * 安排id
     */
    @ApiModelProperty(value = "安排id")
    private String arrangeId;

    /**
     * 签约计划id
     */
    @ApiModelProperty(value = "签约计划id")
    private String signingPlanId;

    /**
     * 运营项目Id
     */
    @ApiModelProperty(value = "运营项目Id")
    private String projectId;

    /**
     * 运营项目名称
     */
    @ApiModelProperty(value = "运营项目名称")
    private String projectName;


    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id")
    private String communityId;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区名称")
    private String communityName;

    /**
     * 业务code，公租房／保租房
     */
    @ApiModelProperty(value = "业务code")
    private String businessCode;

    /**
     * 租赁code，趸租／散租．．．．
     */
    @ApiModelProperty(value = "租赁code")
    private String leaseCode;

    /**
     * 楼栋号
     */
    @ApiModelProperty(value = "楼栋号")
    private String buildingNo;

    /**
     * 楼栋号
     */
    @ApiModelProperty(value = "楼栋号")
    private String buildingName;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    private String unitNo;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    private String unitName;


    /**
     * 房源id
     */
    @ApiModelProperty(value = "房源id")
    private String houseResourceId;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
    private String houseResource;


    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customerId;

    /**
     * 客户手机号
     */
    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    /**
     * 消息是否发送 1-未发送 2已发送
     */
    @ApiModelProperty(value = "消息是否发送 1-未发送 2已发送")
    private String sendState;


    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String companyName;

    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
    private String houseRent;

    /**
     * 租金标准
     */
    @ApiModelProperty(value = " 租金标准")
    private String rentStandard;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractEndTime;

    /**
     * 数据来源 0-已签约1-导入
     */
    @ApiModelProperty(value = "数据来源 0-已签约1-导入")
    private String checkDataSources;
}

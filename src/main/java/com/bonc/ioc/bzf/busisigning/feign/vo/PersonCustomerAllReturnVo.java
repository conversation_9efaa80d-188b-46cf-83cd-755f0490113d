package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 审批 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/10
 */
@Data
@ApiModel(value="PersonCustomerAllReturnVo", description="PersonCustomerAllReturnVo")
public class PersonCustomerAllReturnVo implements Serializable {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private String birthday;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String certificateNum;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    /**
     * 客户注册来源
     */
    @ApiModelProperty(value = "客户注册来源")
    private String customerRegisterSource;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String genderCode;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nation;
    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    private String nationality;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String personCustomerId;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String personCustomerName;
    /**
     * 手机号集合 默认传是否注册1
     */
    @ApiModelProperty(value = "手机号集合 默认传是否注册1")
    List<PersonCustomerPhoneParamVo> personCustomerPhoneParamVos;

    @ApiModelProperty(value = "客户拓展信息")
    private PersonCustomerExtendReturnVo extendInfo;

}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 机构客户-房关系表 实体类
 *
 * @date 2023-06-08
 * @change 2023-06-08 by wangkz for init
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value="OrgCustomerHouseRelInsertVo对象", description="机构客户-房关系表")
public class OrgCustomerHouseRelInsertVo  extends McpBaseVo implements Serializable {

    /**
     * 机构客户ID
     */
    @ApiModelProperty(value = "机构客户ID", required = true)
    @NotBlank(message = "机构客户ID不能为空")
    @Length(max = 64, message = "机构客户ID长度不能超过64")
    private String orgCustomerId;

    /**
     * 房屋编码
     */
    @ApiModelProperty(value = "房屋编码", required = true)
    @NotBlank(message = "房屋编码不能为空")
    @Length(max = 100, message = "房屋编码长度不能超过100")
    private String houseCode;

    /**
     * 房源编码
     */
    @ApiModelProperty(value = "房源编码", required = true)
    @NotBlank(message = "房源编码不能为空")
    @Length(max = 100, message = "房源编码长度不能超过100")
    private String houseResourcesCode;

    /**
     * 小区编码
     */
    @ApiModelProperty(value = "小区编码", required = true)
    @NotBlank(message = "小区编码不能为空")
    @Length(max = 100, message = "小区编码长度不能超过100")
    private String communityCode;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码", required = true)
    @NotBlank(message = "合同编码不能为空")
    @Length(max = 100, message = "合同编码长度不能超过100")
    private String contractCode;

    /**
     * 运营项目ID
     */
    @ApiModelProperty(value = "运营项目ID", required = true)
    @NotBlank(message = "运营项目ID不能为空")
    @Length(max = 100, message = "运营项目ID长度不能超过100")
    private String operateProjectId;

    /**
     * 签约业务类型编码
     */
    @ApiModelProperty(value = "签约业务类型编码(02:商业、0101:公租房散租、0102:公租房趸租、0103:公租房趸租管理协议、0701:保租房散租、0702:保租房趸租、0703:保租房趸租管理协议)", required = true)
    @NotBlank(message = "签约业务类型编码不能为空")
    @Length(max = 20, message = "签约业务类型编码长度不能超过20")
    private String businessTypeCode;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间", required = true)
    @NotNull(message = "签约时间不能为空")
    private String signingDate;

    /**
     * 批次号(用于标记同一次导入)
     */
    @ApiModelProperty(value = "批次号(用于标记同一次导入)")
    @Length(max = 64, message = "批次号长度不能超过64")
    private String batchNo;
}

package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 标准类型 枚举类
 *
 * <AUTHOR>
 * @since 2024/9/12
 */
public enum StandardTypeEnum {

    /**
     * 租金
     */
    RENT("rent", "租金"),

    /**
     * 物业费
     */
    PROP("prop", "物业费");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    StandardTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    StandardTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        StandardTypeEnum[] enums = values();
        for (StandardTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}

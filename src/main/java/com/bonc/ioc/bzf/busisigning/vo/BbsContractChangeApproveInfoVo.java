package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 合同变更审批表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by fzq for init
 */
@ApiModel(value="BbsContractChangeApproveInfoVo对象", description="合同变更审批表")
public class BbsContractChangeApproveInfoVo extends McpBaseVo implements Serializable{


    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    @NotBlank(message = "审批id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String approveId;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
                            private String ccId;

    /**
     * 审批状态(1.已通过 2.未通过 3.待审核）
     */
    @ApiModelProperty(value = "审批状态(1.已通过 2.未通过 3.待审核）")
                            private String approveStatus;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date approveTime;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
                            private String approverUserId;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
                            private String approverUserName;

    /**
     * @return 审批id
     */
    public String getApproveId() {
        return approveId;
    }

    public void setApproveId(String approveId) {
        this.approveId = approveId;
    }

    /**
     * @return 合同id
     */
    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }

    /**
     * @return 审批状态(1.已通过 2.未通过 3.待审核）
     */
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    /**
     * @return 审批时间
     */
    public Date getApproveTime(){
        if(approveTime!=null){
            return (Date)approveTime.clone();
        }else{
            return null;
        }
    }

    public void setApproveTime(Date approveTime) {
        if(approveTime==null){
            this.approveTime = null;
        }else{
            this.approveTime = (Date)approveTime.clone();
        }
    }

    /**
     * @return 审批人id
     */
    public String getApproverUserId() {
        return approverUserId;
    }

    public void setApproverUserId(String approverUserId) {
        this.approverUserId = approverUserId;
    }

    /**
     * @return 审批人姓名
     */
    public String getApproverUserName() {
        return approverUserName;
    }

    public void setApproverUserName(String approverUserName) {
        this.approverUserName = approverUserName;
    }

      @Override
    public String toString() {
        return "BbsContractChangeApproveInfoVo{" +
            "approveId=" + approveId +
            ", ccId=" + ccId +
            ", approveStatus=" + approveStatus +
            ", approveTime=" + approveTime +
            ", approverUserId=" + approverUserId +
            ", approverUserName=" + approverUserName +
        "}";
    }
}

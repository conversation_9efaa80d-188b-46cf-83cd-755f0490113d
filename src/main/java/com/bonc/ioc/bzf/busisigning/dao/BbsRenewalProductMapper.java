package com.bonc.ioc.bzf.busisigning.dao;

import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalProductEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.busisigning.vo.*;
import java.util.List;

/**
 * 人-产品：产品表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@Mapper
public interface BbsRenewalProductMapper extends McpBaseMapper<BbsRenewalProductEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change 2023-09-22 by King-Y for init
     */
    List<BbsRenewalProductPageResultVo> selectByPageCustom(@Param("vo") BbsRenewalProductPageVo vo );


    List<BbsRenewalProductEntity> selectBySignId(@Param("signId") String signId);
}

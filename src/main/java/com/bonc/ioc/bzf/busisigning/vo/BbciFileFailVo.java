package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2022-09-22
 * @change 2022-09-22 by wtl for init
 */
@ApiModel(value = "BbciFileFailVo")
@Data
public class BbciFileFailVo extends McpBaseVo implements Serializable {
    public List<String> failList;

    public List<Integer> fileList;

}

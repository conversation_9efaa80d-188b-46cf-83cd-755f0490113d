package com.bonc.ioc.bzf.busisigning.factory.sign;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.consts.KeywordConst;
import com.bonc.ioc.bzf.busisigning.enums.AreaTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.StandardTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.WhetherEnum;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.WaterMarkVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.utils.DictTreeToBusinessFormatUtil;
import com.bonc.ioc.bzf.busisigning.utils.MoneyToChineseUtils;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultProductVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultRelationVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignIncrementalConfigVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.ChargeRespondVo;
import com.bonc.ioc.bzf.busisigning.vo.Depos;
import com.bonc.ioc.bzf.busisigning.vo.PreviewBillsResultVo;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 普通合同签约 工厂类
 *
 * <AUTHOR>
 * @since 2024/10/25
 */
@Slf4j
public class CommonContractSignFactory extends AbstractContractSignFactory {

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param signInfoVo                   签约信息 vo实体
     * @param renewalInfoVo                续签信息 vo实体
     */
    public CommonContractSignFactory(FeignServiceConfiguration feignServiceConfiguration,
                                     BusinessServiceConfiguration businessServiceConfiguration,
                                     BbsSignInfoVo signInfoVo,
                                     BbsRenewalInfoVo renewalInfoVo) {
        super(feignServiceConfiguration,
                businessServiceConfiguration,
                signInfoVo,
                renewalInfoVo);
    }

    /**
     * 预览或下载
     *
     * @param appFlag       app标签
     * @param watermarkType 水印类型
     * @return 合同文件信息 vo实体
     */
    @Override
    public FIleResultVo previewAndDownload(boolean appFlag, String watermarkType) {
        BbctPreviewInfoParamsVo previewInfoParamsVo = new BbctPreviewInfoParamsVo();
        previewInfoParamsVo.setAppFlag(appFlag);
        previewInfoParamsVo.setWatermarkType(watermarkType);
        // 获取人房关系信息
        BbsResultRelationVo relationVo = getRelationInfo();
        // 获取客户信息
        previewInfoParamsVo.setCustomerVo(getCustomerInfo(relationVo.getRrId()));
        // 获取产品信息
        previewInfoParamsVo.setProductList(getProductList(relationVo.getRrId()));
        // 获取模板属性map集
        previewInfoParamsVo.setTemplateSeatMap(getTemplateSeatMap());
        // 获取租金递增规则
        previewInfoParamsVo.setRentIncrementalConfigList(getIncrementalConfigList(StandardTypeEnum.RENT.getCode()));
        // 获取物业费递增规则
        previewInfoParamsVo.setPropIncrementalConfigList(getIncrementalConfigList(StandardTypeEnum.PROP.getCode()));
        return createPdf(createPreviewInfoVo(previewInfoParamsVo));
    }

    /**
     * 创建预览信息 vo实体
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     * @return 预览信息 vo实体
     */
    @Override
    public BbctContractManagementVo createPreviewInfoVo(BbctPreviewInfoParamsVo previewInfoParamsVo) {
        BbctContractManagementVo contractManagementVo = new BbctContractManagementVo();
        if (StringUtils.isBlank(signInfoVo.getContractTemplateId())) {
            throw new McpException("合同模板ID为空");
        }
        contractManagementVo.setContractTemplateId(signInfoVo.getContractTemplateId());
        Map<String, Object> resultMap = new HashMap<>(previewInfoParamsVo.getTemplateSeatMap());
        // 赋值默认签约信息
        setDefaultSignInfoToPreviewInfo(resultMap,
                previewInfoParamsVo.getCustomerVo(),
                previewInfoParamsVo.getProductList());
        // 赋值签约信息
        setSignInfoToPreviewInfo(resultMap,
                previewInfoParamsVo.getCustomerVo(),
                previewInfoParamsVo.getProductList());
        // 赋值递增规则信息
        setIncrementalInfoToPreviewInfo(resultMap,
                previewInfoParamsVo.getProductList(),
                previewInfoParamsVo.getRentIncrementalConfigList(),
                previewInfoParamsVo.getPropIncrementalConfigList());
        // 获取试算结果
        ChargeRespondVo<PreviewBillsResultVo> previewBills = businessServiceConfiguration
                .getSignInfoExtService()
                .getPreviewBills(signInfoVo);
        // 赋值押金信息
        setCashPledgeInfoToPreviewInfo(resultMap, previewBills);
        // 赋值物业费信息
        setPropInfoToPreviewInfo(resultMap, previewBills);
        // 赋值租金信息
        setRentInfoToPreviewInfo(resultMap, previewBills);
        // 赋值app信息
        setAppInfoToPreviewInfo(resultMap, previewInfoParamsVo.getAppFlag());
        // 赋值附件信息
        setAttachmentInfoToPreviewInfo(resultMap,
                previewInfoParamsVo.getCustomerVo());
        contractManagementVo.setContractAttr(resultMap);
        // 赋值水印信息
        setWatermarkInfoToPreviewInfo(contractManagementVo,
                previewInfoParamsVo.getWatermarkType(),
                previewInfoParamsVo.getTemplateSeatMap());
        return contractManagementVo;
    }

    /**
     * 赋值签约信息到预览信息实体
     *
     * @param resultMap   预览信息map集
     * @param customerVo  客户信息 vo实体
     * @param productList 产品信息列表
     */
    private void setSignInfoToPreviewInfo(Map<String, Object> resultMap,
                                          BbsResultCustomerVo customerVo,
                                          List<BbsResultProductVo> productList) {
        //房屋实际用途名称
        String houseRealPurposeName = productList.stream().map(BbsResultProductVo::getHousePurposeName).distinct().collect(Collectors.joining("、"));
        //装修状态/装修情况名称
        String houseDecorateStateName = productList.stream().map(BbsResultProductVo::getDecorateState).distinct().collect(Collectors.joining("、"));
        resultMap.put("contractCode", signInfoVo.getContractCode());
        resultMap.put("xmName", customerVo.getCustomerName());
        resultMap.put("projectName", productList.get(0).getProjectName());
        resultMap.put("productName", productList.stream().map(BbsResultProductVo::getProductName).collect(Collectors.joining("、")));
        resultMap.put("firstBankCard", signInfoVo.getFirstAccountId());
        resultMap.put("firstAccountName", signInfoVo.getFirstAccountName());
        resultMap.put("firstBankName", signInfoVo.getFirstBankName());
        resultMap.put("propertyNature", houseRealPurposeName);
        resultMap.put("businessFormat", DictTreeToBusinessFormatUtil.getPreviewFormatName(signInfoVo.getBusinessFormat()));
        resultMap.put("renovationStatus", houseDecorateStateName);
        resultMap.put("houseArea", productList
                .stream()
                .mapToDouble(
                        p -> Convert.toDouble(AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(signInfoVo.getAreaType()) ?
                                p.getHouseStructArea() : p.getInnerSleeveArea()))
                .sum());
        resultMap.put("houseAreaStr", productList
                .stream()
                .map(
                        p -> (productList.size() > 1 ? p.getProductName() : "") +
                                "【" +
                                (AreaTypeEnum.BUILD_AREA_TYPE.getCode().equals(signInfoVo.getAreaType()) ?
                                        p.getHouseStructArea() : p.getInnerSleeveArea()) +
                                "】平方米")
                .collect(Collectors.joining("、")));
        String contractBeginTime = Objects.nonNull(signInfoVo.getContractBeginTime()) ? DateUtils.formatDate(signInfoVo.getContractBeginTime(), "yyyy-MM-dd") : null;
        String contractEndTime = Objects.nonNull(signInfoVo.getContractEndTime()) ? DateUtils.formatDate(signInfoVo.getContractEndTime(), "yyyy-MM-dd") : null;
        resultMap.put("rentBeginTime", Optional.ofNullable(contractBeginTime).orElse(""));
        resultMap.put("rentEndTime", Optional.ofNullable(contractEndTime).orElse(""));
        if (StringUtils.isNotBlank(contractBeginTime) && StringUtils.isNotBlank(contractEndTime)) {
            resultMap.put("totalLeaseTerm", DateUtils.getIntegerYearStr(DateUtil.parse(contractBeginTime), DateUtil.parse(contractEndTime)));
        } else {
            resultMap.put("totalLeaseTerm", "");
        }
        if (StrUtil.equals(signInfoVo.getRentFreePeriodType(), "0")) {
            resultMap.put("rentFreeDay", "/");
            resultMap.put("rentFreeTime", "/");
        } else if (StrUtil.equals(signInfoVo.getRentFreePeriodType(), "2")) {
            resultMap.put("rentFreeDay", signInfoVo.getRentFpFixedValue());
            if (StrUtil.isNotBlank(signInfoVo.getRentFpFixedDate()) && signInfoVo.getRentFpFixedValue() != null) {
                resultMap.put("rentFreeTime", DateUtils.getDateAddDaySubtraction(signInfoVo.getRentFpFixedDate(), signInfoVo.getRentFpFixedValue()));
            } else {
                resultMap.put("rentFreeTime", "/");
            }
        } else {
            resultMap.put("rentFreeDay", "");
            resultMap.put("rentFreeTime", "");
        }
        resultMap.put("secondAccountName", customerVo.getBankUserName());
        resultMap.put("secondBankName", customerVo.getBankSubbranchName().startsWith(customerVo.getBankName()) ? customerVo.getBankSubbranchName() : (customerVo.getBankName() + customerVo.getBankSubbranchName()));
        resultMap.put("secondBankCard", customerVo.getBankCard());
        List<McpDictEntity> areaTypeDictList = businessServiceConfiguration
                .getMcpDictSession()
                .getMcpDictUtil()
                .getPairsListByDictCode("AREA_TYPE");
        String areaTypeName = areaTypeDictList
                .stream()
                .filter(p -> p.getCode().equals(signInfoVo.getAreaType()))
                .map(McpDictEntity::getMeaning)
                .findFirst()
                .orElse("");
        resultMap.put("areaType", areaTypeName);
        List<McpDictEntity> paymentCycleCodeList = businessServiceConfiguration
                .getMcpDictSession()
                .getMcpDictUtil()
                .getPairsListByDictCode("PAYMENT_CYCLE_CODE");
        String paymentCycleCode = paymentCycleCodeList
                .stream()
                .filter(p -> p.getCode().equals(signInfoVo.getPaymentCycleCode()))
                .map(McpDictEntity::getMeaning)
                .findFirst()
                .orElse("");
        String paymentCycleMonth = paymentCycleCodeList
                .stream()
                .filter(p -> p.getCode().equals(signInfoVo.getPaymentCycleCode()))
                .map(McpDictEntity::getExpand)
                .findFirst()
                .orElse("");
        resultMap.put("paymentCycleCode", paymentCycleCode);
        resultMap.put("paymentCycleMonth", paymentCycleMonth);
        resultMap.put("secondPartyAddress", StrUtil.emptyToDefault((String) resultMap.get("secondPartyAddress"), customerVo.getMailAddress()));
        resultMap.put("secondPartyLegalMobile", StrUtil.emptyToDefault((String) resultMap.get("secondPartyLegalMobile"), customerVo.getCustomerTel()));
        resultMap.put("secondPartyPostalCode", StrUtil.emptyToDefault((String) resultMap.get("secondPartyPostalCode"), customerVo.getPostalCode()));
        resultMap.put("secondPartyLegalName", StrUtil.emptyToDefault((String) resultMap.get("secondPartyLegalName"), customerVo.getLegalName()));
    }

    /**
     * 赋值递增规则信息到预览信息实体
     *
     * @param resultMap                 预览信息map集
     * @param productList               产品信息列表
     * @param rentIncrementalConfigList 租金递增规则列表
     * @param propIncrementalConfigList 物业费递增规则列表
     */
    private void setIncrementalInfoToPreviewInfo(Map<String, Object> resultMap,
                                                 List<BbsResultProductVo> productList,
                                                 List<BbsSignIncrementalConfigVo> rentIncrementalConfigList,
                                                 List<BbsSignIncrementalConfigVo> propIncrementalConfigList) {
        // 赋值租金递增规则信息
        setRentIncrementalInfoToPreviewInfo(resultMap,
                rentIncrementalConfigList,
                productList);
        // 赋值物业费递增规则信息
        setPropIncrementalInfoToPreviewInfo(resultMap,
                propIncrementalConfigList,
                productList);
    }

    /**
     * 赋值递增规则信息到预览信息实体(租金)
     *
     * @param resultMap                 预览信息map集
     * @param rentIncrementalConfigList 租金递增规则列表
     * @param productList               产品信息列表
     */
    private void setRentIncrementalInfoToPreviewInfo(Map<String, Object> resultMap,
                                                     List<BbsSignIncrementalConfigVo> rentIncrementalConfigList,
                                                     List<BbsResultProductVo> productList) {
        if (CollectionUtils.isNotEmpty(rentIncrementalConfigList)) {
            String rents = "";
            if ("appoint".equals(rentIncrementalConfigList.get(0).getAdjustmentPoint())) {
                // 第
                rents = productList
                        .stream()
                        .map(p -> "租金为" +
                                p.getRentStandard() +
                                p.getRentStandardUnitName() +
                                "（价税合计）")
                        .collect(Collectors.joining("、")) +
                        "，" +
                        rentIncrementalConfigList
                                .stream()
                                .map(c -> "租金递增率为" +
                                        c.getAdjustmentPointName() +
                                        c.getTimePoint() +
                                        c.getUnitName() +
                                        StrUtil.emptyToDefault(Convert.toStr(c.getIncrease()), "0") +
                                        "%")
                                .collect(Collectors.joining("、"))
                        + "。";
            } else if ("every".equals(rentIncrementalConfigList.get(0).getAdjustmentPoint())) {
                // 每
                rents = productList
                        .stream()
                        .map(p -> "租金为" +
                                p.getRentStandard() +
                                p.getRentStandardUnitName() +
                                "（价税合计）")
                        .collect(Collectors.joining("、")) +
                        "，租金递增率为" +
                        StrUtil.emptyToDefault(Convert.toStr(rentIncrementalConfigList.get(0).getIncrease()), "0") +
                        "%/" +
                        (KeywordConst.ONE.equals(rentIncrementalConfigList.get(0).getTimePoint()) ?
                        "" : rentIncrementalConfigList.get(0).getTimePoint().toString()) +
                        rentIncrementalConfigList.get(0).getUnitName() +
                        "。";
            }
            resultMap.put("rents", rents);
        } else {
            String rents = productList
                    .stream()
                    .map(p -> "租金为" +
                            p.getRentStandard() +
                            p.getRentStandardUnitName() +
                            "（价税合计）")
                    .collect(Collectors.joining("、")) +
                    "，租金递增率0%/年。";
            resultMap.put("rents", rents);
        }
    }

    /**
     * 赋值递增规则信息到预览信息实体(物业费)
     *
     * @param resultMap                 预览信息map集
     * @param propIncrementalConfigList 物业费递增规则列表
     * @param productList               产品信息列表
     */
    private void setPropIncrementalInfoToPreviewInfo(Map<String, Object> resultMap,
                                                     List<BbsSignIncrementalConfigVo> propIncrementalConfigList,
                                                     List<BbsResultProductVo> productList) {
        if (signInfoVo.getContractFees().equals("02-08")) {
            if (CollectionUtils.isNotEmpty(propIncrementalConfigList)) {
                String cashPledges = "";
                if ("appoint".equals(propIncrementalConfigList.get(0).getAdjustmentPoint())) {
                    //第
                    cashPledges = productList
                            .stream()
                            .map(p -> "物业服务费单价为"
                                    + p.getPropStandard()
                                    + p.getPropStandardUnitName()
                                    + "（价税合计）")
                            .collect(Collectors.joining("、"))
                            + "，"
                            + propIncrementalConfigList
                            .stream()
                            .map(c -> "物业服务费递增率为"
                                    + c.getAdjustmentPointName()
                                    + c.getTimePoint()
                                    + c.getUnitName()
                                    + StrUtil.emptyToDefault(Convert.toStr(c.getIncrease()), "0") + "%")
                            .collect(Collectors.joining("、"))
                            + "。";
                } else if ("every".equals(propIncrementalConfigList.get(0).getAdjustmentPoint())) {
                    //每
                    cashPledges = productList
                            .stream()
                            .map(p -> "物业服务费单价为"
                                    + p.getPropStandard()
                                    + p.getPropStandardUnitName()
                                    + "（价税合计）")
                            .collect(Collectors.joining("、"))
                            + "，物业服务费递增率为"
                            + StrUtil.emptyToDefault(Convert.toStr(propIncrementalConfigList.get(0).getIncrease()), "0")
                            + "%/"
                            + (KeywordConst.ONE.equals(propIncrementalConfigList.get(0).getTimePoint()) ?
                            "" : propIncrementalConfigList.get(0).getTimePoint().toString())
                            + propIncrementalConfigList.get(0).getUnitName()
                            + "。";
                }
                resultMap.put("cashPledges", cashPledges);
            } else {
                String cashPledges = productList.stream().map(p -> "物业服务费单价为" + p.getPropStandard() + p.getPropStandardUnitName() + "（价税合计）").collect(Collectors.joining("、")) + "，物业服务费递增率0%/年。";
                resultMap.put("cashPledges", cashPledges);
            }
        } else {
            resultMap.put("cashPledges", "");
        }
    }

    /**
     * 赋值押金信息到预览信息实体
     *
     * @param resultMap    预览信息map集
     * @param previewBills 试算结果
     */
    private void setCashPledgeInfoToPreviewInfo(Map<String, Object> resultMap,
                                                ChargeRespondVo<PreviewBillsResultVo> previewBills) {
        if (previewBills != null && StrUtil.equals(previewBills.getCode(), "00000") &&
                CollectionUtils.isNotEmpty(previewBills.getData().getDepositList())) {
            List<Depos> depositList = previewBills.getData().getDepositList();
            if (depositList.get(0).getPayableMoney() != null) {
                resultMap.put("cashPledgePrice", depositList.get(0).getPayableMoney());
                resultMap.put("cashPledgePricebig", MoneyToChineseUtils.convert(depositList.get(0).getPayableMoney().doubleValue()));
            } else {
                resultMap.put("cashPledgePrice", "");
                resultMap.put("cashPledgePricebig", "");
            }
            List<Map<String, Object>> syYjList = new ArrayList<>();
            depositList.forEach(r -> {
                Map<String, Object> yjMap = new HashMap<>();
                yjMap.put("syYjList-chargePeriod", r.getChargePeriod());
                yjMap.put("syYjList-payTime", r.getChargeStartDate());
                yjMap.put("syYjList-rentPeriod", r.getChargeStartDate() + "-" + r.getChargeEndDate());
                yjMap.put("syYjList-houseAddr", r.getHouseName());
                yjMap.put("syYjList-paramprice", r.getParamprice());
                yjMap.put("syYjList-paramarea", r.getParamarea());
                if (StrUtil.equals(signInfoVo.getCashPledgeCode(), "9")) {
                    yjMap.put("syYjList-months", "--");
                } else {
                    yjMap.put("syYjList-months", r.getMonths());
                }
                yjMap.put("syYjList-payableMoney", r.getPayableMoney());
                yjMap.put("syYjList-remark", r.getNotes());
                syYjList.add(yjMap);
            });
            resultMap.put("syYjList", syYjList);
        } else {
            List<Map<String, Object>> syYjList = new ArrayList<>();
            Map<String, Object> yjMap = new HashMap<>();
            yjMap.put("syYjList-chargePeriod", "");
            yjMap.put("syYjList-payTime", "");
            yjMap.put("syYjList-rentPeriod", "");
            yjMap.put("syYjList-houseAddr", "");
            yjMap.put("syYjList-paramprice", "");
            yjMap.put("syYjList-paramarea", "");
            yjMap.put("syYjList-months", "");
            yjMap.put("syYjList-payableMoney", "");
            yjMap.put("syYjList-remark", "");
            syYjList.add(yjMap);
            resultMap.put("syYjList", syYjList);
            resultMap.put("cashPledgePrice", "");
            resultMap.put("cashPledgePricebig", "");
        }
    }

    /**
     * 赋值物业费信息到预览信息实体
     *
     * @param resultMap    预览信息map集
     * @param previewBills 试算结果
     */
    private void setPropInfoToPreviewInfo(Map<String, Object> resultMap,
                                          ChargeRespondVo<PreviewBillsResultVo> previewBills) {
        if (previewBills != null && StrUtil.equals(previewBills.getCode(), "00000") &&
                CollectionUtils.isNotEmpty(previewBills.getData().getPropertyList())) {
            List<Depos> propertyList = previewBills.getData().getPropertyList();
            List<Map<String, Object>> syPfList = new ArrayList<>();
            propertyList.forEach(p -> {
                Map<String, Object> pfMap = new HashMap<>();
                pfMap.put("syPfList-chargePeriod", p.getChargePeriod());
                pfMap.put("syPfList-payTime", p.getChargeStartDate());
                pfMap.put("syPfList-rentPeriod", p.getChargeStartDate() + "-" + p.getChargeEndDate());
                pfMap.put("syPfList-propertyParamprice", p.getParamprice());
                pfMap.put("syPfList-paramarea", p.getParamarea());
                pfMap.put("syPfList-months", p.getMonths());
                pfMap.put("syPfList-payableMoney", p.getPayableMoney());
                pfMap.put("syPfList-taxRate", p.getTaxRate());
                pfMap.put("syPfList-excludingRateMoney", p.getExcludingRateMoney());
                pfMap.put("syPfList-rateMoney", p.getRateMoney());
                pfMap.put("syPfList-remark", p.getNotes());
                syPfList.add(pfMap);
            });
            resultMap.put("syPfList", syPfList);
        }
    }

    /**
     * 赋值租金信息到预览信息实体
     *
     * @param resultMap    预览信息map集
     * @param previewBills 试算结果
     */
    private void setRentInfoToPreviewInfo(Map<String, Object> resultMap,
                                          ChargeRespondVo<PreviewBillsResultVo> previewBills) {
        if (previewBills != null && StrUtil.equals(previewBills.getCode(), "00000") &&
                CollectionUtils.isNotEmpty(previewBills.getData().getRentList())) {
            List<Depos> rentList = previewBills.getData().getRentList();
            List<Map<String, Object>> syZjList = new ArrayList<>();
            rentList.forEach(d -> {
                Map<String, Object> zjMap = new HashMap<>();
                zjMap.put("syZjList-chargePeriod", d.getChargePeriod());
                zjMap.put("syZjList-payTime", d.getChargeStartDate());
                zjMap.put("syZjList-rentPeriod", d.getChargeStartDate() + "-" + d.getChargeEndDate());
                zjMap.put("syZjList-paramprice", d.getParamprice());
                zjMap.put("syZjList-paramarea", d.getParamarea());
                zjMap.put("syZjList-dayMoney", d.getDayMoney());
                zjMap.put("syZjList-days", d.getDays());
                zjMap.put("syZjList-payableMoney", d.getPayableMoney());
                zjMap.put("syZjList-taxRate", d.getTaxRate());
                zjMap.put("syZjList-excludingRateMoney", d.getExcludingRateMoney());
                zjMap.put("syZjList-rateMoney", d.getRateMoney());
                zjMap.put("syZjList-remark", d.getNotes());
                syZjList.add(zjMap);
            });
            resultMap.put("syZjList", syZjList);
        }
    }

    /**
     * 赋值app信息到预览信息实体
     *
     * @param resultMap 预览信息map集
     * @param appFlag   app标签
     */
    private void setAppInfoToPreviewInfo(Map<String, Object> resultMap,
                                         boolean appFlag) {
        if (appFlag) {
            Date current = DateUtil.date();
            resultMap.put("signYear", DateUtil.year(current));
            resultMap.put("signMonth", DateUtil.month(current) + 1);
            resultMap.put("signDay", DateUtil.dayOfMonth(current));
        }
    }

    /**
     * 赋值附件信息到预览信息实体
     *
     * @param resultMap  预览信息map集
     * @param customerVo 客户信息 vo实体
     */
    private void setAttachmentInfoToPreviewInfo(Map<String, Object> resultMap,
                                                BbsResultCustomerVo customerVo) {
        //附件：租赁房屋平面图
        resultMap.put("imgFile_house", "");
        //附件：乙方营业执照复印件/身份证复印件
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("previewAndDownload>getIntentionFileByIntentionId入参:{}", customerVo.getIntentionId());
        AppReply<List<String>> idCardJson = feignServiceConfiguration
                .getSystemCommercialFeignClient()
                .getIntentionFileByIntentionId(customerVo.getIntentionId());
        stopWatch.stop();
        log.info("previewAndDownload>getIntentionFileByIntentionId返回:{},{}", JSON.toJSONString(idCardJson), stopWatch.getTotalTimeMillis());
        if (idCardJson != null && AppReply.SUCCESS_CODE.equals(idCardJson.getCode()) && idCardJson.getData() != null) {
            stopWatch.start();
            List<String> idCardList = idCardJson.getData();
            idCardList = idCardList.stream().flatMap(d -> Arrays.stream(d.split(","))).collect(Collectors.toList());
            for (int i = 1; i <= idCardList.size(); i++) {
                AppReply<String> fileAppreply = feignServiceConfiguration
                        .getFileFeignClient()
                        .downloadFileBase64(idCardList.get(i - 1));
                resultMap.put("imgFile_idCard" + i, fileAppreply.getData());
            }
            stopWatch.stop();
            log.info("previewAndDownload>imgFile_idCard执行耗时:{}", stopWatch.getTotalTimeMillis());
        }
    }

    /**
     * 赋值水印信息到预览信息实体
     *
     * @param contractManagementVo 合同信息 vo实体
     * @param watermarkType        水印类型
     * @param templateSeatMap      模板属性map集
     */
    private void setWatermarkInfoToPreviewInfo(BbctContractManagementVo contractManagementVo,
                                               String watermarkType,
                                               Map<String, String> templateSeatMap) {
        if (WhetherEnum.YES.getCode().equals(watermarkType)) {
            if (StrUtil.isNotBlank(signInfoVo.getContractWatermark())) {
                if ("1,2".equals(signInfoVo.getContractWatermark()) || "2,1".equals(signInfoVo.getContractWatermark())) {
                    if (!StringUtils.isEmpty(templateSeatMap.get("firstLogoId"))) {
                        WaterMarkVo waterMarkVo2 = new WaterMarkVo();
                        waterMarkVo2.setWaterType("2");
                        waterMarkVo2.setOffsetX(220);
                        waterMarkVo2.setOffsetY(430);
                        waterMarkVo2.setPicturefileId(templateSeatMap.get("firstLogoId"));
                        waterMarkVo2.setHeight(50);
                        contractManagementVo.addWaterMarkVo(waterMarkVo2);
                    }
                    WaterMarkVo waterMarkVo1 = new WaterMarkVo();
                    waterMarkVo1.setWaterType("1");
                    waterMarkVo1.setOffsetX(200);
                    waterMarkVo1.setOffsetY(340);
                    waterMarkVo1.setText(templateSeatMap.get("lessorName"));
                    waterMarkVo1.setTextSize(20);
                    contractManagementVo.addWaterMarkVo(waterMarkVo1);
                    WaterMarkVo waterMarkVo4 = new WaterMarkVo();
                    waterMarkVo4.setWaterType("2");
                    waterMarkVo4.setOffsetX(270);
                    waterMarkVo4.setOffsetY(260);
                    waterMarkVo4.setPicturefileId(signInfoVo.getSecondWartmarkLogoId());
                    waterMarkVo4.setHeight(50);
                    contractManagementVo.addWaterMarkVo(waterMarkVo4);
                    WaterMarkVo waterMarkVo3 = new WaterMarkVo();
                    waterMarkVo3.setWaterType("1");
                    waterMarkVo3.setOffsetX(250);
                    waterMarkVo3.setOffsetY(170);
                    waterMarkVo3.setText(signInfoVo.getSecondWartmarkName());
                    waterMarkVo3.setTextSize(20);
                    contractManagementVo.addWaterMarkVo(waterMarkVo3);
                }
                //甲方水印
                if ("1".equals(signInfoVo.getContractWatermark())) {
                    if (!StringUtils.isEmpty(templateSeatMap.get("firstLogoId"))) {
                        WaterMarkVo waterMarkVo2 = new WaterMarkVo();
                        waterMarkVo2.setWaterType("2");
                        waterMarkVo2.setOffsetX(220);
                        waterMarkVo2.setOffsetY(430);
                        waterMarkVo2.setPicturefileId(templateSeatMap.get("firstLogoId"));
                        waterMarkVo2.setHeight(50);
                        contractManagementVo.addWaterMarkVo(waterMarkVo2);
                    }
                    WaterMarkVo waterMarkVo1 = new WaterMarkVo();
                    waterMarkVo1.setWaterType("1");
                    waterMarkVo1.setOffsetX(200);
                    waterMarkVo1.setOffsetY(340);
                    waterMarkVo1.setText(templateSeatMap.get("lessorName"));
                    waterMarkVo1.setTextSize(20);
                    contractManagementVo.addWaterMarkVo(waterMarkVo1);
                }
                if ("2".equals(signInfoVo.getContractWatermark())) {
                    WaterMarkVo waterMarkVo2 = new WaterMarkVo();
                    waterMarkVo2.setWaterType("2");
                    waterMarkVo2.setOffsetX(220);
                    waterMarkVo2.setOffsetY(430);
                    waterMarkVo2.setPicturefileId(signInfoVo.getSecondWartmarkLogoId());
                    waterMarkVo2.setHeight(50);
                    contractManagementVo.addWaterMarkVo(waterMarkVo2);
                    WaterMarkVo waterMarkVo1 = new WaterMarkVo();
                    waterMarkVo1.setWaterType("1");
                    waterMarkVo1.setOffsetX(200);
                    waterMarkVo1.setOffsetY(340);
                    waterMarkVo1.setText(signInfoVo.getSecondWartmarkName());
                    waterMarkVo1.setTextSize(20);
                    contractManagementVo.addWaterMarkVo(waterMarkVo1);
                }
            }
        }
    }
}

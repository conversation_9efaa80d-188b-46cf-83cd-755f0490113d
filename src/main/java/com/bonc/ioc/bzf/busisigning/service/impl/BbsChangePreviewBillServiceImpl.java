package com.bonc.ioc.bzf.busisigning.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsChangePreviewBillEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsChangePreviewBillMapper;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsChangePreviewBillService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;

import com.bonc.ioc.common.base.page.PageResult;

/**
 * 预览账单表 服务类实现
 *
 * <AUTHOR>
 * @date 2024-10-30
 * @change 2024-10-30 by pyj for init
 */
@Slf4j
@Service
public class BbsChangePreviewBillServiceImpl extends McpBaseServiceImpl<BbsChangePreviewBillEntity> implements IBbsChangePreviewBillService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsChangePreviewBillMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsChangePreviewBillService baseService;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsChangePreviewBillVo vo) {
        if (vo == null) {
            return null;
        }

        BbsChangePreviewBillEntity entity = new BbsChangePreviewBillEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setBillId(null);
        if (!baseService.insert(entity)) {
            log.error("预览账单表新增失败:" + entity.toString());
            throw new McpException("预览账单表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getBillId(), 1)) {
                log.error("预览账单表新增后保存历史失败:" + entity.toString());
                throw new McpException("预览账单表新增后保存历史失败");
            }

            log.debug("预览账单表新增成功:" + entity.getBillId());
            return entity.getBillId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsChangePreviewBillVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsChangePreviewBillEntity> entityList = new ArrayList<>();
        for (BbsChangePreviewBillVo item : voList) {
            BbsChangePreviewBillEntity entity = new BbsChangePreviewBillEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsChangePreviewBillEntity item : entityList) {
            item.setBillId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("预览账单表新增失败");
            throw new McpException("预览账单表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbsChangePreviewBillEntity::getBillId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("预览账单表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("预览账单表批量新增后保存历史失败");
            }

            log.debug("预览账单表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param billId 需要删除的账单id
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String billId) {
        if (!StringUtils.isEmpty(billId)) {
            if (!baseService.saveOperationHisById(billId, 3)) {
                log.error("预览账单表删除后保存历史失败:" + billId);
                throw new McpException("预览账单表删除后保存历史失败");
            }

            if (!baseService.removeById(billId)) {
                log.error("预览账单表删除失败");
                throw new McpException("预览账单表删除失败" + billId);
            }
        } else {
            throw new McpException("预览账单表删除失败账单id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param billIdList 需要删除的账单id
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> billIdList) {
        if (!CollectionUtils.isEmpty(billIdList)) {
            int oldSize = billIdList.size();
            billIdList = billIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(billIdList) || oldSize != billIdList.size()) {
                throw new McpException("预览账单表批量删除失败 存在主键id为空的记录" + StringUtils.join(billIdList));
            }

            if (!baseService.saveOperationHisByIds(billIdList, 3)) {
                log.error("预览账单表批量删除后保存历史失败:" + StringUtils.join(billIdList));
                throw new McpException("预览账单表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(billIdList)) {
                log.error("预览账单表批量删除失败");
                throw new McpException("预览账单表批量删除失败" + StringUtils.join(billIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的预览账单表
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsChangePreviewBillVo vo) {
        if (vo != null) {
            BbsChangePreviewBillEntity entity = new BbsChangePreviewBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getBillId())) {
                throw new McpException("预览账单表更新失败传入账单id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("预览账单表更新失败");
                throw new McpException("预览账单表更新失败" + entity.getBillId());
            } else {
                if (!baseService.saveOperationHisById(entity.getBillId(), 2)) {
                    log.error("预览账单表更新后保存历史失败:" + entity.getBillId());
                    throw new McpException("预览账单表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("预览账单表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的预览账单表
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsChangePreviewBillVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsChangePreviewBillEntity> entityList = new ArrayList<>();

            for (BbsChangePreviewBillVo item : voList) {
                BbsChangePreviewBillEntity entity = new BbsChangePreviewBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getBillId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("预览账单表批量更新失败 存在账单id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("预览账单表批量更新失败");
                throw new McpException("预览账单表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getBillId())).map(BbsChangePreviewBillEntity::getBillId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("预览账单表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("预览账单表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的预览账单表
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsChangePreviewBillVo vo) {
        if (vo != null) {
            BbsChangePreviewBillEntity entity = new BbsChangePreviewBillEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("预览账单表保存失败");
                throw new McpException("预览账单表保存失败" + entity.getBillId());
            } else {
                if (!baseService.saveOperationHisById(entity.getBillId(), 4)) {
                    log.error("预览账单表保存后保存历史失败:" + entity.getBillId());
                    throw new McpException("预览账单表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("预览账单表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的预览账单表
     * @return void
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsChangePreviewBillVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsChangePreviewBillEntity> entityList = new ArrayList<>();

            for (BbsChangePreviewBillVo item : voList) {
                BbsChangePreviewBillEntity entity = new BbsChangePreviewBillEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("预览账单表批量保存失败");
                throw new McpException("预览账单表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getBillId())).map(BbsChangePreviewBillEntity::getBillId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("预览账单表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("预览账单表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param billId 需要查询的账单id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsChangePreviewBillVo selectByIdRecord(String billId) {
        BbsChangePreviewBillVo vo = new BbsChangePreviewBillVo();

        if (!StringUtils.isEmpty(billId)) {
            BbsChangePreviewBillEntity entity = baseService.selectById(billId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-10-30
     * @change 2024-10-30 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsChangePreviewBillPageResultVo>> selectByPageRecord(BbsChangePreviewBillPageVo vo) {
        List<BbsChangePreviewBillPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据合同变更id和账单类型查询
     *
     * @param ccId     合同变更id
     * @param billType 账单类型
     * @return 账单信息列表
     */
    @Override
    public List<BbsChangePreviewBillVo> selectByCcIdAndBillType(String ccId, String billType) {
        return baseMapper.selectByCcIdAndBillType(ccId, billType);
    }
}

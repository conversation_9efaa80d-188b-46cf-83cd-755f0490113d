package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 人-产品：关系表 实体类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by l<PERSON><PERSON><PERSON><PERSON> for init
 */
@TableName("bbs_result_relation")
@ApiModel(value = "BbsResultRelationEntity对象", description = "人-产品：关系表")
public class BbsResultRelationEntity extends McpBaseEntity implements Serializable {

    public static final String FIELD_RR_ID = "rr_id";
    public static final String FIELD_SOURCE = "source";
    public static final String FIELD_SIGN_INFO_ID = "sign_info_id";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "rr_id", type = IdType.ASSIGN_UUID)
    private String rrId;

    /**
     * 来源:1：选房中心 ，2：入住中心，3、配租中心，4、续租中心
     */
    @ApiModelProperty(value = "来源:1：选房中心 ，2：入住中心，3、配租中心，4、续租中心")
    private Integer source;

    /**
     * 签约结果ID
     */
    @ApiModelProperty(value = "签约结果ID")
    private String signInfoId;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * @return 主键
     */
    public String getRrId() {
        return rrId;
    }

    public void setRrId(String rrId) {
        this.rrId = rrId;
    }

    /**
     * @return 来源:1：选房中心 ，2：入住中心，3、配租中心，4、续租中心
     */
    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * @return 签约结果ID
     */
    public String getSignInfoId() {
        return signInfoId;
    }

    public void setSignInfoId(String signInfoId) {
        this.signInfoId = signInfoId;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "BbsResultRelationEntity{" +
                "rrId=" + rrId +
                ", source=" + source +
                ", signInfoId=" + signInfoId +
                ", delFlag=" + delFlag +
                "}";
    }
}
package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 签约结果明细表-v3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-12-01
 * @change 2022-12-01 by ly for init
 */
@TableName("bbsi_rule_result_info")
@ApiModel(value="BbsiRuleResultInfoEntity对象", description="签约结果明细表-v3.0")
public class BbsiRuleResultInfoEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_RESULT_ID = "result_id";
    public static final String FIELD_RULE_ID = "rule_id";
    public static final String FIELD_ONLINE_ID = "online_id";
    public static final String FIELD_PROJECT_NAME = "project_name";
    public static final String FIELD_PROJECT_ID = "project_id";
    public static final String FIELD_XM_NAME = "xm_name";
    public static final String FIELD_SEX = "sex";
    public static final String FIELD_CERTIFICATE_TYPE = "certificate_type";
    public static final String FIELD_CERTIFICATE_NUM = "certificate_num";
    public static final String FIELD_HOUSE_RESOURCE = "house_resource";
    public static final String FIELD_SIGN_TIME = "sign_time";
    public static final String FIELD_CONTRACT_ID = "contract_id";
    public static final String FIELD_CONTRACT_CODE = "contract_code";
    public static final String FIELD_RENT_BEGIN_TIME = "rent_begin_time";
    public static final String FIELD_RENT_END_TIME = "rent_end_time";
    public static final String FIELD_PAYMENT_CYCLE_CODE = "payment_cycle_code";
    public static final String FIELD_RENT = "rent";
    public static final String FIELD_SIGN_STATUS = "sign_status";
    public static final String FIELD_EXAMINE_STATUS = "examine_status";
    public static final String FIELD_SEL_RESULT = "sel_result";
    public static final String FIELD_NOTICE_STATUS = "notice_status";
    public static final String FIELD_NOTICE_RESULT = "notice_result";
    public static final String FIELD_NOTICE_RESULT_ID = "notice_result_id";
    public static final String FIELD_MESSAGE_STATUS = "message_status";
    public static final String FIELD_MESSAGE_RESULT_ID = "message_result_id";
    public static final String FIELD_DEL_FLAG = "del_flag";
    public static final String FIELD_CASH_PLEDGE_PRICE = "cash_pledge_price";
    public static final String FIELD_LESSOR_NAME ="lessor_name";
    public static final String FIELD_UNIFIED_CREDIT_CODE ="unified_credit_code";
    public static final String FIELD_FIRST_PARTY_PHONE ="first_party_phone";
    public static final String FIELD_FIRST_PARTY_ADDRESS ="first_party_address";
    public static final String FIELD_FIRST_PARTY_POSTAL_CODE ="first_party_postal_code";
    public static final String FIELD_SECOND_PARTY_FILING_NO ="second_party_filing_no";
    public static final String FIELD_SECOND_PARTY_FIXED_TELEPHONE ="second_party_fixed_telephone";
    public static final String FIELD_SECOND_PARTY_WORK_UNIT ="second_party_work_unit";
    public static final String FIELD_SECOND_PARTY_POSTAL_CODE ="second_party_postal_code";
    public static final String FIELD_HOUSE_LOCATION ="house_location";
    public static final String FIELD_HOUSE_STREET ="house_street";
    public static final String FIELD_HOUSE_ESTATE_NO ="house_estate_no";
    public static final String FIELD_BUILDING_NO ="building_no";
    public static final String FIELD_HOUSE_UNIT ="house_unit";
    public static final String FIELD_HOUSE_NO ="house_no";
    public static final String FIELD_HOUSE_TYPE ="house_type";
    public static final String FIELD_HOUSE_AREA ="house_area";
    public static final String FIELD_TOTAL_LEASE_TERM ="total_lease_term";
    public static final String FIELD_RENT_STANDARD ="rent_standard";
    public static final String FIELD_SECOND_PARTY_COST ="second_party_cost";
    public static final String FIELD_TOTAL_CONTRACT_NUM ="total_contract_num";
    public static final String FIELD_FIRST_PARTY_CONTRACT_NUM ="first_party_contract_num";
    public static final String FIELD_SECOND_PARTY_CONTRACT_NUM ="second_party_contract_num";
    public static final String FIELD_CLIENT_NAME ="client_name";
    public static final String FIELD_CLIENT_TELEPHONE ="client_telephone";
    public static final String FIELD_HOUSE_LONGITUDE ="house_longitude";
    public static final String FIELD_HOUSE_LATITUDE ="house_latitude";

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
                                @TableId(value = "result_id", type = IdType.ASSIGN_UUID)
                                  private String resultId;

    /**
     * 签约规则id
     */
    @ApiModelProperty(value = "签约规则id")
                            private String ruleId;

    /**
     * 选房中心结果id
     */
    @ApiModelProperty(value = "选房中心结果id")
                            private String onlineId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
                            private String projectId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
                            private String xmName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
                            private String sex;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
                            private String certificateType;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
                            private String certificateNum;

    /**
     * 房屋地址
     */
    @ApiModelProperty(value = "房屋地址")
                            private String houseResource;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date signTime;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
                            private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 租赁开始日期
     */
    @ApiModelProperty(value = "租赁开始日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date rentBeginTime;

    /**
     * 租赁结束日期
     */
    @ApiModelProperty(value = "租赁结束日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date rentEndTime;

    /**
     * 缴费周期
     */
    @ApiModelProperty(value = "缴费周期")
                            private String paymentCycleCode;

    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
                            private Double rent;

    /**
     * 待签约、已签约、未签约
     */
    @ApiModelProperty(value = "待签约、已签约、未签约")
                            private String signStatus;

    /**
     * 待审核，未通过，已通过
     */
    @ApiModelProperty(value = "待审核，未通过，已通过")
                            private String examineStatus;

    /**
     * 选房结果
     */
    @ApiModelProperty(value = "选房结果")
                            private String selResult;

    /**
     * 签约通知发送状态编码
     */
    @ApiModelProperty(value = "签约通知发送状态编码")
                            private String noticeStatus;

    /**
     * 签约通知_通知消息结果编码
     */
    @ApiModelProperty(value = "签约通知_通知消息结果编码")
                            private String noticeResult;

    /**
     * 签约通知_通知消息记录ID
     */
    @ApiModelProperty(value = "签约通知_通知消息记录ID")
                            private String noticeResultId;

    /**
     * 签约通知_短信消息结果编码
     */
    @ApiModelProperty(value = "签约通知_短信消息结果编码")
                            private String messageStatus;

    /**
     * 签约通知_短信消息记录ID
     */
    @ApiModelProperty(value = "签约通知_短信消息记录ID")
                            private String messageResultId;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 押金金额
     */
    @ApiModelProperty(value = "押金金额")
                            private Double cashPledgePrice;


    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 出租人(单位)名称
     * @return
     */
    @ApiModelProperty(value = "出租人(单位)名称")
    private String lessorName;

    /**
     * 统一信用代码
     * @return
     */
    @ApiModelProperty(value = "统一信用代码")
    private String unifiedCreditCode;

    /**
     * 项目(小区)服务站联系电话
     * @return
     */
    @ApiModelProperty(value = "项目(小区)服务站联系电话")
    private String firstPartyPhone;

    /**
     * 甲方通讯地址
     * @return
     */
    @ApiModelProperty(value = "甲方通讯地址")
    private String firstPartyAddress;

    /**
     * 甲方邮编
     * @return
     */
    @ApiModelProperty(value = "甲方邮编")
    private String firstPartyPostalCode;

    /**
     * 乙方备案号
     * @return
     */
    @ApiModelProperty(value = "乙方备案号")
    private String secondPartyFilingNo;

    /**
     * 乙方固定电话
     * @return
     */
    @ApiModelProperty(value = "乙方固定电话")
    private String secondPartyFixedTelephone;

    /**
     * 乙方工作单位
     * @return
     */
    @ApiModelProperty(value = "乙方工作单位")
    private String secondPartyWorkUnit;
    /**
     * 乙方通讯地址
     */
    @ApiModelProperty(value = "乙方通讯地址")
    private String secondPartyAddress;
    /**
     * 乙方邮编
     * @return
     */
    @ApiModelProperty(value = "乙方邮编")
    private String secondPartyPostalCode;

    /**
     * 房屋所在区
     * @return
     */
    @ApiModelProperty(value = "房屋所在区")
    private String houseLocation;

    /**
     * 房屋街道(路)
     * @return
     */
    @ApiModelProperty(value = "房屋街道(路)")
    private String houseStreet;

    /**
     * 小区号
     * @return
     */
    @ApiModelProperty(value = "小区号")
    private String houseEstateNo;

    /**
     * 楼栋号
     * @return
     */
    @ApiModelProperty(value = "楼栋号")
    private String buildingNo;


    /**
     * 楼栋号
     * @return
     */
    @ApiModelProperty(value = "楼栋号")
    private String buildingName;

    /**
     * 单元
     * @return
     */
    @ApiModelProperty(value = "单元")
    private String houseUnit;
    /**
     * 单元
     * @return
     */
    @ApiModelProperty(value = "单元")
    private String houseUnitName;
    /**
     * 房屋号
     * @return
     */
    @ApiModelProperty(value = "房屋号")
    private String houseNo;

    /**
     * 房屋号
     * @return
     */
    @ApiModelProperty(value = "房屋号")
    private String houseName;

    /**
     * 户型
     * @return
     */
    @ApiModelProperty(value = "户型")
    private String houseType;

    /**
     * 建筑面积
     * @return
     */
    @ApiModelProperty(value = "建筑面积")
    private String houseArea;

    /**
     * 租赁总期限(月)
     * @return
     */
    @ApiModelProperty(value = "租赁总期限(月)")
    private String totalLeaseTerm;

    /**
     * 租金标准(元/平方米·月)
     * @return
     */
    @ApiModelProperty(value = "租金标准(元/平方米·月)")
    private String rentStandard;

    /**
     * 由乙方承担的费用项
     * @return
     */
    @ApiModelProperty(value = "由乙方承担的费用项")
    private String secondPartyCost;

    /**
     * 合同总份数
     * @return
     */
    @ApiModelProperty(value = "合同总份数")
    private String totalContractNum;

    /**
     * 甲方持合同份数
     * @return
     */
    @ApiModelProperty(value = "甲方持合同份数")
    private String firstPartyContractNum;

    /**
     * 乙方持合同份数
     * @return
     */
    @ApiModelProperty(value = "乙方持合同份数")
    private String secondPartyContractNum;

    /**
     * 委托人姓名
     * @return
     */
    @ApiModelProperty(value = "委托人姓名")
    private String clientName;

    /**
     * 委托人联系电话
     * @return
     */
    @ApiModelProperty(value = "委托人联系电话")
    private String clientTelephone;

    /**
     * 小区经度
     * @return
     */
    @ApiModelProperty(value = "小区经度")
    private String houseLongitude;

    /**
     * 小区纬度
     * @return
     */
    @ApiModelProperty(value = "小区纬度")
    private String houseLatitude;

    @ApiModelProperty(value = "客户id")
    private String customId;

    /**
     * 客户手机号
     */
    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;
    @ApiModelProperty(value = "配租计划id")
    private String peizuPlanId;


    @ApiModelProperty(value = "配租计划名称")
    private String peizuPlanName;

    @ApiModelProperty(value = "房源id")
    private String assetId;
    @ApiModelProperty(value = "审核人id")
    private String examineId;


    @ApiModelProperty(value = "房源id")
    private String houseId;

    @ApiModelProperty(value = "楼层")
    private String houseFloor;

    /**
     * 消息是否发送 1-未发送 2已发送
     */
    @ApiModelProperty(value = "消息是否发送 1-未发送 2已发送")
    private String sendState;


    @ApiModelProperty(value = "人房关系id")
    private String relationId;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id")
    private String communityId;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区名称")
    private String communityName;

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getSendState() {
        return sendState;
    }

    public void setSendState(String sendState) {
        this.sendState = sendState;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getHouseUnitName() {
        return houseUnitName;
    }

    public void setHouseUnitName(String houseUnitName) {
        this.houseUnitName = houseUnitName;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getHouseFloor() {
        return houseFloor;
    }

    public void setHouseFloor(String houseFloor) {
        this.houseFloor = houseFloor;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }


    public String getExamineId() {
        return examineId;
    }

    public void setExamineId(String examineId) {
        this.examineId = examineId;
    }

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getPeizuPlanId() {
        return peizuPlanId;
    }

    public void setPeizuPlanId(String peizuPlanId) {
        this.peizuPlanId = peizuPlanId;
    }

    public String getPeizuPlanName() {
        return peizuPlanName;
    }

    public void setPeizuPlanName(String peizuPlanName) {
        this.peizuPlanName = peizuPlanName;
    }

    public String getCustomId() {
        return customId;
    }

    public void setCustomId(String customId) {
        this.customId = customId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * @return 主键id
     */
    public String getResultId() {
        return resultId;
    }

    public void setResultId(String resultId) {
        this.resultId = resultId;
    }

    /**
     * @return 签约规则id
     */
    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    /**
     * @return 选房中心结果id
     */
    public String getOnlineId() {
        return onlineId;
    }

    public void setOnlineId(String onlineId) {
        this.onlineId = onlineId;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 项目id
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 姓名
     */
    public String getXmName() {
        return xmName;
    }

    public void setXmName(String xmName) {
        this.xmName = xmName;
    }

    /**
     * @return 性别
     */
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    /**
     * @return 证件类型
     */
    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    /**
     * @return 身份证号
     */
    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    /**
     * @return 房屋地址
     */
    public String getHouseResource() {
        return houseResource;
    }

    public void setHouseResource(String houseResource) {
        this.houseResource = houseResource;
    }

    /**
     * @return 签约时间
     */
    public Date getSignTime(){
        if(signTime!=null){
            return (Date)signTime.clone();
        }else{
            return null;
        }
    }

    public void setSignTime(Date signTime) {
        if(signTime==null){
            this.signTime = null;
        }else{
            this.signTime = (Date)signTime.clone();
        }
    }

    /**
     * @return 合同id
     */
    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 租赁开始日期
     */
    public Date getRentBeginTime(){
        if(rentBeginTime!=null){
            return (Date)rentBeginTime.clone();
        }else{
            return null;
        }
    }

    public void setRentBeginTime(Date rentBeginTime) {
        if(rentBeginTime==null){
            this.rentBeginTime = null;
        }else{
            this.rentBeginTime = (Date)rentBeginTime.clone();
        }
    }

    /**
     * @return 租赁结束日期
     */
    public Date getRentEndTime(){
        if(rentEndTime!=null){
            return (Date)rentEndTime.clone();
        }else{
            return null;
        }
    }

    public void setRentEndTime(Date rentEndTime) {
        if(rentEndTime==null){
            this.rentEndTime = null;
        }else{
            this.rentEndTime = (Date)rentEndTime.clone();
        }
    }

    /**
     * @return 缴费周期
     */
    public String getPaymentCycleCode() {
        return paymentCycleCode;
    }

    public void setPaymentCycleCode(String paymentCycleCode) {
        this.paymentCycleCode = paymentCycleCode;
    }

    /**
     * @return 租金
     */
    public Double getRent() {
        return rent;
    }

    public void setRent(Double rent) {
        this.rent = rent;
    }

    /**
     * @return 待签约、已签约、未签约
     */
    public String getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(String signStatus) {
        this.signStatus = signStatus;
    }

    /**
     * @return 待审核，未通过，已通过
     */
    public String getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(String examineStatus) {
        this.examineStatus = examineStatus;
    }

    /**
     * @return 选房结果
     */
    public String getSelResult() {
        return selResult;
    }

    public void setSelResult(String selResult) {
        this.selResult = selResult;
    }

    /**
     * @return 签约通知发送状态编码
     */
    public String getNoticeStatus() {
        return noticeStatus;
    }

    public void setNoticeStatus(String noticeStatus) {
        this.noticeStatus = noticeStatus;
    }

    /**
     * @return 签约通知_通知消息结果编码
     */
    public String getNoticeResult() {
        return noticeResult;
    }

    public void setNoticeResult(String noticeResult) {
        this.noticeResult = noticeResult;
    }

    /**
     * @return 签约通知_通知消息记录ID
     */
    public String getNoticeResultId() {
        return noticeResultId;
    }

    public void setNoticeResultId(String noticeResultId) {
        this.noticeResultId = noticeResultId;
    }

    /**
     * @return 签约通知_短信消息结果编码
     */
    public String getMessageStatus() {
        return messageStatus;
    }

    public void setMessageStatus(String messageStatus) {
        this.messageStatus = messageStatus;
    }

    /**
     * @return 签约通知_短信消息记录ID
     */
    public String getMessageResultId() {
        return messageResultId;
    }

    public void setMessageResultId(String messageResultId) {
        this.messageResultId = messageResultId;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 押金金额
     */
    public Double getCashPledgePrice() {
        return cashPledgePrice;
    }

    public void setCashPledgePrice(Double cashPledgePrice) {
        this.cashPledgePrice = cashPledgePrice;
    }

    public String getLessorName() {
        return lessorName;
    }

    public void setLessorName(String lessorName) {
        this.lessorName = lessorName;
    }

    public String getUnifiedCreditCode() {
        return unifiedCreditCode;
    }

    public void setUnifiedCreditCode(String unifiedCreditCode) {
        this.unifiedCreditCode = unifiedCreditCode;
    }

    public String getFirstPartyPhone() {
        return firstPartyPhone;
    }

    public void setFirstPartyPhone(String firstPartyPhone) {
        this.firstPartyPhone = firstPartyPhone;
    }

    public String getFirstPartyAddress() {
        return firstPartyAddress;
    }

    public void setFirstPartyAddress(String firstPartyAddress) {
        this.firstPartyAddress = firstPartyAddress;
    }

    public String getFirstPartyPostalCode() {
        return firstPartyPostalCode;
    }

    public void setFirstPartyPostalCode(String firstPartyPostalCode) {
        this.firstPartyPostalCode = firstPartyPostalCode;
    }

    public String getSecondPartyFilingNo() {
        return secondPartyFilingNo;
    }

    public void setSecondPartyFilingNo(String secondPartyFilingNo) {
        this.secondPartyFilingNo = secondPartyFilingNo;
    }

    public String getSecondPartyFixedTelephone() {
        return secondPartyFixedTelephone;
    }

    public void setSecondPartyFixedTelephone(String secondPartyFixedTelephone) {
        this.secondPartyFixedTelephone = secondPartyFixedTelephone;
    }


    public String getSecondPartyPostalCode() {
        return secondPartyPostalCode;
    }

    public void setSecondPartyPostalCode(String secondPartyPostalCode) {
        this.secondPartyPostalCode = secondPartyPostalCode;
    }

    public String getHouseLocation() {
        return houseLocation;
    }

    public void setHouseLocation(String houseLocation) {
        this.houseLocation = houseLocation;
    }

    public String getHouseStreet() {
        return houseStreet;
    }

    public void setHouseStreet(String houseStreet) {
        this.houseStreet = houseStreet;
    }

    public String getHouseEstateNo() {
        return houseEstateNo;
    }

    public void setHouseEstateNo(String houseEstateNo) {
        this.houseEstateNo = houseEstateNo;
    }

    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    public String getHouseUnit() {
        return houseUnit;
    }

    public void setHouseUnit(String houseUnit) {
        this.houseUnit = houseUnit;
    }

    public String getHouseNo() {
        return houseNo;
    }

    public void setHouseNo(String houseNo) {
        this.houseNo = houseNo;
    }

    public String getHouseType() {
        return houseType;
    }

    public void setHouseType(String houseType) {
        this.houseType = houseType;
    }

    public String getHouseArea() {
        return houseArea;
    }

    public void setHouseArea(String houseArea) {
        this.houseArea = houseArea;
    }

    public String getTotalLeaseTerm() {
        return totalLeaseTerm;
    }

    public void setTotalLeaseTerm(String totalLeaseTerm) {
        this.totalLeaseTerm = totalLeaseTerm;
    }

    public String getRentStandard() {
        return rentStandard;
    }

    public void setRentStandard(String rentStandard) {
        this.rentStandard = rentStandard;
    }

    public String getSecondPartyCost() {
        return secondPartyCost;
    }

    public void setSecondPartyCost(String secondPartyCost) {
        this.secondPartyCost = secondPartyCost;
    }

    public String getTotalContractNum() {
        return totalContractNum;
    }

    public void setTotalContractNum(String totalContractNum) {
        this.totalContractNum = totalContractNum;
    }

    public String getFirstPartyContractNum() {
        return firstPartyContractNum;
    }

    public void setFirstPartyContractNum(String firstPartyContractNum) {
        this.firstPartyContractNum = firstPartyContractNum;
    }

    public String getSecondPartyContractNum() {
        return secondPartyContractNum;
    }

    public void setSecondPartyContractNum(String secondPartyContractNum) {
        this.secondPartyContractNum = secondPartyContractNum;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientTelephone() {
        return clientTelephone;
    }

    public void setClientTelephone(String clientTelephone) {
        this.clientTelephone = clientTelephone;
    }

    public String getHouseLongitude() {
        return houseLongitude;
    }

    public void setHouseLongitude(String houseLongitude) {
        this.houseLongitude = houseLongitude;
    }

    public String getHouseLatitude() {
        return houseLatitude;
    }

    public void setHouseLatitude(String houseLatitude) {
        this.houseLatitude = houseLatitude;
    }

    public String getSecondPartyAddress() {
        return secondPartyAddress;
    }

    public void setSecondPartyAddress(String secondPartyAddress) {
        this.secondPartyAddress = secondPartyAddress;
    }

    @Override
    public String toString() {
        return "BbsiRuleResultInfoEntity{" +
            "resultId=" + resultId +
            ", ruleId=" + ruleId +
            ", onlineId=" + onlineId +
            ", projectName=" + projectName +
            ", projectId=" + projectId +
            ", xmName=" + xmName +
            ", sex=" + sex +
            ", certificateType=" + certificateType +
            ", certificateNum=" + certificateNum +
            ", houseResource=" + houseResource +
            ", signTime=" + signTime +
            ", contractId=" + contractId +
            ", contractCode=" + contractCode +
            ", rentBeginTime=" + rentBeginTime +
            ", rentEndTime=" + rentEndTime +
            ", paymentCycleCode=" + paymentCycleCode +
            ", rent=" + rent +
            ", signStatus=" + signStatus +
            ", examineStatus=" + examineStatus +
            ", selResult=" + selResult +
            ", noticeStatus=" + noticeStatus +
            ", noticeResult=" + noticeResult +
            ", noticeResultId=" + noticeResultId +
            ", messageStatus=" + messageStatus +
            ", messageResultId=" + messageResultId +
            ", delFlag=" + delFlag +
            ", cashPledgePrice=" + cashPledgePrice +
                ", lessorName=" + lessorName +
                ", unifiedCreditCode=" + unifiedCreditCode +
                ", firstPartyPhone=" + firstPartyPhone +
                ", firstPartyAddress=" + firstPartyAddress +
                ", firstPartyPostalCode=" + firstPartyPostalCode +
                ", secondPartyFilingNo=" + secondPartyFilingNo +
                ", secondPartyFixedTelephone=" + secondPartyFixedTelephone +
                ", seconPartyWorkUnit=" + secondPartyWorkUnit +
                ", secondPartyPostalCode=" + secondPartyPostalCode +
                ", houseLocation=" + houseLocation +
                ", houseStreet=" + houseStreet +
                ", houseEstateNo=" + houseEstateNo +
                ", buildingNo=" + buildingNo +
                ", houseUnit=" + houseUnit +
                ", houseNo=" + houseNo +
                ", houseType=" + houseType +
                ", houseArea=" + houseArea +
                ", totalLeaseTerm=" + totalLeaseTerm +
                ", rentStandard=" + rentStandard +
                ", secondPartyCost=" + secondPartyCost +
                ", totalContractNum=" + totalContractNum +
                ", firstPartyContractNum=" + firstPartyContractNum +
                ", secondPartyContractNum=" + secondPartyContractNum +
                ", clientName=" + clientName +
                ", clientTelephone=" + clientTelephone +
                ", houseLongitude=" + houseLongitude +
                ", houseLatitude=" + houseLatitude +
        "}";
    }

    public String getSecondPartyWorkUnit() {
        return secondPartyWorkUnit;
    }

    public void setSecondPartyWorkUnit(String secondPartyWorkUnit) {
        this.secondPartyWorkUnit = secondPartyWorkUnit;
    }
}

package com.bonc.ioc.bzf.busisigning.factory.sign.otherchange;

import cn.hutool.extra.spring.SpringUtil;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.consts.SymbolConst;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.utils.ListUtil;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 子变更签约 抽象工厂类
 *
 * <AUTHOR>
 * @since 2024/11/4
 */
@Slf4j
public abstract class AbstractSubChangeSignFactory {

    /**
     * feign服务 配置实例
     */
    protected FeignServiceConfiguration feignServiceConfiguration;

    /**
     * 业务服务 配置实例
     */
    protected BusinessServiceConfiguration businessServiceConfiguration;

    /**
     * 签约信息 vo实体实例
     */
    protected BbsSignInfoVo signInfoVo;

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param signInfoVo                   签约信息 vo实体
     */
    protected AbstractSubChangeSignFactory(FeignServiceConfiguration feignServiceConfiguration,
                                           BusinessServiceConfiguration businessServiceConfiguration,
                                           BbsSignInfoVo signInfoVo) {
        this.feignServiceConfiguration = feignServiceConfiguration;
        this.businessServiceConfiguration = businessServiceConfiguration;
        this.signInfoVo = signInfoVo;
    }

    /**
     * 获取实例
     *
     * @param signInfoVo 签约信息 vo实体
     * @return 实例
     */
    public static List<AbstractSubChangeSignFactory> getInstances(BbsSignInfoVo signInfoVo) {
        // 获取bean
        FeignServiceConfiguration feignServiceConfiguration = SpringUtil.getBean(FeignServiceConfiguration.class);
        BusinessServiceConfiguration businessServiceConfiguration = SpringUtil.getBean(BusinessServiceConfiguration.class);
        List<AbstractSubChangeSignFactory> instanceList = new ArrayList<>();
        List<String> changeTypeItemList = ListUtil.splitBySymbol(signInfoVo.getProductSourceType(), SymbolConst.COMMA);
        if (changeTypeItemList.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())) {
            instanceList.add(new RentChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.BOND_CHANGE.getCode())) {
            instanceList.add(new CashPledgeChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.BUSINESS_FORMAT_CHANGE.getCode())) {
            instanceList.add(new BusinessFormatChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.LEASE_DATE_CHANGE.getCode())) {
            instanceList.add(new LeaseDateChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.SERVICE_TERMS_CHANGE.getCode())) {
            instanceList.add(new ServiceTermsChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.PAYABLE_DATE_CHANGE.getCode())) {
            instanceList.add(new PayableDateChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo));
        }
        if (changeTypeItemList.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())) {
            instanceList.add(new LeaseAreaReductionChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo));
        }
        return instanceList;
    }

    /**
     * 赋值预览信息map集
     *
     * @param previewInfoMap      预览信息map集
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    public abstract void setPreviewInfoMap(Map<String, Object> previewInfoMap,
                                           BbctPreviewInfoParamsVo previewInfoParamsVo);
}

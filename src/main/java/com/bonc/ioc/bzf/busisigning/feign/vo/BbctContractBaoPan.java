package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName BbctContractBaoPan
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2023-08-05 11:27
 **/
@ApiModel(value = "BbctContractBaoPanVo对象", description = "合同主表")
@Data
public class BbctContractBaoPan extends McpBasePageVo implements Serializable{

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "签约时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date passTime;

    @ApiModelProperty(value = "分类1")
    private String businessTypeCode1;

    @ApiModelProperty(value = "分类2")
    private String businessTypeCode2;

    @ApiModelProperty(value = "房源地址")
    private String houseAddress;

    @ApiModelProperty(value = "租户姓名")
    private String tenantName;

    @ApiModelProperty(value = "项目id")
    private String realProjectId;

    @ApiModelProperty(value = "租户电话")
    private String mobile;

    @ApiModelProperty(value = "小区名称")
    private String projectName;
}

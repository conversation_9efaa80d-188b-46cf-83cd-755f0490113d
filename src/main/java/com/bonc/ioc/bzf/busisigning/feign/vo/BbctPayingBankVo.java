package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "付款银行 vo实体", description = "付款银行 vo实体")
public class BbctPayingBankVo extends McpBaseVo implements Serializable {

    /**
     * 付款银行开户行
     */
    @ApiModelProperty(value = "付款银行开户行")
    private String payingBankNameCode;

    /**
     * 付款银行开户行名称
     */
    @ApiModelProperty(value = "付款银行开户行名称")
    private String payingBankName;

    /**
     * NCC银行类别编码
     */
    @ApiModelProperty(value = "NCC银行类别编码")
    private String nccBankCode;
}

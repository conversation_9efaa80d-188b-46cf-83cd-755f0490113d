package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 客户类型 枚举类
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
public enum CustomerTypeEnum {

    /**
     * 散户
     */
    SINGLE("00", "散户"),

    /**
     * 企业
     */
    COMPANY("01", "企业");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    CustomerTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    CustomerTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        CustomerTypeEnum[] enums = values();
        for (CustomerTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 合同邮件表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-25
 * @change 2023-05-25 by lyh for init
 */
@ApiModel(value="BbsContractEmailVo对象", description="合同邮件表")
public class BbsContractEmailVo extends McpBaseVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String emailId;

    /**
     * 邮件标题
     */
    @ApiModelProperty(value = "邮件标题")
                            private String emailTitle;

    /**
     * 邮件正文内容
     */
    @ApiModelProperty(value = "邮件正文内容")
                            private String emailContent;

    /**
     * 分类类型(1.合同  2.押金条)
     */
    @ApiModelProperty(value = "分类类型(1.合同  2.押金条)")
                            private String emailType;

    /**
     * 删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 主键
     */
    public String getEmailId() {
        return emailId;
    }

    public void setEmailId(String emailId) {
        this.emailId = emailId;
    }

    /**
     * @return 邮件标题
     */
    public String getEmailTitle() {
        return emailTitle;
    }

    public void setEmailTitle(String emailTitle) {
        this.emailTitle = emailTitle;
    }

    /**
     * @return 邮件正文内容
     */
    public String getEmailContent() {
        return emailContent;
    }

    public void setEmailContent(String emailContent) {
        this.emailContent = emailContent;
    }

    /**
     * @return 分类类型(1.合同  2.押金条)
     */
    public String getEmailType() {
        return emailType;
    }

    public void setEmailType(String emailType) {
        this.emailType = emailType;
    }

    /**
     * @return 删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsContractEmailVo{" +
            "emailId=" + emailId +
            ", emailTitle=" + emailTitle +
            ", emailContent=" + emailContent +
            ", emailType=" + emailType +
            ", delFlag=" + delFlag +
        "}";
    }
}

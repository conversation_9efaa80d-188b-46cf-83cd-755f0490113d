package com.bonc.ioc.bzf.busisigning.factory.change;

import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.consts.SymbolConst;
import com.bonc.ioc.bzf.busisigning.enums.BillTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.ContractTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.WhetherEnum;
import com.bonc.ioc.bzf.busisigning.factory.change.otherchange.AbstractSubChangeFactory;
import com.bonc.ioc.bzf.busisigning.factory.sign.AbstractContractSignFactory;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.file.vo.NewFIleResultVo;
import com.bonc.ioc.bzf.busisigning.utils.ListUtil;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsChangePreviewBillResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsChangePreviewBillVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsContractChangeTemplateVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.bzf.busisigning.vo.SigningSaveVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsResultDataVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsResultVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChangeTrialPreviewBillsParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChangeTrialPreviewBillsResultVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.BusinessContractChangeRentFreeDTO;
import com.bonc.ioc.bzf.busisigning.vo.payment.BusinessContractChangePeriodDTO;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChargeSubjectBillDTO;
import com.bonc.ioc.bzf.busisigning.vo.payment.BillInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.adjust.RentingOutDetailVO;
import com.bonc.ioc.common.exception.McpException;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 其他变更 工厂类
 *
 * <AUTHOR>
 * @since 2024/10/22
 */
@Slf4j
public class OtherChangeFactory extends AbstractContractChangeFactory {

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param contractChangeVo             合同变更信息 vo实体
     */
    public OtherChangeFactory(FeignServiceConfiguration feignServiceConfiguration,
                              BusinessServiceConfiguration businessServiceConfiguration,
                              BbsiContractChangeVo contractChangeVo) {
        super(feignServiceConfiguration,
                businessServiceConfiguration,
                contractChangeVo);
    }

    /**
     * 新增预览账单信息
     */
    @Override
    public void insertPreviewBills() {
        List<String> changeTypeItemList = ListUtil.splitBySymbol(contractChangeVo.getChangeTypeItem(), SymbolConst.COMMA);
        if (changeTypeItemList.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode()) ||
                changeTypeItemList.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode()) ||
                changeTypeItemList.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode()) ||
                changeTypeItemList.contains(ContractChangeTypeEnum.BOND_CHANGE.getCode()) ||
                changeTypeItemList.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())) {
            BbsChangePreviewBillResultVo changePreviewBillResultVo = getChargePreviewBills();
            // 新增租金预览账单信息
            businessServiceConfiguration
                    .getChangePreviewBillService()
                    .insertBatchRecord(changePreviewBillResultVo.getRentList());
            // 新增保证金预览账单信息
            businessServiceConfiguration
                    .getChangePreviewBillService()
                    .insertBatchRecord(changePreviewBillResultVo.getCashPledgeList());
            // 新增月租金预览账单信息
            businessServiceConfiguration
                    .getChangePreviewBillService()
                    .insertBatchRecord(changePreviewBillResultVo.getMonthRentList());
            // 新增可抵扣账单预览账单信息
            businessServiceConfiguration
                    .getChangePreviewBillService()
                    .insertBatchRecord(changePreviewBillResultVo.getDeductibleBillList());

            // 新增物业费账单信息
            businessServiceConfiguration
                    .getChangePreviewBillService()
                    .insertBatchRecord(changePreviewBillResultVo.getPropertyList());
            // 新增可抵扣物业费账单信息
            businessServiceConfiguration
                    .getChangePreviewBillService()
                    .insertBatchRecord(changePreviewBillResultVo.getDeductiblePropertyBillList());
        }else if(changeTypeItemList.contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode())
            || changeTypeItemList.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())){
                //TODO 改造4 - 调用changeTrial接口获取试算结果并保存
                BbsChangePreviewBillResultVo changeTrialResultVo = getChangeTrialPreviewBills();
                // 保存changeTrial试算结果到bbs_contract_change表的新字段
                saveChangeTrialResult(changeTrialResultVo);
        }
    }

    /**
     * 试算账单(实时)
     *
     * @return 试算结果 vo实体
     */
    @Override
    public BbsChangePreviewBillResultVo getChargePreviewBills() {
        // 获取上级合同信息
        BbctContractManagementVo parentContractInfoVo = getContractInfoByContractCode(contractChangeVo.getContractCode());
        // 获取上级合同签约信息
        SigningSaveVo parentSignInfoVo = getSignInfoByContractCode(contractChangeVo.getContractCode());
        // 构建试算账单请求参数
        PreviewBillsParamsVo previewBillsParamsVo = new PreviewBillsParamsVo();
        previewBillsParamsVo.setContractId(contractChangeVo.getContractCode());
        previewBillsParamsVo.setQueryDeductibleBillFlag(true);
        previewBillsParamsVo.setContractBeginDate(parentContractInfoVo.getContractBeginTime());
        previewBillsParamsVo.setContractEndDate(parentContractInfoVo.getContractEndTime());
        // 如果是缴费周期变更，使用变更后的新缴费周期，否则使用原合同的缴费周期
        String contractPricePeriod = parentContractInfoVo.getPaymentCycleCode();
        List<String> changeTypeItemList = ListUtil.splitBySymbol(contractChangeVo.getChangeTypeItem(), SymbolConst.COMMA);
        if (changeTypeItemList.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())) {
            // 使用新的缴费周期
            if (StringUtils.isNotBlank(contractChangeVo.getPaymentCycle())) {
                contractPricePeriod = contractChangeVo.getPaymentCycle();
                log.debug("缴费周期变更：使用新的缴费周期 {}", contractPricePeriod);
            }
            // 设置变更会计期间类型默认值
            previewBillsParamsVo.setChangeAccountingPeriodType("01");
            log.debug("缴费周期变更：设置变更会计期间类型为默认值 01");
        }
        previewBillsParamsVo.setContractPricePeriod(contractPricePeriod);
        previewBillsParamsVo.setProjectId(parentContractInfoVo.getSubjectMatterList().get(0).getProjectId());
        previewBillsParamsVo.setProjectNo(parentContractInfoVo.getSubjectMatterList().get(0).getProjectNoNcc());
        // 赋值子变更信息
        List<AbstractSubChangeFactory> subChangeFactoryList = getSubChangeFactoryList();
        for (AbstractSubChangeFactory subChangeFactory : subChangeFactoryList) {
            subChangeFactory.setPreviewBillsParams(previewBillsParamsVo, parentContractInfoVo, parentSignInfoVo);
        }
        // 缴费中心 获取试算结果
        PreviewBillsResultVo chargeBillResultVo = getPreviewBills(previewBillsParamsVo);
        // 转换实体
        BbsChangePreviewBillResultVo previewBillResultVo = new BbsChangePreviewBillResultVo();
        if (!Objects.isNull(chargeBillResultVo)) {
            setPreviewRentBill(chargeBillResultVo, previewBillResultVo);
            setPreviewCashPledgeBill(chargeBillResultVo, previewBillResultVo);
            setPreviewMonthRentBill(chargeBillResultVo, previewBillResultVo);
            setPreviewDeductionBill(chargeBillResultVo, previewBillResultVo);
            //处理物业费
            setPreviewPropertyBill(chargeBillResultVo, previewBillResultVo);
            //处理物业费抵扣
            setPreviewDeductiblePropertyBill(chargeBillResultVo, previewBillResultVo);
            previewBillResultVo.setDeductedAmountList(chargeBillResultVo.getDeductedAmountList());
        }
        return previewBillResultVo;
    }

    /**
     * 调用changeTrial接口获取试算结果(免租期变更和缴费周期变更)
     *
     * @return 试算结果 vo实体
     */
    public BbsChangePreviewBillResultVo getChangeTrialPreviewBills() {
        // 获取上级合同信息
        BbctContractManagementVo parentContractInfoVo = getContractInfoByContractCode(contractChangeVo.getContractCode());
        // 获取上级合同签约信息
        SigningSaveVo parentSignInfoVo = getSignInfoByContractCode(contractChangeVo.getContractCode());

        // 构建changeTrial请求参数
        ChangeTrialPreviewBillsParamsVo changeTrialParamsVo = new ChangeTrialPreviewBillsParamsVo();
        changeTrialParamsVo.setProjectId(parentContractInfoVo.getSubjectMatterList().get(0).getProjectId());

        List<String> changeTypeItemList = ListUtil.splitBySymbol(contractChangeVo.getChangeTypeItem(), SymbolConst.COMMA);

        if (changeTypeItemList.contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode())) {
            // 免租期变更
            changeTrialParamsVo.setChangeType("15");
            BusinessContractChangeRentFreeDTO rentFreeDTO = buildRentFreeDTO(parentContractInfoVo, parentSignInfoVo);
            changeTrialParamsVo.setContractChangeRentFreeDTO(rentFreeDTO);
        } else if (changeTypeItemList.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())) {
            // 缴费周期变更
            changeTrialParamsVo.setChangeType("16");
            BusinessContractChangePeriodDTO periodDTO = buildPeriodDTO(parentContractInfoVo, parentSignInfoVo);
            changeTrialParamsVo.setContractChangePeriodDTO(periodDTO);
        }

        // 调用changeTrial接口
        List<ChangeTrialPreviewBillsResultVo> changeTrialResultList = getChangeTrialBills(changeTrialParamsVo);

        // 转换并合并结果
        BbsChangePreviewBillResultVo previewBillResultVo = new BbsChangePreviewBillResultVo();
        if (CollectionUtils.isNotEmpty(changeTrialResultList)) {
            convertChangeTrialResult(changeTrialResultList, previewBillResultVo);
        }

        return previewBillResultVo;
    }



    /**
     * 构建免租期变更DTO
     *
     * @param parentContractInfoVo 上级合同信息
     * @param parentSignInfoVo     上级合同签约信息
     * @return 免租期变更DTO
     */
    private BusinessContractChangeRentFreeDTO buildRentFreeDTO(BbctContractManagementVo parentContractInfoVo, SigningSaveVo parentSignInfoVo) {
        BusinessContractChangeRentFreeDTO rentFreeDTO = new BusinessContractChangeRentFreeDTO();
        rentFreeDTO.setContractCode(contractChangeVo.getContractCode());
        rentFreeDTO.setAgreementCode(contractChangeVo.getCcId()); // 使用变更ID作为协议号

        // 设置变更账期时间范围
        if (StringUtils.isNotBlank(contractChangeVo.getBillCycleStartDate())) {
            rentFreeDTO.setChangeBillStartDate(contractChangeVo.getBillCycleStartDate());
        }
        if (StringUtils.isNotBlank(contractChangeVo.getBillCycleEndDate())) {
            rentFreeDTO.setChangeBillEndDate(contractChangeVo.getBillCycleEndDate());
        }

        // 设置默认值
        rentFreeDTO.setDeductionType("0"); // 默认抵扣
        rentFreeDTO.setRefundChannel("0"); // 默认线下退款

        // 构建房源列表
        rentFreeDTO.setRoomList(buildRoomListForChangeTrial(parentContractInfoVo, parentSignInfoVo));

        return rentFreeDTO;
    }

    /**
     * 构建缴费周期变更DTO
     *
     * @param parentContractInfoVo 上级合同信息
     * @param parentSignInfoVo     上级合同签约信息
     * @return 缴费周期变更DTO
     */
    private BusinessContractChangePeriodDTO buildPeriodDTO(BbctContractManagementVo parentContractInfoVo, SigningSaveVo parentSignInfoVo) {
        BusinessContractChangePeriodDTO periodDTO = new BusinessContractChangePeriodDTO();
        periodDTO.setContractCode(contractChangeVo.getContractCode());
        periodDTO.setAgreementCode(contractChangeVo.getCcId()); // 使用变更ID作为协议号

        // 设置变更账期时间范围
        if (StringUtils.isNotBlank(contractChangeVo.getBillCycleStartDate())) {
            periodDTO.setChangeBillStartDate(contractChangeVo.getBillCycleStartDate());
        }
        if (StringUtils.isNotBlank(contractChangeVo.getBillCycleEndDate())) {
            periodDTO.setChangeBillEndDate(contractChangeVo.getBillCycleEndDate());
        }

        // 设置变更后的缴费周期
        if (StringUtils.isNotBlank(contractChangeVo.getPaymentCycle())) {
            periodDTO.setChargeSubjectPeriod(contractChangeVo.getPaymentCycle());
        }

        // TODO: 构建房源列表 - 这里需要根据实际业务逻辑构建
        // periodDTO.setRoomList(buildRoomList(parentContractInfoVo, parentSignInfoVo));

        return periodDTO;
    }

    /**
     * 转换changeTrial结果到预览账单结果
     *
     * @param changeTrialResultList changeTrial结果列表
     * @param previewBillResultVo   预览账单结果
     */
    private void convertChangeTrialResult(List<ChangeTrialPreviewBillsResultVo> changeTrialResultList,
                                        BbsChangePreviewBillResultVo previewBillResultVo) {
        // 合并多个houseId的账单列表
        List<BillInfoVo> billInfoVoList = new ArrayList<>();
        List<RentingOutDetailVO> rentingOutDetailVOList = new ArrayList<>();

        for (ChangeTrialPreviewBillsResultVo changeTrialResult : changeTrialResultList) {
            // 转换账单列表
            if (CollectionUtils.isNotEmpty(changeTrialResult.getChargeSubjectBillList())) {
                for (ChargeSubjectBillDTO chargeSubjectBill : changeTrialResult.getChargeSubjectBillList()) {
                    BillInfoVo billInfoVo = new BillInfoVo();
                    BeanUtils.copyProperties(chargeSubjectBill, billInfoVo);
                    billInfoVo.setHouseId(changeTrialResult.getHouseId());
                    billInfoVoList.add(billInfoVo);
                }
            }

            // 转换应退/抵扣金额列表
            if (CollectionUtils.isNotEmpty(changeTrialResult.getRentingOutDetailVOList())) {
                rentingOutDetailVOList.addAll(changeTrialResult.getRentingOutDetailVOList());
            }
        }

        previewBillResultVo.setBillInfoVoList(billInfoVoList);
        previewBillResultVo.setRentingOutDetailVOList(rentingOutDetailVOList);
    }

    /**
     * 保存changeTrial试算结果到bbs_contract_change表
     *
     * @param changeTrialResultVo changeTrial试算结果
     */
    private void saveChangeTrialResult(BbsChangePreviewBillResultVo changeTrialResultVo) {
        try {
            // 将试算结果转换为JSON字符串保存到数据库
            String changeTrialResultJson = JSON.toJSONString(changeTrialResultVo);

            // 更新合同变更记录，保存试算结果到新字段
            BbsiContractChangeVo updateVo = new BbsiContractChangeVo();
            updateVo.setCcId(contractChangeVo.getCcId());

            // 根据需求文档，需要在bbs_contract_change表中新增字段保存changeTrial结果
            // 这里暂时使用changeExtend字段保存，后续需要新增专门的字段如change_trial_result_json
            updateVo.setChangeExtend(changeTrialResultJson);

            // 调用服务更新数据库记录
            businessServiceConfiguration.getContractChangeService().updateByIdRecord(updateVo);

            log.info("changeTrial试算结果已保存到bbs_contract_change表, ccId: {}", contractChangeVo.getCcId());
        } catch (Exception e) {
            log.error("保存changeTrial试算结果失败, ccId: {}, error: {}", contractChangeVo.getCcId(), e.getMessage(), e);
            throw new McpException("保存changeTrial试算结果失败: " + e.getMessage());
        }
    }

    /**
     * 预览或下载
     *
     * @return 合同文件信息
     */
    @Override
    public FIleResultVo previewAndDownload() {
        if (StringUtils.isBlank(contractChangeVo.getAgreementFileId())) {
            throw new McpException("协议文件不存在");
        }
        NewFIleResultVo fileInfoVo = getFileInfoById(contractChangeVo.getAgreementFileId()).get(0);
        FIleResultVo resultVo = new FIleResultVo();
        resultVo.setFileName(fileInfoVo.getFileName());
        resultVo.setId(Integer.parseInt(fileInfoVo.getFileId()));
        resultVo.setFileUrl(fileInfoVo.getPreviewAddress());
        resultVo.setCreateTime(fileInfoVo.getFileCreateTime());
        resultVo.setState(fileInfoVo.getIsDeleted());
        return resultVo;
    }

    /**
     * 赋值子变更预览信息
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    @Override
    public void setSubChangePreviewInfo(BbctPreviewInfoParamsVo previewInfoParamsVo) {
        List<AbstractSubChangeFactory> subChangeFactoryList = getSubChangeFactoryList();
        for (AbstractSubChangeFactory subChangeFactory : subChangeFactoryList) {
            subChangeFactory.setPreviewInfo(previewInfoParamsVo);
        }
    }

    /**
     * 合同变更信息推送给签约
     *
     * @return 签约id
     */
    @Override
    public String pushSignInfo() {
        // 获取上级合同信息
        BbctContractManagementVo parentContractInfoVo = getContractInfoByContractCode(contractChangeVo.getContractCode());
        // 获取上级合同签约信息
        SigningSaveVo parentSignInfoVo = getSignInfoByContractCode(contractChangeVo.getContractCode());
        // 赋值签约信息(默认参数)
        SigningSaveVo signingSaveVo = setDefaultSignInfo(parentContractInfoVo, parentSignInfoVo);
        // 根据其他变更信息赋值签约信息
        setSignInfoByOtherChangeInfo(signingSaveVo, parentSignInfoVo);
        // 新增签约信息
        return insertSignInfo(signingSaveVo);
    }

    /**
     * 据乙方变更信息生成预览信息
     *
     * @param parentSignInfoVo 上级合同签约信息 vo实体
     * @return 预览信息 vo实体
     */
    private BbctContractManagementVo createOtherChangePreviewInfo(SigningSaveVo parentSignInfoVo) {
        BbctPreviewInfoParamsVo previewInfoParamsVo = new BbctPreviewInfoParamsVo();
        BbsSignInfoVo signInfoVo = new BbsSignInfoVo();
        // 赋值合同模板信息
        BbsContractChangeTemplateVo contractChangeTemplateVo = getContractTemplate();
        signInfoVo.setContractTemplateId(contractChangeTemplateVo.getTemplateId());
        signInfoVo.setContractTemplateName(contractChangeTemplateVo.getTemplateName());
        // 赋值来源信息
        signInfoVo.setParentContractCode(contractChangeVo.getContractCode());
        signInfoVo.setContractType(ContractTypeEnum.OTHER_CHANGE.getCode());
        signInfoVo.setProductSourceType(contractChangeVo.getChangeTypeItem());
        // 赋值模板属性信息
        previewInfoParamsVo.setTemplateSeatMap(createTemplateSeatMap(parentSignInfoVo.getSeatInfoVoList()));
        // 赋值子变更信息
        setSubChangePreviewInfo(previewInfoParamsVo);
        return AbstractContractSignFactory.getInstance(signInfoVo).createPreviewInfoVo(previewInfoParamsVo);
    }

    /**
     * 根据其他变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     * @param signingSaveVo 上级合同签约信息 vo实体
     */
    private void setSignInfoByOtherChangeInfo(SigningSaveVo signingSaveVo,
                                              SigningSaveVo parentSignInfoVo) {
        // 赋值合同模板信息
        signingSaveVo.setContractTemplateId(parentSignInfoVo.getContractTemplateId());
        signingSaveVo.setContractTemplateName(parentSignInfoVo.getContractTemplateName());
        // 赋值来源信息
        signingSaveVo.setParentContractCode(contractChangeVo.getContractCode());
        signingSaveVo.setContractType(ContractTypeEnum.OTHER_CHANGE.getCode());
        signingSaveVo.setProductSourceType(contractChangeVo.getChangeTypeItem());
        
        // 处理缴费周期变更
         List<String> changeTypeItemList = ListUtil.splitBySymbol(contractChangeVo.getChangeTypeItem(), SymbolConst.COMMA);
         if (changeTypeItemList.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode()) &&
             StringUtils.isNotBlank(contractChangeVo.getPaymentCycle())) {
             // 更新新合同的缴费周期
             signingSaveVo.setPaymentCycleCode(contractChangeVo.getPaymentCycle());
             log.debug("缴费周期变更推签约：设置新的缴费周期 {}", contractChangeVo.getPaymentCycle());
         }
        
        // 赋值子变更信息
        List<AbstractSubChangeFactory> subChangeFactoryList = getSubChangeFactoryList();
        for (AbstractSubChangeFactory subChangeFactory : subChangeFactoryList) {
            subChangeFactory.setSignInfoByChangeInfo(signingSaveVo);
        }
    }

    /**
     * 获取子变更工厂列表
     *
     * @return 子变更工厂列表
     */
    private List<AbstractSubChangeFactory> getSubChangeFactoryList() {
        return AbstractSubChangeFactory.getInstances(contractChangeVo);
    }

    /**
     * 赋值租金账单相关预览信息
     *
     * @param chargeBillResultVo  缴费中心返回的试算账单结果 vo实体
     * @param previewBillResultVo 预览账单结果 vo实体
     */
    private void setPreviewRentBill(PreviewBillsResultVo chargeBillResultVo,
                                    BbsChangePreviewBillResultVo previewBillResultVo) {
        List<PreviewBillsResultDataVo> chargeRentBillList = chargeBillResultVo.getRentList();
        if (CollectionUtils.isNotEmpty(chargeRentBillList)) {
            List<BbsChangePreviewBillVo> previewRentBillList = new ArrayList<>();
            for (PreviewBillsResultDataVo chargeRentBillVo : chargeRentBillList) {
                BbsChangePreviewBillVo previewRentBillVo = new BbsChangePreviewBillVo();
                BeanUtils.copyProperties(chargeRentBillVo, previewRentBillVo);
                previewRentBillVo.setCcId(contractChangeVo.getCcId());
                previewRentBillVo.setBillType(BillTypeEnum.RENT.getCode());
                previewRentBillVo.setMonths(Objects.toString(chargeRentBillVo.getMonths(), null));
                previewRentBillVo.setDays(Objects.toString(chargeRentBillVo.getDays(), null));
                previewRentBillVo.setPayableMoney(Objects.toString(chargeRentBillVo.getPayableMoney(), null));
                previewRentBillVo.setTaxRate(Objects.toString(chargeRentBillVo.getTaxRate(), null));
                previewRentBillVo.setExcludingRateMoney(Objects.toString(chargeRentBillVo.getExcludingRateMoney(), null));
                previewRentBillVo.setRateMoney(Objects.toString(chargeRentBillVo.getRateMoney(), null));
                previewRentBillVo.setDayMoney(Objects.toString(chargeRentBillVo.getDayMoney(), null));
                previewRentBillVo.setToBePaidMoney(Objects.toString(chargeRentBillVo.getToBePaidMoney(), null));
                previewRentBillVo.setPaidInMoney(Objects.toString(chargeRentBillVo.getPaidInMoney(), null));
                previewRentBillVo.setMonthAmount(Objects.toString(chargeRentBillVo.getMonthAmount(), null));
                previewRentBillVo.setChargePeriod(StringUtils.isNotBlank(chargeRentBillVo.getChargePeriod()) ? Integer.valueOf(chargeRentBillVo.getChargePeriod()) : null);
                previewRentBillVo.setDelFlag(Integer.valueOf(WhetherEnum.YES.getCode()));
                previewRentBillList.add(previewRentBillVo);
            }
            previewBillResultVo.setRentList(previewRentBillList);
        }
    }

    /**
     * 赋值物业费账单相关预览信息
     *
     * @param chargeBillResultVo  缴费中心返回的试算账单结果 vo实体
     * @param previewBillResultVo 预览账单结果 vo实体
     */
    private void setPreviewPropertyBill(PreviewBillsResultVo chargeBillResultVo,
                                    BbsChangePreviewBillResultVo previewBillResultVo) {
        List<PreviewBillsResultDataVo> chargeRentBillList = chargeBillResultVo.getPropertyList();
        if (CollectionUtils.isNotEmpty(chargeRentBillList)) {
            List<BbsChangePreviewBillVo> previewRentBillList = new ArrayList<>();
            for (PreviewBillsResultDataVo chargeRentBillVo : chargeRentBillList) {
                BbsChangePreviewBillVo previewRentBillVo = new BbsChangePreviewBillVo();
                BeanUtils.copyProperties(chargeRentBillVo, previewRentBillVo);
                previewRentBillVo.setCcId(contractChangeVo.getCcId());
                previewRentBillVo.setBillType(BillTypeEnum.PROP.getCode());
                previewRentBillVo.setMonths(Objects.toString(chargeRentBillVo.getMonths(), null));
                previewRentBillVo.setDays(Objects.toString(chargeRentBillVo.getDays(), null));
                previewRentBillVo.setPayableMoney(Objects.toString(chargeRentBillVo.getPayableMoney(), null));
                previewRentBillVo.setTaxRate(Objects.toString(chargeRentBillVo.getTaxRate(), null));
                previewRentBillVo.setExcludingRateMoney(Objects.toString(chargeRentBillVo.getExcludingRateMoney(), null));
                previewRentBillVo.setRateMoney(Objects.toString(chargeRentBillVo.getRateMoney(), null));
                previewRentBillVo.setDayMoney(Objects.toString(chargeRentBillVo.getDayMoney(), null));
                previewRentBillVo.setToBePaidMoney(Objects.toString(chargeRentBillVo.getToBePaidMoney(), null));
                previewRentBillVo.setPaidInMoney(Objects.toString(chargeRentBillVo.getPaidInMoney(), null));
                previewRentBillVo.setMonthAmount(Objects.toString(chargeRentBillVo.getMonthAmount(), null));
                previewRentBillVo.setChargePeriod(StringUtils.isNotBlank(chargeRentBillVo.getChargePeriod()) ? Integer.valueOf(chargeRentBillVo.getChargePeriod()) : null);
                previewRentBillVo.setDelFlag(Integer.valueOf(WhetherEnum.YES.getCode()));
                previewRentBillList.add(previewRentBillVo);
            }
            previewBillResultVo.setPropertyList(previewRentBillList);
        }
    }

    /**
     * 赋值保证金账单相关预览信息
     *
     * @param chargeBillResultVo  缴费中心返回的试算账单结果 vo实体
     * @param previewBillResultVo 预览账单结果 vo实体
     */
    private void setPreviewCashPledgeBill(PreviewBillsResultVo chargeBillResultVo,
                                          BbsChangePreviewBillResultVo previewBillResultVo) {
        List<PreviewBillsResultDataVo> chargeCashPledgeBillList = chargeBillResultVo.getDepositList();
        if (CollectionUtils.isNotEmpty(chargeCashPledgeBillList)) {
            List<BbsChangePreviewBillVo> previewCashPledgeBillList = new ArrayList<>();
            for (PreviewBillsResultDataVo chargeCashPledgeBillVo : chargeCashPledgeBillList) {
                BbsChangePreviewBillVo previewCashPledgeBillVo = new BbsChangePreviewBillVo();
                BeanUtils.copyProperties(chargeCashPledgeBillVo, previewCashPledgeBillVo);
                previewCashPledgeBillVo.setCcId(contractChangeVo.getCcId());
                previewCashPledgeBillVo.setBillType(BillTypeEnum.CASH_PLEDGE.getCode());
                previewCashPledgeBillVo.setMonths(Objects.toString(chargeCashPledgeBillVo.getMonths(), null));
                previewCashPledgeBillVo.setDays(Objects.toString(chargeCashPledgeBillVo.getDays(), null));
                previewCashPledgeBillVo.setPayableMoney(Objects.toString(chargeCashPledgeBillVo.getPayableMoney(), null));
                previewCashPledgeBillVo.setTaxRate(Objects.toString(chargeCashPledgeBillVo.getTaxRate(), null));
                previewCashPledgeBillVo.setExcludingRateMoney(Objects.toString(chargeCashPledgeBillVo.getExcludingRateMoney(), null));
                previewCashPledgeBillVo.setRateMoney(Objects.toString(chargeCashPledgeBillVo.getRateMoney(), null));
                previewCashPledgeBillVo.setDayMoney(Objects.toString(chargeCashPledgeBillVo.getDayMoney(), null));
                previewCashPledgeBillVo.setToBePaidMoney(Objects.toString(chargeCashPledgeBillVo.getToBePaidMoney(), null));
                previewCashPledgeBillVo.setPaidInMoney(Objects.toString(chargeCashPledgeBillVo.getPaidInMoney(), null));
                previewCashPledgeBillVo.setMonthAmount(Objects.toString(chargeCashPledgeBillVo.getMonthAmount(), null));
                previewCashPledgeBillVo.setChargePeriod(StringUtils.isNotBlank(chargeCashPledgeBillVo.getChargePeriod()) ? Integer.valueOf(chargeCashPledgeBillVo.getChargePeriod()) : null);
                previewCashPledgeBillVo.setDelFlag(Integer.valueOf(WhetherEnum.YES.getCode()));
                previewCashPledgeBillList.add(previewCashPledgeBillVo);
            }
            previewBillResultVo.setCashPledgeList(previewCashPledgeBillList);
        }
    }

    /**
     * 赋值月租金账单相关预览信息
     *
     * @param chargeBillResultVo  缴费中心返回的试算账单结果 vo实体
     * @param previewBillResultVo 预览账单结果 vo实体
     */
    private void setPreviewMonthRentBill(PreviewBillsResultVo chargeBillResultVo,
                                         BbsChangePreviewBillResultVo previewBillResultVo) {
        List<PreviewBillsResultDataVo> chargeCashPledgeBillList = chargeBillResultVo.getMonthRentList();
        if (CollectionUtils.isNotEmpty(chargeCashPledgeBillList)) {
            List<BbsChangePreviewBillVo> previewCashPledgeBillList = new ArrayList<>();
            for (PreviewBillsResultDataVo chargeCashPledgeBillVo : chargeCashPledgeBillList) {
                BbsChangePreviewBillVo previewCashPledgeBillVo = new BbsChangePreviewBillVo();
                BeanUtils.copyProperties(chargeCashPledgeBillVo, previewCashPledgeBillVo);
                previewCashPledgeBillVo.setCcId(contractChangeVo.getCcId());
                previewCashPledgeBillVo.setBillType(BillTypeEnum.MONTH_RENT.getCode());
                previewCashPledgeBillVo.setMonths(Objects.toString(chargeCashPledgeBillVo.getMonths(), null));
                previewCashPledgeBillVo.setDays(Objects.toString(chargeCashPledgeBillVo.getDays(), null));
                previewCashPledgeBillVo.setPayableMoney(Objects.toString(chargeCashPledgeBillVo.getPayableMoney(), null));
                previewCashPledgeBillVo.setTaxRate(Objects.toString(chargeCashPledgeBillVo.getTaxRate(), null));
                previewCashPledgeBillVo.setExcludingRateMoney(Objects.toString(chargeCashPledgeBillVo.getExcludingRateMoney(), null));
                previewCashPledgeBillVo.setRateMoney(Objects.toString(chargeCashPledgeBillVo.getRateMoney(), null));
                previewCashPledgeBillVo.setDayMoney(Objects.toString(chargeCashPledgeBillVo.getDayMoney(), null));
                previewCashPledgeBillVo.setToBePaidMoney(Objects.toString(chargeCashPledgeBillVo.getToBePaidMoney(), null));
                previewCashPledgeBillVo.setPaidInMoney(Objects.toString(chargeCashPledgeBillVo.getPaidInMoney(), null));
                previewCashPledgeBillVo.setMonthAmount(Objects.toString(chargeCashPledgeBillVo.getMonthAmount(), null));
                previewCashPledgeBillVo.setChargePeriod(StringUtils.isNotBlank(chargeCashPledgeBillVo.getChargePeriod()) ? Integer.valueOf(chargeCashPledgeBillVo.getChargePeriod()) : null);
                previewCashPledgeBillVo.setDelFlag(Integer.valueOf(WhetherEnum.YES.getCode()));
                previewCashPledgeBillList.add(previewCashPledgeBillVo);
            }
            previewBillResultVo.setMonthRentList(previewCashPledgeBillList);
        }
    }

    /**
     * 赋值抵扣账单相关预览信息
     *
     * @param chargeBillResultVo  缴费中心返回的试算账单结果 vo实体
     * @param previewBillResultVo 预览账单结果 vo实体
     */
    private void setPreviewDeductionBill(PreviewBillsResultVo chargeBillResultVo,
                                         BbsChangePreviewBillResultVo previewBillResultVo) {
        List<PreviewBillsResultDataVo> chargeCashPledgeBillList = chargeBillResultVo.getDeductibleBillList();
        if (CollectionUtils.isNotEmpty(chargeCashPledgeBillList)) {
            List<BbsChangePreviewBillVo> previewCashPledgeBillList = new ArrayList<>();
            for (PreviewBillsResultDataVo chargeDeductionBillVo : chargeCashPledgeBillList) {
                BbsChangePreviewBillVo previewDeductionBillVo = new BbsChangePreviewBillVo();
                BeanUtils.copyProperties(chargeDeductionBillVo, previewDeductionBillVo);
                previewDeductionBillVo.setCcId(contractChangeVo.getCcId());
                previewDeductionBillVo.setBillType(BillTypeEnum.DEDUCTION.getCode());
                previewDeductionBillVo.setMonths(Objects.toString(chargeDeductionBillVo.getMonths(), null));
                previewDeductionBillVo.setDays(Objects.toString(chargeDeductionBillVo.getDays(), null));
                previewDeductionBillVo.setPayableMoney(Objects.toString(chargeDeductionBillVo.getPayableMoney(), null));
                previewDeductionBillVo.setTaxRate(Objects.toString(chargeDeductionBillVo.getTaxRate(), null));
                previewDeductionBillVo.setExcludingRateMoney(Objects.toString(chargeDeductionBillVo.getExcludingRateMoney(), null));
                previewDeductionBillVo.setRateMoney(Objects.toString(chargeDeductionBillVo.getRateMoney(), null));
                previewDeductionBillVo.setDayMoney(Objects.toString(chargeDeductionBillVo.getDayMoney(), null));
                previewDeductionBillVo.setToBePaidMoney(Objects.toString(chargeDeductionBillVo.getToBePaidMoney(), null));
                previewDeductionBillVo.setPaidInMoney(Objects.toString(chargeDeductionBillVo.getPaidInMoney(), null));
                previewDeductionBillVo.setMonthAmount(Objects.toString(chargeDeductionBillVo.getMonthAmount(), null));
                previewDeductionBillVo.setChargePeriod(StringUtils.isNotBlank(chargeDeductionBillVo.getChargePeriod()) ? Integer.valueOf(chargeDeductionBillVo.getChargePeriod()) : null);
                previewDeductionBillVo.setDelFlag(Integer.valueOf(WhetherEnum.YES.getCode()));
                previewCashPledgeBillList.add(previewDeductionBillVo);
            }
            previewBillResultVo.setDeductibleBillList(previewCashPledgeBillList);
        }
    }

    /**
     * 赋值物业费抵扣账单相关预览信息
     *
     * @param chargeBillResultVo  缴费中心返回的试算账单结果 vo实体
     * @param previewBillResultVo 预览账单结果 vo实体
     */
    private void setPreviewDeductiblePropertyBill(PreviewBillsResultVo chargeBillResultVo,
                                         BbsChangePreviewBillResultVo previewBillResultVo) {
        List<PreviewBillsResultDataVo> chargeCashPledgeBillList = chargeBillResultVo.getDeductiblePropertyBillList();
        if (CollectionUtils.isNotEmpty(chargeCashPledgeBillList)) {
            List<BbsChangePreviewBillVo> previewCashPledgeBillList = new ArrayList<>();
            for (PreviewBillsResultDataVo chargeDeductionBillVo : chargeCashPledgeBillList) {
                BbsChangePreviewBillVo previewDeductionBillVo = new BbsChangePreviewBillVo();
                BeanUtils.copyProperties(chargeDeductionBillVo, previewDeductionBillVo);
                previewDeductionBillVo.setCcId(contractChangeVo.getCcId());
                previewDeductionBillVo.setBillType(BillTypeEnum.PROP_DEDUCTION.getCode());
                previewDeductionBillVo.setMonths(Objects.toString(chargeDeductionBillVo.getMonths(), null));
                previewDeductionBillVo.setDays(Objects.toString(chargeDeductionBillVo.getDays(), null));
                previewDeductionBillVo.setPayableMoney(Objects.toString(chargeDeductionBillVo.getPayableMoney(), null));
                previewDeductionBillVo.setTaxRate(Objects.toString(chargeDeductionBillVo.getTaxRate(), null));
                previewDeductionBillVo.setExcludingRateMoney(Objects.toString(chargeDeductionBillVo.getExcludingRateMoney(), null));
                previewDeductionBillVo.setRateMoney(Objects.toString(chargeDeductionBillVo.getRateMoney(), null));
                previewDeductionBillVo.setDayMoney(Objects.toString(chargeDeductionBillVo.getDayMoney(), null));
                previewDeductionBillVo.setToBePaidMoney(Objects.toString(chargeDeductionBillVo.getToBePaidMoney(), null));
                previewDeductionBillVo.setPaidInMoney(Objects.toString(chargeDeductionBillVo.getPaidInMoney(), null));
                previewDeductionBillVo.setMonthAmount(Objects.toString(chargeDeductionBillVo.getMonthAmount(), null));
                previewDeductionBillVo.setChargePeriod(StringUtils.isNotBlank(chargeDeductionBillVo.getChargePeriod()) ? Integer.valueOf(chargeDeductionBillVo.getChargePeriod()) : null);
                previewDeductionBillVo.setDelFlag(Integer.valueOf(WhetherEnum.YES.getCode()));
                previewCashPledgeBillList.add(previewDeductionBillVo);
            }
            previewBillResultVo.setDeductiblePropertyBillList(previewCashPledgeBillList);
        }
    }
}

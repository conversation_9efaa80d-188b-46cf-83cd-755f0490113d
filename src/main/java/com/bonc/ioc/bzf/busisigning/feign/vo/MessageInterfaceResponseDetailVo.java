package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 接口返回的明细信息-本系统接口返回使用的类
 *
 * <AUTHOR>
 * @date 2022-06-14 11:44
 * @change 2022-06-14 11:44 by sqj for init
 */
@Data
public class MessageInterfaceResponseDetailVo {

    @ApiModelProperty(value = "接口返回状态码 00:成功；其余失败")
    private String code;

    @ApiModelProperty(value = "用户id")
    private String toUser;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "业务id")
    private String businessId;

    @ApiModelProperty(value = "结果编码")
    private String recordDetailId;
}

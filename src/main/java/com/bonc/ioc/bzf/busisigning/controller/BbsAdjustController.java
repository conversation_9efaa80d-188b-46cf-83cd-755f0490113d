package com.bonc.ioc.bzf.busisigning.controller;

import com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.busisigning.service.IBbsAdjustService;
import com.bonc.ioc.bzf.busisigning.vo.BbpmBillManagementPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.adjust.*;
import com.bonc.ioc.bzf.busisigning.workflow.vo.CallBakParamsVo;
import com.bonc.ioc.common.base.controller.McpBaseController;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *  缴费中心 账单
 *
 * <AUTHOR>
 * @date 2021-08-27
 * @change 2021-08-27 by wtl for init
 */
@RestController
@RequestMapping("/adjust")
@Api(tags = "应收调整")
@Validated
public class BbsAdjustController extends McpBaseController {

    @Autowired
    private IBbsAdjustService adjustService;

//    @Value("${fw.workflowId.adjust}")
//    private String adjustWorkflowId;


    //++++++++++++++应收调整接口-开始++++++++++++++

    /**
     * selectByIdRecord 根据主键查询
     * @param id 需要查询的主键
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     */
    @GetMapping(value = "/adjustSelectById", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "yuanxuesong")
    @ApiOperation(value = "应收调整-根据主键查询", notes = "根据主键查询表中信息", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmReceivableAdjustVo> selectAdjustById(@ApiParam(value = "需要查询的主键" ,required = false) @RequestParam(required = false) String id){
        return adjustService.selectAdjustById(id);
    }

    /**
     * selectByIdRecord 根据合同变更主键查询
     * @param id 需要查询的主键
     * @return  com.bonc.ioc.common.util.AppReply 主键查询的数据
     * @since 1.0.0
     * <AUTHOR>
     */
    @GetMapping(value = "/adjustSelectByCcid", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "yuanxuesong")
    @ApiOperation(value = "应收调整-根据合同变更主键查询", notes = "根据合同变更主键查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<BbpmReceivableAdjustVo> adjustSelectByCcid(@ApiParam(value = "需要查询的主键" ,required = false) @RequestParam(required = false) String ccid){
        return adjustService.adjustSelectByCcid(ccid);
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     */
    @GetMapping(value = "/adjustSelectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "yuanxuesong")
    @ApiOperation(value = "应收调整-分页查询", notes = "分页查询", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmReceivableAdjustPageResultVo>>> selectByPageRecord(BbpmReceivableAdjustPageVo vo){
        return adjustService.selectAdjustList(vo);
    }


    @GetMapping(value = "/adjustContractSelectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "应收调整-分页查询合同", notes = "应收调整分页查询合同", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmReceivableAdjustContractPageResultVo>>> selectByPageForAdjust(BbpmReceivableAdjustContractPageVo vo) {
        return adjustService.selectByPageForAdjust(vo);
    }

    @PostMapping(value = "/adjustBillView", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 8, author = "sx")
    @ApiOperation(value = "应收调整-应收试算", notes = "应收试算", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<CalculationResultVo>> adjustBillView(@RequestBody CalculationParamVo vo){
        return adjustService.adjustBillView(vo);
    }



    @GetMapping(value = "/selectBillForAdjust", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 9, author = "yuanxuesong")
    @ApiOperation(value = "应收调整-根据调整单id查询账单", notes = "根据调整单id查询账单", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<List<BbpmBillForAdjustPageVo>> selectBillForAdjust(@ApiParam(value = "需要查询的主键" ,required = false) @RequestParam(required = false) String id){
        return adjustService.selectBillForAdjust(id);
    }

    /**
     * selectContractForAdjustId 查询应收调整合同编号接口
     * @return  com.bonc.ioc.common.util.AppReply 应收调整合同编号
     * @since 1.0.0
     * <AUTHOR>
     * @date  2025-02-18
     * @change
     * 2025-02-18 by yuanxuesong for init
     */
    @GetMapping(value = "/selectContractForAdjustId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "yuanxuesong")
    @ApiOperation(value = "应收调整-查询应收调整合同编号接口", notes = "查询应收调整合同编号接口", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String[]> selectContractForAdjustId(@RequestParam(required = false) String id){
        return adjustService.selectContractForAdjustId(id);
    }



    //++++++++++++++应收调整接口-结束++++++++++++++

    /**
     * 根据requestId 删除数据
     * @param requestId
     * @return
     */
    @PostMapping(value = "/adjustRemoveByRequestId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 3, author = "sx")
    @ApiOperation(value = "应收调整-根据主键删除", notes = "根据主键删除表中信息 逻辑删除", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply adjustRemoveByRequestId(@ApiParam(value = "需要删除的主键" ,required = false) @RequestParam String requestId){
        AppReply appReply = adjustService.removeByRequestId(requestId);
        return appReply;
    }

    /**
     * 获取应收调整流程id
     * @return
     */
    @GetMapping(value = "/getAdjustWorkflowId", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "sx")
    @ApiOperation(value = "应收调整-获取workflowId", notes = "应收调整-获取workflowId", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> getAdjustWorkflowId(){
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
//        appReply.setData(adjustWorkflowId);
        return appReply;
    }

    /**
     * 保存 发起 流程
     * @param vo
     * @return
     */
    @PostMapping(value = "/adjustSaveWorkFlow", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "yuanxuesong")
    @ApiOperation(value = "应收调整-根据主键更新或新增", notes = "根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<String> adjustSaveWorkFlow(@ApiParam(value = "需要更新或新增的应收调整" ,required = false) @RequestBody @Validated(InsertValidatorGroup.class) BbpmReceivableAdjustVo vo){
        AppReply<String> appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        appReply.setData(adjustService.adjustSaveWorkFlow(vo));
        return appReply;
    }

    /**
     * 应收调整-工作流结束回调
     * @param vo
     * @return
     */
    @PostMapping(value = "/finishAdjust", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7, author = "sx")
    @ApiOperation(value = "应收调整-工作流结束回调", notes = "应收调整-工作流结束回调", hidden = false)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<Object> finishAdjust(@RequestBody @Validated CallBakParamsVo vo){
        AppReply appReply = new AppReply(AppReply.SUCCESS_CODE,AppReply.SUCCESS_MSG,null);
        adjustService.finishAdjust(vo);
        return appReply;
    }

    /**
     * selectByPageRecord 账单管理--分页查询
     * @param vo 需要查询的条件
     * @return  com.bonc.ioc.common.util.AppReply 分页查询的数据
     * @since 1.0.0
     * <AUTHOR>
     * @date  2022-12-02
     * @change
     * 2022-12-02 by binghong.tang for init
     */
    @GetMapping(value = "/bbpmBillManagementEntity/selectByPage", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10, author = "binghong.tang")
    @ApiOperation(value = "001-账单管理--分页查询", notes = "账单管理--分页查询", hidden = false,position = 4)
    @ApiImplicitParam(name = "tokenId", value = "tokenId", required = true, paramType = "header", example = "1")
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> selectByPageRecord(BbpmBillManagementPageVo vo){
        return adjustService.selectByPageRecord(vo);
    }

}

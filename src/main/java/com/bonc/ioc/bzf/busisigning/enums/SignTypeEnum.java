package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 签约类型 枚举类
 *
 * <AUTHOR>
 * @since 2023/5/13
 */
public enum SignTypeEnum {

    /**
     * 散租
     */
    PERSONAL_RENTAL("01", "散租"),

    /**
     * 趸租
     */
    WHOLESALE_RENTAL("02", "趸租"),

    /**
     * 管理协议
     */
    WHOLESALE_AGREEMENT("03", "管理协议"),

    /**
     * 商租
     */
    BUSINESS_STUDIES("07", "商租");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 合同字典id
     */
    private String contractDictId;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code           编号
     * @param desc           描述信息
     */
    SignTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    SignTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        SignTypeEnum[] enums = values();
        for (SignTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述信息查询编号
     *
     * @param desc 描述信息
     * @return 编号
     */
    public static String getCodeByDesc(String desc) {
        SignTypeEnum[] enums = values();
        for (SignTypeEnum en : enums) {
            if (en.getDesc().equals(desc)) {
                return en.getCode();
            }
        }
        return null;
    }
}

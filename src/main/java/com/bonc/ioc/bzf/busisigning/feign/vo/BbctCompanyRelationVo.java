package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业与房关系(客户中心) vo实体类
 *
 * <AUTHOR>
 * @since 2023/6/9
 */
@Data
@ApiModel(value = "企业与房关系(客户中心) vo实体", description = "企业与房关系(客户中心) vo实体")
public class BbctCompanyRelationVo extends McpBaseVo implements Serializable {

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /**
     * 小区编码
     */
    @ApiModelProperty(value = "小区编码")
    private String communityCode;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractCode;

    /**
     * 房屋编码
     */
    @ApiModelProperty(value = "房屋编码")
    private String houseCode;

    /**
     * 房源编码
     */
    @ApiModelProperty(value = "房源编码")
    private String houseResourcesCode;

    /**
     * 运营项目ID
     */
    @ApiModelProperty(value = "运营项目ID")
    private String operateProjectId;

    /**
     * 机构客户ID
     */
    @ApiModelProperty(value = "机构客户ID")
    private String orgCustomerId;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
    private Date signingDate;

    /**
     * 签约业务类型编码
     */
    @ApiModelProperty(value = "签约业务类型编码")
    private String businessTypeCode;
}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 代扣代缴-银行卡信息
 */
@ApiModel(value="BbsBankInfoVo对象", description="代扣代缴-银行卡信息")
@Data
public class BbsBankInfoVo extends McpBaseVo implements Serializable{

    @ApiModelProperty(value = "租户表id")
    private String rcId;

    @ApiModelProperty(value = "签约表id（bbs_sign_info）")
    private String signId;

    @ApiModelProperty(value = "开户姓名")
    private String customerName;

    @ApiModelProperty(value = "开户行手机号")
    private String bankPhone;

    @ApiModelProperty(value = "开户银行")
    private String bankName;

    @ApiModelProperty(value = "开户银行编码")
    private String bankNameCode;

    @ApiModelProperty(value = "银行卡号")
    private String bankCard;

    @ApiModelProperty(value = "是否鉴权")
    private String bankIsAuthentication;

    @ApiModelProperty(value = "是否签署代扣代缴协议")
    private String bankIsAgreement;

    @ApiModelProperty(value = "协议编号")
    private String bankAgreementNo;

    @ApiModelProperty(value = "身份证号")
    private String customerIdNumber;

    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date contractBeginTime;

    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date contractEndTime;

    @ApiModelProperty(value = "支行编码")
    private String bankSubbranchCode;

    @ApiModelProperty(value = "支行名称")
    private String bankSubbranchName;

    @ApiModelProperty(value = "银行分类代码")
    private String bankNccCategoryCode;

    @ApiModelProperty(value = "开通代扣代缴服务资料地址（PC端）")
    private String bankDataUrls;

    @ApiModelProperty(value = "代扣代缴开通类型（PC端）")
    private String bankAgreementType;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "调用接口来源 是否合同变更(1是 0否)")
    private String isContractChange = "0";

    @ApiModelProperty(value = "短信验证码")
    private String textMessageCode;

    @ApiModelProperty(value = "合同号+协议号（合同变更）")
    private String prefixRedisKey;
}

package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 签约-递增设置（当租金/物业费是否递增标识为1时有效） 实体类
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@ApiModel(value="BbsRenewalIncrementalConfigPageVo对象", description="签约-递增设置（当租金/物业费是否递增标识为1时有效）")
public class BbsRenewalIncrementalConfigPageVo extends McpBasePageVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String sicId;

    /**
     * 签约ID
     */
    @ApiModelProperty(value = "签约ID")
                            private String signInfoId;

    /**
     * 标准类型（rent：租金，prop：物业费）
     */
    @ApiModelProperty(value = "标准类型（rent：租金，prop：物业费）")
                            private String standardType;

    /**
     * 调节点，appoint:第，every:每
     */
    @ApiModelProperty(value = "调节点，appoint:第，every:每")
                            private String adjustmentPoint;

    /**
     * 调节点名称
     */
    @ApiModelProperty(value = "调节点名称")
                            private String adjustmentPointName;

    /**
     * 时间点
     */
    @ApiModelProperty(value = "时间点")
                            private Integer timePoint;

    /**
     * 单位，year:年，month:月
     */
    @ApiModelProperty(value = "单位，year:年，month:月")
                            private String unit;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
                            private String unitName;

    /**
     * 涨幅，百分号前数值
     */
    @ApiModelProperty(value = "涨幅，百分号前数值")
                            private Double increase;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    @NotNull(message = "删除标志;删除标识（1 未删除 0 已删除）不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private Integer delFlag;

    /**
     * @return 主键
     */
    public String getSicId() {
        return sicId;
    }

    public void setSicId(String sicId) {
        this.sicId = sicId;
    }

    /**
     * @return 签约ID
     */
    public String getSignInfoId() {
        return signInfoId;
    }

    public void setSignInfoId(String signInfoId) {
        this.signInfoId = signInfoId;
    }

    /**
     * @return 标准类型（rent：租金，prop：物业费）
     */
    public String getStandardType() {
        return standardType;
    }

    public void setStandardType(String standardType) {
        this.standardType = standardType;
    }

    /**
     * @return 调节点，appoint:第，every:每
     */
    public String getAdjustmentPoint() {
        return adjustmentPoint;
    }

    public void setAdjustmentPoint(String adjustmentPoint) {
        this.adjustmentPoint = adjustmentPoint;
    }

    /**
     * @return 调节点名称
     */
    public String getAdjustmentPointName() {
        return adjustmentPointName;
    }

    public void setAdjustmentPointName(String adjustmentPointName) {
        this.adjustmentPointName = adjustmentPointName;
    }

    /**
     * @return 时间点
     */
    public Integer getTimePoint() {
        return timePoint;
    }

    public void setTimePoint(Integer timePoint) {
        this.timePoint = timePoint;
    }

    /**
     * @return 单位，year:年，month:月
     */
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    /**
     * @return 单位名称
     */
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * @return 涨幅，百分号前数值
     */
    public Double getIncrease() {
        return increase;
    }

    public void setIncrease(Double increase) {
        this.increase = increase;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsRenewalIncrementalConfigPageVo{" +
            "sicId=" + sicId +
            ", signInfoId=" + signInfoId +
            ", standardType=" + standardType +
            ", adjustmentPoint=" + adjustmentPoint +
            ", adjustmentPointName=" + adjustmentPointName +
            ", timePoint=" + timePoint +
            ", unit=" + unit +
            ", unitName=" + unitName +
            ", increase=" + increase +
            ", delFlag=" + delFlag +
        "}";
    }
}

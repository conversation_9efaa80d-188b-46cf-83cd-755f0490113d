package com.bonc.ioc.bzf.busisigning.vo.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="3.59.商业合同变更退款生成付款单接口ResultVo", description="3.59.商业合同变更退款生成付款单接口ResultVo")
public class BusinessGeneratePaymentByGXResultVo implements java.io.Serializable{

    @ApiModelProperty(value = "付款单唯一标识")
    private String paymentCode;
}

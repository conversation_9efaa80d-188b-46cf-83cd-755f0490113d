package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.bzf.utils.common.convert.CopyFieldPoint;
import com.bonc.ioc.bzf.utils.common.convert.CopyFieldUtil;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 签约规则信息表 实体类
 *
 * <AUTHOR>
 * @date 2022-11-29
 * @change 2022-11-29 by ly for init
 */
@ApiModel(value = "BbsiRuleInfoVo对象", description = "签约规则信息表")
public class BbsiRuleInfoVo extends McpBaseVo implements Serializable {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空", groups = {UpdateValidatorGroup.class})
    private String signRuleId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 查询银行用的项目id
     */
    @ApiModelProperty(value = "查询银行用的项目id")
    private String projectIdBank;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 合同模板id
     */
    @ApiModelProperty(value = "合同模板id")
    private String contractTemplateId;

    /**
     * 签约告知书id
     */
    @ApiModelProperty(value = "签约告知书id")
    private String signNoticeId;

    /**
     * 租金标准id
     */
    @ApiModelProperty(value = "租金标准id")
    @McpDictPoint(dictCode = "RENT_STANDARD", overTransCopyTo = "rentStandardIdText")
    private String rentStandardId;

    /**
     * 租金标准
     */
    @ApiModelProperty(value = "租金标准")
    private String rentStandardIdText;


    /**
     * 缴费周期code
     */
    @ApiModelProperty(value = "缴费周期code")
    @McpDictPoint(dictCode = "PAYMENT_CYCLE", overTransCopyTo = "paymentCycleCodeText")
    private String paymentCycleCode;
    /**
     * 缴费周期
     */
    @ApiModelProperty(value = "缴费周期")
    private String paymentCycleCodeText;


    /**
     * 押金标准code
     */
    @ApiModelProperty(value = "押金标准code")
    @McpDictPoint(dictCode = "DEPOSIT_STANDARD", overTransCopyTo = "cashPledgeCodeText")
    private String cashPledgeCode;

    /**
     * 押金标准
     */
    @ApiModelProperty(value = "押金标准")
    private String cashPledgeCodeText;

    /**
     * sign_in_begin_time签约开始时间
     */
    @ApiModelProperty(value = "sign_in_begin_time签约开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signInBeginTime;

    /**
     * 签约结束时间
     */
    @ApiModelProperty(value = "签约结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signInEndTime;

    /**
     * 租赁开始日期
     */
    @ApiModelProperty(value = "租赁开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date rentBeginTime;

    /**
     * 租赁结束日期
     */
    @ApiModelProperty(value = "租赁结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date rentEndTime;

    /**
     * 收款银行
     */
    @ApiModelProperty(value = "收款银行")
    private String dueBank;

    /**
     * 通知消息模板id
     */
    @ApiModelProperty(value = "通知消息模板id")
    private String noticeTemplateId;

    /**
     * 短信消息模板id
     */
    @ApiModelProperty(value = "短信消息模板id")
    private String messageTemplateId;

    /**
     * 是否报送热力公司
     */
    @ApiModelProperty(value = "是否报送热力公司")
    @McpDictPoint(dictCode = "YES_NO", overTransCopyTo = "isHeatingCompanyText")
    private String isHeatingCompany;

    @ApiModelProperty(value = "是否报送热力公司")
    private String isHeatingCompanyText;

    /**
     * 是否同时办理入
     */
    @ApiModelProperty(value = "是否同时办理入")
    @McpDictPoint(dictCode = "YES_NO", overTransCopyTo = "isMoveIntoText")
    private String isMoveInto;

    @ApiModelProperty(value = "是否同时办理入")
    private String isMoveIntoText;

    /**
     * 租金规则id
     */
    @ApiModelProperty(value = "租金规则id")
    private String rentRulesId;

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    @McpDictPoint(dictCode = "PUBLISH_STATUS", overTransCopyTo = "publishStatusText")
    private String publishStatus;


    @ApiModelProperty(value = "发布状态名称")
    private String publishStatusText;

    public String getPublishStatusText() {
        return publishStatusText;
    }

    public void setPublishStatusText(String publishStatusText) {
        this.publishStatusText = publishStatusText;
    }

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * @return 主键id
     */
    public String getSignRuleId() {
        return signRuleId;
    }


    /**
     * 组织机构id
     */
    @ApiModelProperty(value = "组织机构id")
    private String orgId;


    /**
     * 租金计算参数
     */
    @ApiModelProperty(value = "租金计算参数")
    private String rentRulesParams;

    @ApiModelProperty(value = "租金计算规则名称")
    private String chargeRuleName;

    @ApiModelProperty(value = "租金计算规则编号")
    private String chargeRuleNo;


    @ApiModelProperty(value = "配租计划id")
    private String peizuPlanId;


    @ApiModelProperty(value = "配租计划名称")
    private String peizuPlanName;


    @ApiModelProperty(value = "发布人")
    private String publishUser;

    @ApiModelProperty(value = "入住总人数")
    private String checkInfoNum;


    @ApiModelProperty(value = "签约总人数")
    private String signNum;

    /**
     * 合同模板名称
     */
    @ApiModelProperty(value = "合同模板名称")
    @CopyFieldPoint(fieldName = "name", type = CopyFieldUtil.TYPE_CONTRACT)
    private String contractTemplateName;


    /**
     * 签约告知书名称
     */
    @ApiModelProperty(value = "签约告知书名称")
    private String signNoticeName;
    /**
     * 消息通知模板名称
     */
    @ApiModelProperty(value = "通知消息模板名称")
    @CopyFieldPoint(fieldName = "messTemplateName", type = CopyFieldUtil.TYPE_MESSAGE_NOTICE)
    private String noticeTemplateName;

    /**
     * 短信消息模板名称
     */
    @ApiModelProperty(value = "短信消息模板名称")
    @CopyFieldPoint(fieldName = "messTemplateName", type = CopyFieldUtil.TYPE_MESSAGE_MESSAGE)
    private String messageTemplateName;

    /**
     * 收款银行编码
     */
    @ApiModelProperty(value = "收款银行编码")
    private String dueBankCode;


    /**
     * 收款银行支行
     */
    @ApiModelProperty(value = "收款银行支行编码")
    private String bankBranchCode;

    /**
     * 收款银行支行
     */
    @ApiModelProperty(value = "收款银行支行名称")
    private String bankBranchName;


    /**
     * 签约计划编号
     */
    @ApiModelProperty(value = "签约计划编号")
    private String signPlanCode;


    /**
     * 是否查验 1-查验
     */
    @ApiModelProperty(value = "是否查验 1-查验")
    private String checkStatus;

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getSignPlanCode() {
        return signPlanCode;
    }

    public void setSignPlanCode(String signPlanCode) {
        this.signPlanCode = signPlanCode;
    }

    public String getIsHeatingCompanyText() {
        return isHeatingCompanyText;
    }

    public void setIsHeatingCompanyText(String isHeatingCompanyText) {
        this.isHeatingCompanyText = isHeatingCompanyText;
    }

    public String getIsMoveIntoText() {
        return isMoveIntoText;
    }

    public void setIsMoveIntoText(String isMoveIntoText) {
        this.isMoveIntoText = isMoveIntoText;
    }

    public String getBankBranchCode() {
        return bankBranchCode;
    }

    public void setBankBranchCode(String bankBranchCode) {
        this.bankBranchCode = bankBranchCode;
    }

    public String getBankBranchName() {
        return bankBranchName;
    }

    public void setBankBranchName(String bankBranchName) {
        this.bankBranchName = bankBranchName;
    }

    public String getDueBankCode() {
        return dueBankCode;
    }

    public void setDueBankCode(String dueBankCode) {
        this.dueBankCode = dueBankCode;
    }

    public String getContractTemplateName() {
        return contractTemplateName;
    }

    public void setContractTemplateName(String contractTemplateName) {
        this.contractTemplateName = contractTemplateName;
    }

    public String getSignNoticeName() {
        return signNoticeName;
    }

    public void setSignNoticeName(String signNoticeName) {
        this.signNoticeName = signNoticeName;
    }

    public String getNoticeTemplateName() {
        return noticeTemplateName;
    }

    public void setNoticeTemplateName(String noticeTemplateName) {
        this.noticeTemplateName = noticeTemplateName;
    }

    public String getMessageTemplateName() {
        return messageTemplateName;
    }

    public void setMessageTemplateName(String messageTemplateName) {
        this.messageTemplateName = messageTemplateName;
    }

    public String getCheckInfoNum() {
        return checkInfoNum;
    }

    public void setCheckInfoNum(String checkInfoNum) {
        this.checkInfoNum = checkInfoNum;
    }

    public String getSignNum() {
        return signNum;
    }

    public void setSignNum(String signNum) {
        this.signNum = signNum;
    }

    public String getPeizuPlanId() {
        return peizuPlanId;
    }

    public void setPeizuPlanId(String peizuPlanId) {
        this.peizuPlanId = peizuPlanId;
    }

    public String getPeizuPlanName() {
        return peizuPlanName;
    }

    public void setPeizuPlanName(String peizuPlanName) {
        this.peizuPlanName = peizuPlanName;
    }

    public String getPublishUser() {
        return publishUser;
    }

    public void setPublishUser(String publishUser) {
        this.publishUser = publishUser;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;


    public String getChargeRuleName() {
        return chargeRuleName;
    }

    public void setChargeRuleName(String chargeRuleName) {
        this.chargeRuleName = chargeRuleName;
    }

    public String getChargeRuleNo() {
        return chargeRuleNo;
    }

    public void setChargeRuleNo(String chargeRuleNo) {
        this.chargeRuleNo = chargeRuleNo;
    }


    /**
     * 入住规则子类
     */
    private BbsiBbciRuleVo bbrv;


    public BbsiBbciRuleVo getBbrv() {
        return bbrv;
    }

    public void setBbrv(BbsiBbciRuleVo bbrv) {
        this.bbrv = bbrv;
    }

    public String getRentRulesParams() {
        return rentRulesParams;
    }

    public void setRentRulesParams(String rentRulesParams) {
        this.rentRulesParams = rentRulesParams;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public void setSignRuleId(String signRuleId) {
        this.signRuleId = signRuleId;
    }

    /**
     * @return 规则名称
     */
    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    /**
     * @return 项目id
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectIdBank() {
        return projectIdBank;
    }

    public void setProjectIdBank(String projectIdBank) {
        this.projectIdBank = projectIdBank;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 合同模板id
     */
    public String getContractTemplateId() {
        return contractTemplateId;
    }

    public void setContractTemplateId(String contractTemplateId) {
        this.contractTemplateId = contractTemplateId;
    }

    /**
     * @return 签约告知书id
     */
    public String getSignNoticeId() {
        return signNoticeId;
    }

    public void setSignNoticeId(String signNoticeId) {
        this.signNoticeId = signNoticeId;
    }

    /**
     * @return 租金标准id
     */
    public String getRentStandardId() {
        return rentStandardId;
    }

    public void setRentStandardId(String rentStandardId) {
        this.rentStandardId = rentStandardId;
    }

    /**
     * @return 缴费周期code
     */
    public String getPaymentCycleCode() {
        return paymentCycleCode;
    }

    public void setPaymentCycleCode(String paymentCycleCode) {
        this.paymentCycleCode = paymentCycleCode;
    }

    /**
     * @return 押金标准code
     */
    public String getCashPledgeCode() {
        return cashPledgeCode;
    }

    public void setCashPledgeCode(String cashPledgeCode) {
        this.cashPledgeCode = cashPledgeCode;
    }

    /**
     * @return sign_in_begin_time签约开始时间
     */
    public Date getSignInBeginTime() {
        if (signInBeginTime != null) {
            return (Date) signInBeginTime.clone();
        } else {
            return null;
        }
    }

    public void setSignInBeginTime(Date signInBeginTime) {
        if (signInBeginTime == null) {
            this.signInBeginTime = null;
        } else {
            this.signInBeginTime = (Date) signInBeginTime.clone();
        }
    }

    public String getRentStandardIdText() {
        return rentStandardIdText;
    }

    public void setRentStandardIdText(String rentStandardIdText) {
        this.rentStandardIdText = rentStandardIdText;
    }

    public String getPaymentCycleCodeText() {
        return paymentCycleCodeText;
    }

    public void setPaymentCycleCodeText(String paymentCycleCodeText) {
        this.paymentCycleCodeText = paymentCycleCodeText;
    }

    public String getCashPledgeCodeText() {
        return cashPledgeCodeText;
    }

    public void setCashPledgeCodeText(String cashPledgeCodeText) {
        this.cashPledgeCodeText = cashPledgeCodeText;
    }

    /**
     * @return 签约结束时间
     */
    public Date getSignInEndTime() {
        if (signInEndTime != null) {
            return (Date) signInEndTime.clone();
        } else {
            return null;
        }
    }

    public void setSignInEndTime(Date signInEndTime) {
        if (signInEndTime == null) {
            this.signInEndTime = null;
        } else {
            this.signInEndTime = (Date) signInEndTime.clone();
        }
    }

    /**
     * @return 租赁开始日期
     */
    public Date getRentBeginTime() {
        if (rentBeginTime != null) {
            return (Date) rentBeginTime.clone();
        } else {
            return null;
        }
    }

    public void setRentBeginTime(Date rentBeginTime) {
        if (rentBeginTime == null) {
            this.rentBeginTime = null;
        } else {
            this.rentBeginTime = (Date) rentBeginTime.clone();
        }
    }

    /**
     * @return 租赁结束日期
     */
    public Date getRentEndTime() {
        if (rentEndTime != null) {
            return (Date) rentEndTime.clone();
        } else {
            return null;
        }
    }

    public void setRentEndTime(Date rentEndTime) {
        if (rentEndTime == null) {
            this.rentEndTime = null;
        } else {
            this.rentEndTime = (Date) rentEndTime.clone();
        }
    }

    /**
     * @return 收款银行
     */
    public String getDueBank() {
        return dueBank;
    }

    public void setDueBank(String dueBank) {
        this.dueBank = dueBank;
    }

    /**
     * @return 通知消息模板id
     */
    public String getNoticeTemplateId() {
        return noticeTemplateId;
    }

    public void setNoticeTemplateId(String noticeTemplateId) {
        this.noticeTemplateId = noticeTemplateId;
    }

    /**
     * @return 短信消息模板id
     */
    public String getMessageTemplateId() {
        return messageTemplateId;
    }

    public void setMessageTemplateId(String messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    /**
     * @return 是否报送热力公司
     */
    public String getIsHeatingCompany() {
        return isHeatingCompany;
    }

    public void setIsHeatingCompany(String isHeatingCompany) {
        this.isHeatingCompany = isHeatingCompany;
    }

    /**
     * @return 是否同时办理入
     */
    public String getIsMoveInto() {
        return isMoveInto;
    }

    public void setIsMoveInto(String isMoveInto) {
        this.isMoveInto = isMoveInto;
    }

    /**
     * @return 租金规则id
     */
    public String getRentRulesId() {
        return rentRulesId;
    }

    public void setRentRulesId(String rentRulesId) {
        this.rentRulesId = rentRulesId;
    }

    /**
     * @return 发布状态
     */
    public String getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(String publishStatus) {
        this.publishStatus = publishStatus;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }


    @Override
    public String toString() {
        return "BbsiRuleInfoVo{" +
                "signRuleId=" + signRuleId +
                ", ruleName=" + ruleName +
                ", projectId=" + projectId +
                ", projectName=" + projectName +
                ", contractTemplateId=" + contractTemplateId +
                ", signNoticeId=" + signNoticeId +
                ", rentStandardId=" + rentStandardId +
                ", paymentCycleCode=" + paymentCycleCode +
                ", cashPledgeCode=" + cashPledgeCode +
                ", signInBeginTime=" + signInBeginTime +
                ", signInEndTime=" + signInEndTime +
                ", rentBeginTime=" + rentBeginTime +
                ", rentEndTime=" + rentEndTime +
                ", dueBank=" + dueBank +
                ", noticeTemplateId=" + noticeTemplateId +
                ", messageTemplateId=" + messageTemplateId +
                ", isHeatingCompany=" + isHeatingCompany +
                ", isMoveInto=" + isMoveInto +
                ", rentRulesId=" + rentRulesId +
                ", publishStatus=" + publishStatus +
                ", delFlag=" + delFlag +
                "}";
    }
}

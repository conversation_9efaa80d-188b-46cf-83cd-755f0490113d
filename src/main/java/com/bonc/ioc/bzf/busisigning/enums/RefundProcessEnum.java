package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 退款状态 枚举类
 *
 * <AUTHOR>
 * @since 2024/8/28
 */
public enum RefundProcessEnum {

    /**
     * 未退款
     */
    NO_REFUND("1", "未退款"),

    /**
     * 退款失败
     */
    REFUND_FAILED("2", "退款失败"),

    /**
     * 已退款
     */
    REFUND_SUCCESS("3", "已退款");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    RefundProcessEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    RefundProcessEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        RefundProcessEnum[] enums = values();
        for (RefundProcessEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}

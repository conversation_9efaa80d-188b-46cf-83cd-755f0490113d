package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 合同变更租金变更产品表 实体类
 *
 * <AUTHOR>
 * @date 2024-09-09
 * @change 2024-09-09 by tbh for init
 */
@ApiModel(value="BbsChangeResultProductPageVo对象", description="合同变更租金变更产品表")
public class BbsChangeResultProductPageVo extends McpBasePageVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String pdctId;

    /**
     * 合同变更表主键id
     */
    @ApiModelProperty(value = "合同变更表主键id")
                            private String ccId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
                            private String productName;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
                            private String productNo;

    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
                            private String houseStructArea;

    /**
     * 租金标准单位
     */
    @ApiModelProperty(value = "租金标准单位")
                            private String rentStandardUnit;

    /**
     * 租金标准单位名称
     */
    @ApiModelProperty(value = "租金标准单位名称")
                            private String rentStandardUnitName;

    /**
     * 租金标准
     */
    @ApiModelProperty(value = "租金标准")
                            private Double rentStandard;

    /**
     * bbs_result_product表主键
     */
    @ApiModelProperty(value = "bbs_result_product表主键")
                            private String rpId;

    /**
     * bbs_result_relation人-产品关系外键
     */
    @ApiModelProperty(value = "bbs_result_relation人-产品关系外键")
                            private String rrId;

    /**
     * 变更前产品名称
     */
    @ApiModelProperty(value = "变更前产品名称")
                            private String productNameOld;

    /**
     * 变更前产品编号
     */
    @ApiModelProperty(value = "变更前产品编号")
                            private String productNoOld;

    /**
     * 变更前建筑面积
     */
    @ApiModelProperty(value = "变更前建筑面积")
                            private String houseStructAreaOld;

    /**
     * 变更前租金标准单位
     */
    @ApiModelProperty(value = "变更前租金标准单位")
                            private String rentStandardUnitOld;

    /**
     * 变更前租金标准单位名称
     */
    @ApiModelProperty(value = "变更前租金标准单位名称")
                            private String rentStandardUnitNameOld;

    /**
     * 变更前租金标准
     */
    @ApiModelProperty(value = "变更前租金标准")
                            private Double rentStandardOld;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * @return 主键
     */
    public String getPdctId() {
        return pdctId;
    }

    public void setPdctId(String pdctId) {
        this.pdctId = pdctId;
    }

    /**
     * @return 合同变更表主键id
     */
    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }

    /**
     * @return 产品名称
     */
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * @return 产品编号
     */
    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    /**
     * @return 建筑面积
     */
    public String getHouseStructArea() {
        return houseStructArea;
    }

    public void setHouseStructArea(String houseStructArea) {
        this.houseStructArea = houseStructArea;
    }

    /**
     * @return 租金标准单位
     */
    public String getRentStandardUnit() {
        return rentStandardUnit;
    }

    public void setRentStandardUnit(String rentStandardUnit) {
        this.rentStandardUnit = rentStandardUnit;
    }

    /**
     * @return 租金标准单位名称
     */
    public String getRentStandardUnitName() {
        return rentStandardUnitName;
    }

    public void setRentStandardUnitName(String rentStandardUnitName) {
        this.rentStandardUnitName = rentStandardUnitName;
    }

    /**
     * @return 租金标准
     */
    public Double getRentStandard() {
        return rentStandard;
    }

    public void setRentStandard(Double rentStandard) {
        this.rentStandard = rentStandard;
    }

    /**
     * @return bbs_result_product表主键
     */
    public String getRpId() {
        return rpId;
    }

    public void setRpId(String rpId) {
        this.rpId = rpId;
    }

    /**
     * @return bbs_result_relation人-产品关系外键
     */
    public String getRrId() {
        return rrId;
    }

    public void setRrId(String rrId) {
        this.rrId = rrId;
    }

    /**
     * @return 变更前产品名称
     */
    public String getProductNameOld() {
        return productNameOld;
    }

    public void setProductNameOld(String productNameOld) {
        this.productNameOld = productNameOld;
    }

    /**
     * @return 变更前产品编号
     */
    public String getProductNoOld() {
        return productNoOld;
    }

    public void setProductNoOld(String productNoOld) {
        this.productNoOld = productNoOld;
    }

    /**
     * @return 变更前建筑面积
     */
    public String getHouseStructAreaOld() {
        return houseStructAreaOld;
    }

    public void setHouseStructAreaOld(String houseStructAreaOld) {
        this.houseStructAreaOld = houseStructAreaOld;
    }

    /**
     * @return 变更前租金标准单位
     */
    public String getRentStandardUnitOld() {
        return rentStandardUnitOld;
    }

    public void setRentStandardUnitOld(String rentStandardUnitOld) {
        this.rentStandardUnitOld = rentStandardUnitOld;
    }

    /**
     * @return 变更前租金标准单位名称
     */
    public String getRentStandardUnitNameOld() {
        return rentStandardUnitNameOld;
    }

    public void setRentStandardUnitNameOld(String rentStandardUnitNameOld) {
        this.rentStandardUnitNameOld = rentStandardUnitNameOld;
    }

    /**
     * @return 变更前租金标准
     */
    public Double getRentStandardOld() {
        return rentStandardOld;
    }

    public void setRentStandardOld(Double rentStandardOld) {
        this.rentStandardOld = rentStandardOld;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsChangeResultProductPageVo{" +
            "pdctId=" + pdctId +
            ", ccId=" + ccId +
            ", productName=" + productName +
            ", productNo=" + productNo +
            ", houseStructArea=" + houseStructArea +
            ", rentStandardUnit=" + rentStandardUnit +
            ", rentStandardUnitName=" + rentStandardUnitName +
            ", rentStandard=" + rentStandard +
            ", rpId=" + rpId +
            ", rrId=" + rrId +
            ", productNameOld=" + productNameOld +
            ", productNoOld=" + productNoOld +
            ", houseStructAreaOld=" + houseStructAreaOld +
            ", rentStandardUnitOld=" + rentStandardUnitOld +
            ", rentStandardUnitNameOld=" + rentStandardUnitNameOld +
            ", rentStandardOld=" + rentStandardOld +
            ", delFlag=" + delFlag +
        "}";
    }
}

package com.bonc.ioc.bzf.busisigning.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: hechengyao
 * @createDate: 2023-09-26
 * @Version 1.0
 **/

@Data
@ApiModel(value="BbsRenewalApplyInfoProductVo对象", description="商铺地址级联查询")
public class BbsRenewalApplyInfoProductVo {

    /**
     * 申请信息id
     */
    @ApiModelProperty(value = "申请信息id")
    private String renewalApplyInfoId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 小区或楼宇
     */
    @ApiModelProperty(value = "小区或楼宇")
    private String communityBuildingNo;

    /**
     * 小区或楼宇名称
     */
    @ApiModelProperty(value = "小区或楼宇名称")
    private String communityBuildingName;

    /**
     * 组团(组团名称)
     */
    @ApiModelProperty(value = "组团(组团名称)")
    private String groupNo;

    /**
     * 组团名称
     */
    @ApiModelProperty(value = "组团名称")
    private String groupName;

    /**
     * 楼号
     */
    @ApiModelProperty(value = "楼号")
    private String buildingNo;

    /**
     * 楼名称
     */
    @ApiModelProperty(value = "楼名称")
    private String buildingName;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    private String unitNo;

    /**
     * 单元名称
     */
    @ApiModelProperty(value = "单元名称")
    private String unitName;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String houseNo;


    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private Integer delFlag;

}

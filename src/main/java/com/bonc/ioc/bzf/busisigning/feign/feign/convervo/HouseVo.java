package com.bonc.ioc.bzf.busisigning.feign.feign.convervo;

import com.bonc.ioc.bzf.utils.common.convert.CopyFieldPoint;
import com.bonc.ioc.bzf.utils.common.convert.CopyFieldUtil;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * @ClassName HouseVo
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2023-06-04 14:11
 **/
@Data
@ApiModel(value="同步数据房态VO", description="同步数据房态VO")
public class HouseVo implements java.io.Serializable{

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String rpId;

    /**
     * 人-产品关系外键
     */
    @ApiModelProperty(value = "人-产品关系外键")
    private String rrId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @CopyFieldPoint(fieldName = "houseAddr",type = CopyFieldUtil.TYPE_HOUSE)
    private String productName;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
    @CopyFieldPoint(fieldName = "houseCode",type = CopyFieldUtil.TYPE_HOUSE)
    private String productNo;


    @ApiModelProperty(value = "项目编号NCC编码")
    @CopyFieldPoint(fieldName = "businessSystemProjectCode",type = CopyFieldUtil.TYPE_PROJECT)
    @NotBlank(message = "项目编号NCC编码[projectNoNcc:businessSystemProjectCode]不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String projectNoNcc;
    public String getProjectNoNcc() {
        return projectNoNcc;
    }

    public void setProjectNoNcc(String projectNoNcc) {
        this.projectNoNcc = projectNoNcc;
    }

    @ApiModelProperty(value = "房源标号NCC编码")

    private String houseNoNcc;

    public String getHouseNoNcc() {
        return houseNoNcc;
    }

    public void setHouseNoNcc(String houseNoNcc) {
        this.houseNoNcc = houseNoNcc;
    }

    /**
     * 项目编号/运营项目
     */
    @ApiModelProperty(value = "项目编号/运营项目")
    @CopyFieldPoint(fieldName = "projectId",type= CopyFieldUtil.TYPE_PROJECT)
    @NotBlank(message = "项目编号/运营项目[projectNo:projectCode]不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String projectNo;

    @ApiModelProperty(value = "项目名称")
    @CopyFieldPoint(fieldName = "projectName",type= CopyFieldUtil.TYPE_PROJECT)
    @NotBlank(message = "项目名称[projectName:projectName]不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String projectName;

    /**
     * 租金标准（元/m²/月）
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）")
    @CopyFieldPoint(fieldName = "rentStandard",type= CopyFieldUtil.TYPE_HOUSE)
    private String rentStandardNo;

    /**
     * 租金标准（元/m²/月）名称
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）名称")
    private String rentStandardName;



    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
    private Double rent;



    /**
     * 选房渠道
     */
    @ApiModelProperty(value = "选房渠道")
    private String houseSelectionChannelsNo;

    /**
     * 选房渠道名称
     */
    @ApiModelProperty(value = "选房渠道名称")
    private String houseSelectionChannelsName;

    /**
     * 小区或楼宇
     */
    @ApiModelProperty(value = "小区或楼宇")
    @CopyFieldPoint(fieldName = "communityCode",type= CopyFieldUtil.TYPE_HOUSE)
    private String communityBuildingNo;

    /**
     * 小区或楼宇名称
     */
    @ApiModelProperty(value = "小区或楼宇名称")
    @CopyFieldPoint(fieldName = "communityName",type= CopyFieldUtil.TYPE_HOUSE)
    private String communityBuildingName;

    /**
     * 组团
     */
    @ApiModelProperty(value = "组团")
    @CopyFieldPoint(fieldName = "groupNo",type= CopyFieldUtil.TYPE_HOUSE)
    private String groupNo;

    /**
     * 组团名称
     */
    @ApiModelProperty(value = "组团名称")
    @CopyFieldPoint(fieldName = "groupName",type= CopyFieldUtil.TYPE_HOUSE)
    private String groupName;

    /**
     * 楼号
     */
    @ApiModelProperty(value = "楼号")
    @CopyFieldPoint(fieldName = "buildCode",type= CopyFieldUtil.TYPE_HOUSE)
    private String buildingNo;

    /**
     * 楼名称
     */
    @ApiModelProperty(value = "楼名称")
    @CopyFieldPoint(fieldName = "buildName",type= CopyFieldUtil.TYPE_HOUSE)
    private String buildingName;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    @CopyFieldPoint(fieldName = "unitCode",type= CopyFieldUtil.TYPE_HOUSE)
    private String unitNo;

    /**
     * 单元名称
     */
    @ApiModelProperty(value = "单元名称")
    @CopyFieldPoint(fieldName = "unitName",type= CopyFieldUtil.TYPE_HOUSE)
    private String unitName;

    /**
     * 户型
     */
    @ApiModelProperty(value = "户型")
    @CopyFieldPoint(fieldName = "modelId",type= CopyFieldUtil.TYPE_HOUSE)
    private String houseTypeNo;

    /**
     * 户型名称
     */
    @ApiModelProperty(value = "户型名称")
    @CopyFieldPoint(fieldName = "modelName",type= CopyFieldUtil.TYPE_HOUSE)
    private String houseTypeName;

    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
    @CopyFieldPoint(fieldName = "buildArea",type= CopyFieldUtil.TYPE_HOUSE)
    private String houseStructArea;


    /**
     * 套型
     */
    @ApiModelProperty(value = "套型")
    @CopyFieldPoint(fieldName = "jacketedName",type= CopyFieldUtil.TYPE_HOUSE)
    private String jacketed;



    @ApiModelProperty(value = "套型编码")
    @CopyFieldPoint(fieldName = "jacketed",type= CopyFieldUtil.TYPE_HOUSE)
    private String jacketedCode;



    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @CopyFieldPoint(fieldName = "projectId",type= CopyFieldUtil.TYPE_HOUSE)
    @NotBlank(message = "项目名称[projectId:projectId]不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String projectId;
    /**
     * 房间名称
     */
    @ApiModelProperty(value = "房间名称")
    @CopyFieldPoint(fieldName = "roomNum",type= CopyFieldUtil.TYPE_HOUSE)
    private String roomName;

    /**
     * 小区地址
     */
    @ApiModelProperty(value = "小区地址")
    @CopyFieldPoint(fieldName = "infoAddr",type= CopyFieldUtil.TYPE_HOUSE)
    private String communityAddress;

    /**
     * 小区所在区
     */
    @ApiModelProperty(value = "小区所在区")
    @CopyFieldPoint(fieldName = "houseRegion",type= CopyFieldUtil.TYPE_HOUSE)
    private String communityRegion;
    /**
     * 所在层
     */
    @ApiModelProperty(value = "所在层")
    @CopyFieldPoint(fieldName = "houseFloor",type= CopyFieldUtil.TYPE_HOUSE)
    private String currentFloorNo;

    /**
     * 总层数
     */
    @ApiModelProperty(value = "总层数")
    @CopyFieldPoint(fieldName = "unitLayerNum",type= CopyFieldUtil.TYPE_HOUSE)
    private String totalFloorNo;

    /**
     * 房间朝向
     */
    @ApiModelProperty(value = "房间朝向")
    @CopyFieldPoint(fieldName = "houseToward",type= CopyFieldUtil.TYPE_HOUSE)
    private String houseOrientation;

    /**
     * 项目简称
     */
    @ApiModelProperty(value = "项目简称")
    @CopyFieldPoint(fieldName = "projectAbbreviation",type= CopyFieldUtil.TYPE_PROJECT)
    @NotBlank(message = "项目简称[projectShortName:projectAbbreviation]不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String projectShortName;

    /**
     * 运营主体类型
     */
    @ApiModelProperty(value = "运营主体类型")
    @NotBlank(message = "运营主体类型[operateEntityType]不能为空",groups = {UpdateValidatorGroup.class})
    private String operateEntityType;

    /**
     * 运营主体名称
     */
    @ApiModelProperty(value = "运营主体名称")
    @NotBlank(message = "运营主体名称[operateEntityName:affiliatedUnitName]不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    @CopyFieldPoint(fieldName = "affiliatedUnitName",type= CopyFieldUtil.TYPE_PROJECT)
    private String operateEntityName;

    /**
     * 运营单位业务中台编号
     */
    @ApiModelProperty(value = "运营单位业务中台编号")
    @CopyFieldPoint(fieldName = "affiliatedUnit",type= CopyFieldUtil.TYPE_PROJECT)
    @NotBlank(message = "运营单位业务中台编号[operateUnitBusinessNo:affiliatedUnit]不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String operateUnitBusinessNo;

    /**
     * 运营单位编号（NCC）
     */
    @ApiModelProperty(value = "运营单位编号（NCC）")
    @NotBlank(message = "项目简称不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String operateUnitNo;

    /**
     * 运营单位名称
     */
    @ApiModelProperty(value = "运营单位名称")
    @NotBlank(message = "运营单位名称不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
    private String operateUnitName;

    /**
     * 项目区域业务中台编号
     */
    @ApiModelProperty(value = "项目区域业务中台编号")
    @NotBlank(message = "运营主体类型是中心且项目业态是公租房情况下，项目区域业务中台编号不能为空",groups = {InsertValidatorGroup.class})
    @CopyFieldPoint(fieldName = "regionalOffice",type= CopyFieldUtil.TYPE_PROJECT)
    private String projectAreaBusinessNo;

    /**
     * 项目区域编号（NCC）
     */
    @ApiModelProperty(value = "项目区域编号（NCC）")
    @NotBlank(message = "运营主体类型是中心且项目业态是公租房情况下，项目区域编号（NCC）不能为空",groups = {InsertValidatorGroup.class})
    private String projectAreaNo;

    /**
     * 项目区域名称
     */
    @ApiModelProperty(value = "项目区域名称")
    @CopyFieldPoint(fieldName = "regionalOfficeName",type= CopyFieldUtil.TYPE_PROJECT)
    @NotBlank(message = "运营主体类型是中心且项目业态是公租房情况下，项目区域名称不能为空",groups = {InsertValidatorGroup.class})
    private String projectAreaName;

    /**
     * 项目业态
     */
    @ApiModelProperty(value = "项目业态")
    @NotBlank(message = "项目业态不能为空",groups = {InsertValidatorGroup.class,UpdateValidatorGroup.class})
    private String projectFormat;

    /**
     * 房源编号
     */
    @ApiModelProperty(value = "房源编号")
    @CopyFieldPoint(fieldName = "houseCode",type= CopyFieldUtil.TYPE_HOUSE)
    private String houseCode;


    @ApiModelProperty(value = "房屋租赁类型")
    @CopyFieldPoint(fieldName = "leaseType",type= CopyFieldUtil.TYPE_HOUSE)
    private String houseHireType;

    @ApiModelProperty(value = "床名称")
    @CopyFieldPoint(fieldName = "bedNum",type= CopyFieldUtil.TYPE_HOUSE)
    private String bedNo;


    @ApiModelProperty(value = "房号")
    @CopyFieldPoint(fieldName = "houseNum",type= CopyFieldUtil.TYPE_HOUSE)
    private String houseNo;

    /**
     * 历史状态
     */
    @ApiModelProperty(value = "历史状态")
    private String historyState;

    /**
     * 居室名称
     */
    @ApiModelProperty(value = "居室名称")
    private String bedRoom;



    /**
     * 来源节点(配租登记，看房，选房，入住)
     */
    @ApiModelProperty(value = "来源节点(配租登记，看房，选房，入住)")
    private String sourceNode;



    /**
     * 所在小区或楼宇名称
     */
    @ApiModelProperty(value = "所在小区或楼宇名称")
    private String projectEstate;

    @ApiModelProperty(value = "租赁类型：按套，按间，按床")
    @CopyFieldPoint(fieldName = "leaseMode",type= CopyFieldUtil.TYPE_HOUSE)
    private String leaseMode;


    /**
     *定价策率编号
     */
    @ApiModelProperty(value = "定价策率编号")
    private String priceSideRatioNo;

    /**
     *定价策率名称
     */
    @ApiModelProperty(value = "定价策率名称")
    private String priceSideRatioName;

    /**
     *月租金规则编号（工银）
     */
    @ApiModelProperty(value = "月租金规则编号（工银）")
    private String monthlyRentRulesNo;

    /**
     *月租金规则名称（工银）
     */
    @ApiModelProperty(value = "月租金规则名称（工银）")
    private String monthlyRentRulesName;

    /**
     *公祖租金标准
     */
    @ApiModelProperty(value = "公祖租金标准")
    private Double publicRentStandard;

    /**
     *市场租金标准
     */
    @ApiModelProperty(value = "市场租金标准")
    private Double marketRentStandard;

    /**
     *人才租金标准
     */
    @ApiModelProperty(value = "人才租金标准")
    private Double talentRentStandard;

    /**
     *租金单位名称（元/㎡/月、元/月）
     */
    @ApiModelProperty(value = "租金单位名称（元/㎡/月、元/月）")
    private String rentUnit;
}

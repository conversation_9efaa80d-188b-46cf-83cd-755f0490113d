package com.bonc.ioc.bzf.busisigning.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.dao.*;
import com.bonc.ioc.bzf.busisigning.entity.*;
import com.bonc.ioc.bzf.busisigning.enums.*;
import com.bonc.ioc.bzf.busisigning.factory.change.AbstractContractChangeFactory;
import com.bonc.ioc.bzf.busisigning.feign.feign.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.*;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbpmBillManagementPageVo;
import com.bonc.ioc.bzf.busisigning.service.*;
import com.bonc.ioc.bzf.busisigning.utils.*;
import com.bonc.ioc.bzf.busisigning.vo.*;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.FeesSummaryVo;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.*;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.ContractOtherInfo;
import com.bonc.ioc.bzf.busisigning.vo.contractExtend.StandardVo;
import com.bonc.ioc.bzf.busisigning.utils.PaymentCycleBillConverter;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsResultDeductedAmount;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.dict.entity.McpDictEntity;
import com.bonc.ioc.common.dict.session.McpDictSession;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同变更表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by fzq for init
 */
@Slf4j
@Service
public class BbsiContractChangeV2ServiceImpl extends McpBaseServiceImpl<BbsiContractChangeEntity> implements IBbsiContractChangeV2Service {

    @Resource
    private BbsSignInfoMapper signInfoMapper;

    @Resource
    private BbsTemplateSeatMapper templateSeatMapper;
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsiContractChangeMapper baseMapper;

    @Resource
    private BbsSignInfoMapper bbsSignInfoMapper;

    @Resource
    private BbsChangeReturnBankCardMapper bbsChangeReturnBankCardMapper;

    @Resource
    private BbsChangeResultProductMapper bbsChangeResultProductMapper;

    @Resource
    private BbsChangeIncrementalConfigMapper bbsChangeIncrementalConfigMapper;

    @Resource
    private BbsChangePayableDateMapper bbsChangePayableDateMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsiContractChangeV2Service iBbsiContractChangeV2Service;

    @Resource
    private IBbsContractApproveInfoService contractApproveInfoService;

    @Resource
    private IBbsiContractChangeService iBbsiContractChangeService;


    /**
     * 获取登录用户
     */
    @Resource
    private UserUtil userUtil;

    @Resource
    private IContractChangeResultService contractChangeResultService;

    /**
     * 签约相关 服务实例
     */
    @Resource
    private IBbsSignInfoExtService signInfoExtService;

    /**
     * 合同变更模板信息相关 服务实例
     */
    @Resource
    private IBbsContractChangeTemplateService contractChangeTemplateService;

    @Resource
    private BbsContractChangeApproveInfoMapper bbsContractChangeApproveInfoMapper;

    @Resource
    private BbsContractChangeApproveDetailMapper bbsContractChangeApproveDetailMapper;

    @Resource
    private BbsChangeShopInfoMapper bbsChangeShopInfoMapper;

    @Resource
    private BbsChangeSubjectMatterMapper bbsChangeSubjectMatterMapper;

    /**
     * 合同中心 feign实例
     */
    @Resource
    private BbctContractFeignClient contractFeignClient;

    /**
     * 客户中心 feign实例
     */
    @Resource
    private BbCustomerFeignClient customerFeignClient;

    @Resource
    private BbpaymentFeignClient bbpaymentFeignClient;

    @Resource
    private BbctPaymentV2FeignClient bbctPaymentV2FeignClient;

    @Resource
    private BbHousingFeignClient bbHousingFeignClient;

    /**
     * 类型(01变更前,02变更后)
     */
    public static final String CHANGE_INCREMENTAL_CONFIG_NEW = "02";
    public static final String CHANGE_INCREMENTAL_CONFIG_OLD = "01";

    @Resource
    private McpDictSession mcpDictSession;

    @Resource
    private BbsChangeDetermineDeductionPeriodMapper bbsChangeDetermineDeductionPeriodMapper;

    @Value("${verifySameType.isOk}")
    private boolean isOk;

    @Resource
    private BbsChangeCalculatedProductMapper bbsChangeCalculatedProductMapper;

    /**
     * 根据合同编号查询办理人
     *
     * @param contractCode 合同编号
     * @return
     */
    @Override
    public BbsiContractChangeCustomerVo findTransactors(String contractCode) {
        BbctContractManagementVo bbctContractManagementVo = selectContractByIdNo(contractCode);
        //合同中人信息 00个人，01企业
        List<BbctContractSignerVo> userList = bbctContractManagementVo.getUserList();
        if (userList == null || userList.size() == 0) {
            throw new McpException(contractCode + "合同信息中userList为空");
        }
        BbctContractSignerVo signerVo = userList.get(0);

        BbsiContractChangeCustomerVo vo = new BbsiContractChangeCustomerVo();
//        vo.setSignId(bbctContractManagementVo.getSignId());
        vo.setContractCode(contractCode);
        vo.setCustomerType(signerVo.getCustomerType());
        vo.setCustomerNo(signerVo.getCustomerNo());
        vo.setCustomerName(signerVo.getCustomerName());
        vo.setLegalName(signerVo.getLegalName());
        vo.setCustomerIdTypeCode(signerVo.getCustomerIdType());
        if (StringUtils.isNotBlank(signerVo.getCustomerIdType())) {
            McpDictEntity mcpDictEntity = mcpDictSession.getMcpDictUtil().getDictInfoByCode("CERTIFICATE_TYPE", signerVo.getCustomerIdType());
            if (mcpDictEntity != null) {
                vo.setCustomerIdTypeName(mcpDictEntity.getMeaning());
            }
        }
        if("00".equals(signerVo.getCustomerType())){
            vo.setCustomerIdNumber(signerVo.getCustomerIdNumber());
        }else {
            vo.setCustomerIdNumber(signerVo.getCustomerCreditCode());
        }
        vo.setMailAddress(signerVo.getMailAddress());
        vo.setBankName(signerVo.getBankName());
        vo.setBankNameCode(signerVo.getBankNameCode());
        vo.setBankSubbranchName(signerVo.getBankSubbranchName());
        vo.setBankSubbranchCode(signerVo.getBankSubbranchCode());
        vo.setBankPhone(signerVo.getBankPhone());
        vo.setBankCard(signerVo.getBankCard());
        vo.setCustomerTel(signerVo.getCustomerTel());
        vo.setCustomerWorkTel(signerVo.getCustomerWorkTel());

        vo.setProjectId(bbctContractManagementVo.getSubjectMatterList()!=null?bbctContractManagementVo.getSubjectMatterList().get(0).getProjectId():null);
        return vo;
    }

    /**
     * 查询变更信息
     *
     * @param ccId
     * @param contractCode
     * @return
     */
    @Override
    public BbsiContractChangeVo selectChangeInfos(String ccId, String contractCode, String changeTypeItem, String isDetails,String isApprove) {
        BbsiContractChangeVo vo = new BbsiContractChangeVo();
        BbctContractManagementVo bbctContractManagementVo = null;
        BbsContractChangeTemplateVo lesseeChangeContractChangeTemplateVo
                = contractChangeTemplateService.selectByChangeType(ChangeTypeEnum.LESSEE_CHANGE.getCode());
        if (!Objects.isNull(lesseeChangeContractChangeTemplateVo)) {
            vo.setLesseeChangeContractTemplateId(lesseeChangeContractChangeTemplateVo.getTemplateId());
            vo.setLesseeChangeContractTemplateName(lesseeChangeContractChangeTemplateVo.getTemplateName());

            //产品其他信息变更也要 协议项，写死
            vo.setOtherChangeContractTemplateId(lesseeChangeContractChangeTemplateVo.getTemplateId());
            vo.setOtherChangeContractTemplateName(lesseeChangeContractChangeTemplateVo.getTemplateName());
        }
        BbsContractChangeTemplateVo otherChangeContractChangeTemplateVo
                = contractChangeTemplateService.selectByChangeType(ChangeTypeEnum.OTHER_CHANGE.getCode());
        if (!Objects.isNull(otherChangeContractChangeTemplateVo)) {
            vo.setOtherChangeContractTemplateId(otherChangeContractChangeTemplateVo.getTemplateId());
            vo.setOtherChangeContractTemplateName(otherChangeContractChangeTemplateVo.getTemplateName());
        }
        //新增时传合同编号查询，修改时传主键ccId
        if (StringUtils.isBlank(ccId)) {
            vo.setChangeTypeItem(changeTypeItem);
            //新增时
            if (StringUtils.isBlank(contractCode)) {
                throw new McpException("合同编号contractCode为空");
            }
            bbctContractManagementVo = selectContractByIdNo(contractCode);
            vo.setContractCode(contractCode);
            vo.setNewpdfFileIdOldContract(bbctContractManagementVo.getNewpdfFileId());
            //合同中产品信息
            List<BbctContractSubjectMatterVo> subjectMatterList = bbctContractManagementVo.getSubjectMatterList();
            if (subjectMatterList == null || subjectMatterList.size() == 0) {
                throw new McpException(contractCode + "合同信息中subjectMatterList为空");
            }
            //提取 subjectMatterList中productName 并用逗号分割
            String productNames = subjectMatterList.stream().map(BbctContractSubjectMatterVo::getProductName).collect(Collectors.joining(","));
            vo.setProductNameOldContract(productNames);
            vo.setProjectId(subjectMatterList.get(0).getProjectId());
            //签约银行卡信息 就取乙方变更里的银行卡吧

            // 判断合同是否存在物业费 并存储（从老合同扩展字段取）
            ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(bbctContractManagementVo.getContractExtend());
            if (contractOtherInfo != null) {
                //有物业费
                if("02-08".equals(contractOtherInfo.getContractFees())){
                    //有物业且是一口价 先赋值后面重置
//                    vo.setIsProperty("1-1");
                    vo.setIsProperty("1-0");
                    for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
                        //从产品扩展字段解析物业费标准
                        String productExtend = subjectMatterVo.getProductExtend();
                        if(StringUtils.isNotBlank(productExtend)){
                            StandardVo propStandard = JsonToObjectUtil.jsonToProductExtend(productExtend);
                            if(propStandard != null){
                                String standardUnit = propStandard.getStandardUnit();
                                if("1".equals(standardUnit) || "4".equals(standardUnit)){
                                    //不是一口价
                                    vo.setIsProperty("1-0");
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            // 是否为 租金为一口价（单位是元/月或元/年）
            for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
                String rentUnit = subjectMatterVo.getRentUnit();
                if (StringUtils.isNotBlank(rentUnit)) {
                    if (rentUnit.contains("元/月") || rentUnit.contains("元/年")) {
                         vo.setIsOnePrice("1");
                         break;
                    }
                }
            }
        } else {
            //修改时
            BbsiContractChangeEntity entity = iBbsiContractChangeV2Service.selectById(ccId);
            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                vo.setCustomerTypeCode(entity.getCustomerTypeOld());
                //这个字段都显示老的类型
                vo.setCustomerType(entity.getCustomerTypeOld());
                vo.setCustomerTypeOld(entity.getCustomerTypeOld());

                contractCode = entity.getContractCode();

                bbctContractManagementVo = selectContractByIdNo(contractCode);
                vo.setContractCode(contractCode);
                vo.setNewpdfFileIdOldContract(bbctContractManagementVo.getNewpdfFileId());
                List<BbctContractSubjectMatterVo> subjectMatterList = bbctContractManagementVo.getSubjectMatterList();
                if (subjectMatterList != null && subjectMatterList.size() > 0) {
                    //提取 subjectMatterList中productName 并用逗号分割
                    String productNames = subjectMatterList.stream().map(BbctContractSubjectMatterVo::getProductName).collect(Collectors.joining(","));
                    vo.setProductNameOldContract(productNames);
                    vo.setProjectId(subjectMatterList.get(0).getProjectId());
                }

                // 判断合同是否存在物业费 并存储（从老合同扩展字段取）
                if(StringUtils.isBlank(vo.getIsProperty())){
                    ContractOtherInfo contractOtherInfo = JsonToObjectUtil.jsonToContractOtherInfo(bbctContractManagementVo.getContractExtend());
                    if (contractOtherInfo != null) {
                        //有物业费
                        if("02-08".equals(contractOtherInfo.getContractFees())){
                            //有物业且是一口价 先赋值后面重置
//                            vo.setIsProperty("1-1");
                            vo.setIsProperty("1-0");
                            for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
                                //从产品扩展字段解析物业费标准
                                String productExtend = subjectMatterVo.getProductExtend();
                                if(StringUtils.isNotBlank(productExtend)){
                                    StandardVo propStandard = JsonToObjectUtil.jsonToProductExtend(productExtend);
                                    if(propStandard != null){
                                        String standardUnit = propStandard.getStandardUnit();
                                        if("1".equals(standardUnit) || "4".equals(standardUnit)){
                                            //不是一口价
                                            vo.setIsProperty("1-0");
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //  是否为 租金为一口价（单位是元/月或元/年）
                if(StringUtils.isBlank(vo.getIsOnePrice())){
                    for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
                        String rentUnit = subjectMatterVo.getRentUnit();
                        if (StringUtils.isNotBlank(rentUnit)) {
                            if (rentUnit.contains("元/月") || rentUnit.contains("元/年")) {
                                vo.setIsOnePrice("1");
                                break;
                            }
                        }
                    }
                }

                //保证金、租金 银行卡  有银行卡退回就需要增加
                BbsChangeReturnBankCardVo bankCardVo = new BbsChangeReturnBankCardVo();
                bankCardVo.setCcId(ccId);
                List<BbsChangeReturnBankCardVo> bankCardVoList = bbsChangeReturnBankCardMapper.selectBankCards(bankCardVo);
//                    vo.setReturnBankCardVoList(bankCardVoList);
                if (bankCardVoList != null && bankCardVoList.size() > 0) {
                    for (BbsChangeReturnBankCardVo bankCardVoItem : bankCardVoList) {
                        vo.setRentReturnBankCardVo(bankCardVoItem);
                        if (SourceTypeEnum.CASH_RETURN.getCode().equals(bankCardVoItem.getSourceType())) {
                            vo.setCashReturnBankCardVo(bankCardVoItem);
//                            vo.setRentReturnBankCardVo(bankCardVoItem);
                        }
                        if (SourceTypeEnum.RENT_RETURN.getCode().equals(bankCardVoItem.getSourceType())) {
//                            vo.setRentReturnBankCardVo(bankCardVoItem);
                            vo.setReduceReturnBankCardVo(bankCardVoItem);
                        }
                        if (SourceTypeEnum.REDUCE_RETURN.getCode().equals(bankCardVoItem.getSourceType())) {
                            vo.setReduceReturnBankCardVo(bankCardVoItem);
//                            vo.setRentReturnBankCardVo(bankCardVoItem);
                        }
                        if(SourceTypeEnum.CAL_RETURN.getCode().equals(bankCardVoItem.getSourceType())){
//                            vo.setRentReturnBankCardVo(bankCardVoItem);
                        }
                    }
                }

                //租金标准
                List<BbsChangeResultProductVo> productVoList = bbsChangeResultProductMapper.selectByCcId(ccId);
                vo.setProductVoList(productVoList);

                //租金递增   老的租金递增规则要入库一份
                vo.setRentStandardJson(getRentStandardJson(CHANGE_INCREMENTAL_CONFIG_NEW, ccId, bbsChangeIncrementalConfigMapper));
                vo.setRentStandardJsonOld(getRentStandardJson(CHANGE_INCREMENTAL_CONFIG_OLD, ccId, bbsChangeIncrementalConfigMapper));

                //缩租
                List<BbsChangeSubjectMatterVo>  subjectMatterVoOldList = bbsChangeSubjectMatterMapper.selectByCcId(vo.getCcId());
                List<BbsChangeShopInfoVo> bbsChangeShopInfoVoList = bbsChangeShopInfoMapper.selectByCcId(vo.getCcId());
                if (subjectMatterVoOldList != null && subjectMatterVoOldList.size() > 0) {
                    for (BbsChangeSubjectMatterVo changeSubjectMatterVo : subjectMatterVoOldList) {
                        if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                            changeSubjectMatterVo.setTotalArea(changeSubjectMatterVo.getInnerSleeveArea());//套内建筑面积
                        }else{
                            changeSubjectMatterVo.setTotalArea(changeSubjectMatterVo.getHouseStructArea());//建筑面积
                        }
                    }
                }
                vo.setSubjectMatterVoOldList(subjectMatterVoOldList);
                vo.setShopInfoVoList(bbsChangeShopInfoVoList);

                //计租面积
                List<BbsChangeCalculatedProductVo> calculatedProductVoList = bbsChangeCalculatedProductMapper.selectByCcId(vo.getCcId());
                if (calculatedProductVoList != null && calculatedProductVoList.size() > 0) {
                    String areaType = bbctContractManagementVo.getAreaType();
                    String areaTypeName = AreaTypeEnum.BUILD_AREA_TYPE.getDesc();
                    if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(areaType)) {
                        areaTypeName = AreaTypeEnum.INNER_AREA_TYPE.getDesc();
                    }
                    for (BbsChangeCalculatedProductVo calculatedProductVo : calculatedProductVoList) {
                        calculatedProductVo.setAreaType(areaType);
                        calculatedProductVo.setAreaTypeName(areaTypeName);
                    }
                }
                vo.setCalculatedProductVoList(calculatedProductVoList);

                //业态   把业态转前端需要的数据
                String businessFormat = vo.getBusinessFormat();
                if(StringUtils.isNotBlank(businessFormat)){
                    List<DictTreeVo> dictTreeVoList = DictTreeToBusinessFormatUtil.jsonToDictTreeVoList(businessFormat);
                    vo.setDictTreeVoList(dictTreeVoList);
                    //重新set 前端需要的格式
                    vo.setBusinessFormat(JSON.toJSONString(dictTreeVoList));
                }

                //缴费周期变更   处理已保存的JSON数据
                if (isPaymentCycleChangeType(changeTypeItem)) {
                    convertSelectedBillsFromJson(vo);
                    
                    // 转换变更前账单周期JSON为List
                    if (StringUtils.isNotBlank(vo.getBillCycleOld())) {
                        try {
                            // 解析JSON字符串为字符串列表
                            List<String> billCycleList = JSON.parseArray(vo.getBillCycleOld(), String.class);
                            vo.setBillCycleOldList(billCycleList);
                            log.debug("账单周期JSON转换成功，数量: {}", billCycleList != null ? billCycleList.size() : 0);
                        } catch (Exception e) {
                            log.error("账单周期JSON转换失败: billCycleOld={}", vo.getBillCycleOld(), e);
                            // 转换失败时设置为空列表
                            vo.setBillCycleOldList(Collections.emptyList());
                        }
                    }
                }

                //合同变更类型切换时 重新赋值
                if (StringUtils.isNotBlank(changeTypeItem)) {
                    vo.setChangeTypeItem(changeTypeItem);
                }

            }
        }

        //变更前信息赋值 （合同变更类型切换时也需要）
        setBerforeInformationOld(vo, bbctContractManagementVo, isDetails,isApprove);

        //字典转换
        mcpDictSession.getMcpDictTransUtil().transResultCodeToMeaning(vo);

        return vo;
    }

    /**
     * 选择变更后的商铺地址
     *
     * @param ccId
     * @param contractCode
     * @return
     */
    @Override
    public List<BbsChangeShopInfoVo> selectChangedShopAddress(String ccId, String contractCode) {
        BbctContractManagementVo bbctContractManagementVo = selectContractByIdNo(contractCode);
        List<BbsChangeShopInfoVo> list = new ArrayList<>();
        if (StringUtils.isNotBlank(ccId)) {
            //修改时
            //本地变更前合同老房源信息
            List<BbsChangeSubjectMatterVo> subjectMatterVoOldList = bbsChangeSubjectMatterMapper.selectByCcId(ccId);
            //本地变更后合同新房源信息
            List<BbsChangeShopInfoVo> bbsChangeShopInfoVoList = bbsChangeShopInfoMapper.selectByCcId(ccId);
            for (BbsChangeSubjectMatterVo subjectMatterVo : subjectMatterVoOldList) {
                String productNo = subjectMatterVo.getProductNo();
                String rentUnit = subjectMatterVo.getRentUnit();
                String productName = subjectMatterVo.getProductName();
                //查询拆分房源并赋值
                //去查房态是否有拆分的房源 FK_PRODUCT_NO
                RoomBedByHouseCodeVo roomBedByHouseCodeVo = selectRoomBedByHouseCode(productNo);
                //有拆分的房源
                if (roomBedByHouseCodeVo != null) {
                    List<RoomSplitVos> splitVos = roomBedByHouseCodeVo.getSplitVos();
                    for (RoomSplitVos splitVo : splitVos) {
                        BbsChangeShopInfoVo shopInfoVoItem = new BbsChangeShopInfoVo();
                        shopInfoVoItem.setProductNo(splitVo.getSplitCode());
                        shopInfoVoItem.setProductName(splitVo.getAddress());
                        shopInfoVoItem.setArea(StringUtils.isNotBlank(splitVo.getBuildArea()) ? splitVo.getBuildArea() : splitVo.getInnerSleeveArea());
                        shopInfoVoItem.setAreaType(StringUtils.isNotBlank(splitVo.getBuildArea()) ? "1" : "2");
                        shopInfoVoItem.setIsSelected("0");
                        shopInfoVoItem.setProductNoOld(productNo);
                        shopInfoVoItem.setProductNameOld(productName);
                        shopInfoVoItem.setIsSplit("1");

                        shopInfoVoItem.setRentStandard(subjectMatterVo.getRent());
                        // 这里去查老合同的
                        if (StringUtils.isNotBlank(rentUnit)) {
                            String unit = "1";
                            if (rentUnit.contains("元/月")) {
                                unit = "2";
                            } else if (rentUnit.contains("元/年")) {
                                unit = "3";
                            }
                            shopInfoVoItem.setRentStandardUnit(unit);
                        }

                        //从产品扩展字段解析物业费标准
                        String productExtend = subjectMatterVo.getProductExtend();
                        if(StringUtils.isNotBlank(productExtend)){
                            StandardVo propStandard = JsonToObjectUtil.jsonToProductExtend(productExtend);
                            if(propStandard != null){
                                shopInfoVoItem.setPropertyStandard(propStandard.getStandard());
                                shopInfoVoItem.setPropertyStandardUnit(propStandard.getStandardUnit());
                            }
                        }

                        //判断之前是否选中过
                        if (bbsChangeShopInfoVoList != null && bbsChangeShopInfoVoList.size() > 0) {
                            BbsChangeShopInfoVo dbShop = bbsChangeShopInfoVoList.stream().filter(b -> b.getProductNo().equals(productNo)).findFirst().orElse(null);
                            if (dbShop != null) {
                                shopInfoVoItem.setType(dbShop.getType());
                                shopInfoVoItem.setIsSelected("1".equals(dbShop.getType()) ? "1" : "0");
                            }
                        }
                        list.add(shopInfoVoItem);
                    }
                    continue;
                }

                BbsChangeShopInfoVo shopInfoVo = new BbsChangeShopInfoVo();
                shopInfoVo.setProductName(subjectMatterVo.getProductName());
                shopInfoVo.setProductNo(productNo);
                shopInfoVo.setAreaType(bbctContractManagementVo.getAreaType());
                if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                    shopInfoVo.setArea(subjectMatterVo.getInnerSleeveArea());//套内建筑面积
                }else{
                    shopInfoVo.setArea(subjectMatterVo.getHouseStructArea());//建筑面积
                }
//                if (StringUtils.isBlank(bbctContractManagementVo.getTotalArea())) {
//                    shopInfoVo.setArea(subjectMatterVo.getHouseStructArea());
//                } else {
//                    shopInfoVo.setArea(bbctContractManagementVo.getTotalArea());
//                }
                shopInfoVo.setIsSelected("0");
                //判断之前是否选中过
                if (bbsChangeShopInfoVoList != null && bbsChangeShopInfoVoList.size() > 0) {
                    BbsChangeShopInfoVo dbShop = bbsChangeShopInfoVoList.stream().filter(b -> b.getProductNo().equals(productNo)).findFirst().orElse(null);
                    if (dbShop != null) {
                        shopInfoVo.setType(dbShop.getType());
                        shopInfoVo.setIsSelected("1".equals(dbShop.getType()) ? "1" : "0");
                    }
                }
                list.add(shopInfoVo);
            }
        } else {
            //新增时
            //合同中产品信息
            List<BbctContractSubjectMatterVo> subjectMatterList = bbctContractManagementVo.getSubjectMatterList();
            if (subjectMatterList == null || subjectMatterList.size() == 0) {
                throw new McpException(contractCode + "合同信息中subjectMatterList为空");
            }
            for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
                String productNo = subjectMatterVo.getProductNo();
                String rentUnit = subjectMatterVo.getRentUnit();
                String productName = subjectMatterVo.getProductName();
                //查询拆分房源并赋值
                //去查房态是否有拆分的房源 FK_PRODUCT_NO
                RoomBedByHouseCodeVo roomBedByHouseCodeVo = selectRoomBedByHouseCode(productNo);
                //有拆分的房源
                if (roomBedByHouseCodeVo != null) {
                    List<RoomSplitVos> splitVos = roomBedByHouseCodeVo.getSplitVos();
                    for (RoomSplitVos splitVo : splitVos) {
                        BbsChangeShopInfoVo shopInfoVoItem = new BbsChangeShopInfoVo();
                        shopInfoVoItem.setProductNo(splitVo.getSplitCode());
                        shopInfoVoItem.setProductName(splitVo.getAddress());
                        shopInfoVoItem.setArea(StringUtils.isNotBlank(splitVo.getBuildArea()) ? splitVo.getBuildArea() : splitVo.getInnerSleeveArea());
                        shopInfoVoItem.setAreaType(StringUtils.isNotBlank(splitVo.getBuildArea()) ? "1" : "2");
                        shopInfoVoItem.setIsSelected("0");
                        shopInfoVoItem.setProductNoOld(productNo);
                        shopInfoVoItem.setProductNameOld(productName);
                        shopInfoVoItem.setIsSplit("1");

                        shopInfoVoItem.setRentStandard(StringUtils.isNotBlank(subjectMatterVo.getRent()) ? Double.valueOf(subjectMatterVo.getRent()) : null);
                        // 这里去查老合同的
                        if (StringUtils.isNotBlank(rentUnit)) {
                            String unit = "1";
                            if (rentUnit.contains("元/月")) {
                                unit = "2";
                            } else if (rentUnit.contains("元/年")) {
                                unit = "3";
                            }
                            shopInfoVoItem.setRentStandardUnit(unit);
                        }

                        //从产品扩展字段解析物业费标准
                        String productExtend = subjectMatterVo.getProductExtend();
                        if(StringUtils.isNotBlank(productExtend)){
                            StandardVo propStandard = JsonToObjectUtil.jsonToProductExtend(productExtend);
                            if(propStandard != null){
                                shopInfoVoItem.setPropertyStandard(propStandard.getStandard());
                                shopInfoVoItem.setPropertyStandardUnit(propStandard.getStandardUnit());
                            }
                        }

                        list.add(shopInfoVoItem);
                    }
                    continue;
                }

                BbsChangeShopInfoVo shopInfoVo = new BbsChangeShopInfoVo();
                shopInfoVo.setProductName(subjectMatterVo.getProductName());
                shopInfoVo.setProductNo(productNo);
                shopInfoVo.setAreaType(bbctContractManagementVo.getAreaType());
                if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                    shopInfoVo.setArea(subjectMatterVo.getInnerSleeveArea());//套内建筑面积
                }else{
                    shopInfoVo.setArea(subjectMatterVo.getHouseStructArea());//建筑面积
                }
//                if (StringUtils.isBlank(bbctContractManagementVo.getTotalArea())) {
//                    shopInfoVo.setArea(subjectMatterVo.getHouseStructArea());
//                } else {
//                    shopInfoVo.setArea(bbctContractManagementVo.getTotalArea());
//                }
                shopInfoVo.setIsSelected("0");
                list.add(shopInfoVo);
            }
        }
        return list;
    }

    private void setBerforeInformationOld(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo, String isDetails,String isApprove) {
        String changeTypeItem = vo.getChangeTypeItem();
        if (StringUtils.isBlank(changeTypeItem)) {
            throw new McpException("changeTypeItem为空");
        }
        //合同中人信息 00个人，01企业
        List<BbctContractSignerVo> userList = bbctContractManagementVo.getUserList();
        if (userList == null || userList.size() == 0) {
            throw new McpException(bbctContractManagementVo.getContractNo() + "合同信息中userList为空");
        }
        BbctContractSignerVo signerVo = userList.get(0);
        //合同中产品信息
        List<BbctContractSubjectMatterVo> subjectMatterList = bbctContractManagementVo.getSubjectMatterList();
        if (subjectMatterList == null || subjectMatterList.size() == 0) {
            throw new McpException("合同信息中subjectMatterList为空");
        }

        //乙方变更
        if (StringUtils.isBlank(vo.getCustomerNoOld()) && StringUtils.isBlank(vo.getCustomerNameOld())) {
            //1乙方变更  变更前信息 ，也是 "法人/经营者办理" 信息
            setPartyChangeInformation(vo, signerVo);
        }

        //业态变更
        if (changeTypeItem.contains(ContractChangeTypeEnum.BUSINESS_FORMAT_CHANGE.getCode())) {
            if (StringUtils.isBlank(vo.getBusinessFormatOld())) {
                //2业态变更 变更前信息
                setBusinessFormatChangeInformation(vo, bbctContractManagementVo);
            }
        }
        //租赁期变更
        if (changeTypeItem.contains(ContractChangeTypeEnum.LEASE_DATE_CHANGE.getCode())) {
            if (vo.getContractBeginTimeOld() == null && vo.getContractEndTimeOld() == null) {
                //3租赁期变更 变更前信息
                setLeasePeriodChangeInformation(vo, bbctContractManagementVo);
            }
        }
        //保证金变更
        if (changeTypeItem.contains(ContractChangeTypeEnum.BOND_CHANGE.getCode())) {
            if (StringUtils.isBlank(vo.getCashPledgeCodeOld())) {
                //4保证金变更 变更前信息
                setDepositChangeInformation(vo, bbctContractManagementVo);
            }
        }
        //租金变更
        if (changeTypeItem.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())) {
            if (vo.getProductVoList() == null || vo.getProductVoList().size() == 0) {
                //5租金标准 变更前信息
                setRentStandardChangeInformation(vo,bbctContractManagementVo, subjectMatterList);
            }
            if (vo.getRentStandardJsonOld() == null || vo.getRentStandardJsonOld().getIncrementalInfo() == null ||
                    vo.getRentStandardJsonOld().getIncrementalInfo().getIncrementalInfoArray() == null || vo.getRentStandardJsonOld().getIncrementalInfo().getIncrementalInfoArray().size() == 0) {
                //6租金递增 变更前信息
                setRentIncrementChangeInformation(vo, bbctContractManagementVo);
            }
        }

        //应缴费日期变更
        if (changeTypeItem.contains(ContractChangeTypeEnum.PAYABLE_DATE_CHANGE.getCode())) {
            setPayableDateInformation(vo, subjectMatterList, isDetails);
        }

        //缩租面积变更
        if (changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())) {
            if (vo.getSubjectMatterVoOldList() == null || vo.getSubjectMatterVoOldList().size() == 0) {
                setReductionAreaChangeInformation(vo, bbctContractManagementVo, subjectMatterList);
            }
        }

        //计租面积变更
        if (changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())) {
            if (vo.getCalculatedProductVoList() == null || vo.getCalculatedProductVoList().size() == 0) {
                setCalculatedProductChangeInformation(vo, bbctContractManagementVo, subjectMatterList);
            }
        }

        //缴费周期变更
        if (changeTypeItem.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode())) {
            if (StringUtils.isBlank(vo.getPaymentCycleOld())) {
                // 从老合同信息中获取缴费周期信息
                vo.setPaymentCycleOld(bbctContractManagementVo.getPaymentCycleCode());
                // 注意：这里不需要设置paymentCycleOldName，因为VO中已经通过@McpDictPoint注解自动转换
            }
            // 获取变更前账单周期描述（多个账单周期组合）
            if (StringUtils.isBlank(vo.getBillCycleOld())) {
                setBillCycleDescriptionOld(vo, bbctContractManagementVo);
            }
            // 缴费周期变更也需要设置合同开始和结束时间
            if (vo.getContractBeginTimeOld() == null && vo.getContractEndTimeOld() == null) {
                setPaymentCycleContractTimeInformation(vo, bbctContractManagementVo);
            }
        }

        //试算回显  和 应退金额
        if (StringUtils.isNotBlank(vo.getCcId())) {
            if (changeTypeItem.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())
                    || changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                    || changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())
                    || changeTypeItem.contains(ContractChangeTypeEnum.BOND_CHANGE.getCode())) {
                //不是审批时才需要查试算信息   部署试试
                if(!"1".equals(isApprove)){
                    //从工银或数据库缓存中获取试算结果
                    ChangeCalculationBillVo changeCalculationBillVo = iBbsiContractChangeV2Service.getPreviewBillByVo(vo);
                    vo.setChangeCalculationBillVo(changeCalculationBillVo);
                }
                //抵扣账期
                if ("3".equals(vo.getRentRefundPaymentMethod())) {
                    List<BbsChangeDetermineDeductionPeriodVo> periodVoList = bbsChangeDetermineDeductionPeriodMapper.selectByCcId(vo.getCcId(), "1");
                    vo.setPeriodVoList(periodVoList);

                    List<BbsChangeDetermineDeductionPeriodVo> propertyVoList = bbsChangeDetermineDeductionPeriodMapper.selectByCcId(vo.getCcId(), "3");
                    vo.setPropertyVoList(propertyVoList);
                }
            }

            // 这里重新组装（上面getPreviewBillByVo方法会拆分）    应收或应退金额处理  （  租金金额1000,押金金额3000 ）
            if (StringUtils.isNotBlank(vo.getRentAmountReceivable()) || StringUtils.isNotBlank(vo.getRentAmountReceivableLease())) {
                String sb = "";
                if (StringUtils.isNotBlank(vo.getRentAmountReceivableLease()) && !vo.getRentAmountReceivableLease().contains("金额")) {
                    if (changeTypeItem.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())
                            || changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                            || changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())){
                        sb = sb + "租金金额" + vo.getRentAmountReceivableLease()+ ",";
                    }
                }
                if (StringUtils.isNotBlank(vo.getRentAmountReceivable()) && !vo.getRentAmountReceivable().contains("金额")) {
                    sb = sb + "押金金额" + vo.getRentAmountReceivable() ;
                }
                vo.setRentAmountReceivable(sb);
            }
            if (StringUtils.isNotBlank(vo.getReduceAmountReceivable()) || StringUtils.isNotBlank(vo.getReduceAmountReceivableLease())) {
                String sb = "";
                if (StringUtils.isNotBlank(vo.getReduceAmountReceivableLease()) && !vo.getReduceAmountReceivableLease().contains("金额")) {
                    if (changeTypeItem.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())
                            || changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                            || changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())){
                        sb = sb + "租金金额" + vo.getReduceAmountReceivableLease()+ ",";
                    }
                }
                if (StringUtils.isNotBlank(vo.getReduceAmountReceivable()) && !vo.getReduceAmountReceivable().contains("金额")) {
                    sb = sb + "押金金额" + vo.getReduceAmountReceivable() ;
                }
                vo.setReduceAmountReceivable(sb);
            }
            if (StringUtils.isNotBlank(vo.getCashAmountReceivable())) {
                String sb = "";
                if (StringUtils.isNotBlank(vo.getCashAmountReceivable()) && !vo.getCashAmountReceivable().contains("金额")) {
                    sb = sb + "押金金额" + vo.getCashAmountReceivable() ;
                }
                vo.setCashAmountReceivable(sb);
            }
            if (StringUtils.isNotBlank(vo.getPropertyAmountReceivable())) {
                String sb = "";
                if (StringUtils.isNotBlank(vo.getPropertyAmountReceivable()) && !vo.getPropertyAmountReceivable().contains("物业费")) {
                    sb = sb + "物业费" + vo.getPropertyAmountReceivable() ;
                }
                vo.setPropertyAmountReceivable(sb);
            }
        }
    }

    private Boolean checkAppReply(AppReply appReply) {
        if (appReply == null || !AppReply.SUCCESS_CODE.equals(appReply.getCode()) || appReply.getData() == null) {
            return false;
        }
        return true;
    }

    private void setCalculatedProductChangeInformation(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo, List<BbctContractSubjectMatterVo> subjectMatterList) {
        List<String> productNoList = subjectMatterList.stream().map(BbctContractSubjectMatterVo::getProductNo).collect(Collectors.toList());
        //查房态临时表面积
        List<LastedAreaVo> lastedAreaVoList =  selectLastedAreaByCodes(productNoList);
        if(lastedAreaVoList == null || lastedAreaVoList.size() == 0){
            return;
        }
        List<BbsChangeCalculatedProductVo> calculatedProductVoList = new ArrayList<>();
        //和房态查面积对比
        for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
            String productNo = subjectMatterVo.getProductNo();
            String houseStructArea = subjectMatterVo.getHouseStructArea();
            String areaType = bbctContractManagementVo.getAreaType();
            String areaTypeName = AreaTypeEnum.BUILD_AREA_TYPE.getDesc();
            //根据面积类型判断是否重新赋值
            if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                houseStructArea = subjectMatterVo.getInnerSleeveArea();//套内建筑面积
                areaTypeName = AreaTypeEnum.INNER_AREA_TYPE.getDesc();
            }
            String rentUnit = subjectMatterVo.getRentUnit();
            String unit = "1";
            if (StringUtils.isNotBlank(rentUnit)) {
                if (rentUnit.contains("元/月")) {
                    unit = "2";
                } else if (rentUnit.contains("元/年")) {
                    unit = "3";
                }
            }
            //java8用productNo 和 lastedAreaVoList中houseCode对比找到一个记录
            LastedAreaVo lastedAreaVo = lastedAreaVoList.stream().filter(areaVo -> areaVo.getHouseCode().equals(productNo)).findFirst().orElse(null);
            if(lastedAreaVo == null){
                continue;
            }
            //房态建筑面积
            String buildArea = lastedAreaVo.getBuildArea();
            //根据面积类型判断是否重新赋值
            if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                buildArea = lastedAreaVo.getInnerSleeveArea();
            }

            //合同里的面积 和 房态不一致
            if(!(
                    (StringUtils.isBlank(houseStructArea) && StringUtils.isNotBlank(buildArea))
                    || (StringUtils.isNotBlank(houseStructArea) && StringUtils.isBlank(buildArea))
                    || (new BigDecimal(houseStructArea).compareTo(new BigDecimal(buildArea)) == 0)
                )){
                BbsChangeCalculatedProductVo calculatedProductVo = new BbsChangeCalculatedProductVo();
                calculatedProductVo.setProductName(subjectMatterVo.getProductName());
                calculatedProductVo.setProductNo(productNo);
                calculatedProductVo.setHouseStructAreaOld(houseStructArea);
                calculatedProductVo.setHouseStructArea(buildArea);
                calculatedProductVo.setRentStandardUnit(unit);
                calculatedProductVo.setRentStandardUnitName(rentUnit);
                calculatedProductVo.setRentStandard(subjectMatterVo.getRent() != null ? Double.valueOf(subjectMatterVo.getRent()) : 0);
                calculatedProductVo.setAreaType(areaType);
                calculatedProductVo.setAreaTypeName(areaTypeName);
                calculatedProductVoList.add(calculatedProductVo);
            }

        }
        vo.setCalculatedProductVoList(calculatedProductVoList);
    }

    private void setReductionAreaChangeInformation(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo, List<BbctContractSubjectMatterVo> subjectMatterList) {
        List<BbsChangeSubjectMatterVo> subjectMatterVoOldList = new ArrayList<>();
        for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
            BbsChangeSubjectMatterVo bbsChangeSubjectMatterVo = new BbsChangeSubjectMatterVo();
            BeanUtils.copyProperties(subjectMatterVo, bbsChangeSubjectMatterVo);
            //赋值关联
            bbsChangeSubjectMatterVo.setCcId(vo.getCcId());
            bbsChangeSubjectMatterVo.setAreaType(bbctContractManagementVo.getAreaType());
            if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                bbsChangeSubjectMatterVo.setTotalArea(subjectMatterVo.getInnerSleeveArea());//套内建筑面积
            }else{
                bbsChangeSubjectMatterVo.setTotalArea(subjectMatterVo.getHouseStructArea());//建筑面积
            }
//                if (StringUtils.isBlank(bbctContractManagementVo.getTotalArea())) {
//                    bbsChangeSubjectMatterVo.setTotalArea(subjectMatterVo.getHouseStructArea());
//                } else {
//                    bbsChangeSubjectMatterVo.setTotalArea(bbctContractManagementVo.getTotalArea());
//                }
            subjectMatterVoOldList.add(bbsChangeSubjectMatterVo);
        }
//        if(subjectMatterVoOldList.size()>0){
//            //    回显的时候应该去查shop表，新增前端自己从选择的商铺里判断
//            for(BbsChangeSubjectMatterVo subjectMatterVo : subjectMatterVoOldList){
//                //去查房态是否有拆分的房源 FK_PRODUCT_NO
//                RoomBedByHouseCodeVo roomBedByHouseCodeVo = selectRoomBedByHouseCode(subjectMatterVo.getProductNo());
//                //有拆分的房源
//                if(roomBedByHouseCodeVo != null) {
//                    List<RoomSplitVos> splitVos = roomBedByHouseCodeVo.getSplitVos();
//                    if(splitVos != null && splitVos.size() > 0){
//                        vo.setIsSplit("1");
//                        break;
//                    }
//                }
//            }
//        }
        vo.setSubjectMatterVoOldList(subjectMatterVoOldList);
    }

    private void setPayableDateInformation(BbsiContractChangeVo vo, List<BbctContractSubjectMatterVo> subjectMatterList, String isDetails) {
        //按照合同编号查工银账单
        BbpmBillManagementVo billManagementVo = new BbpmBillManagementVo();

        billManagementVo.setProjectId(subjectMatterList.get(0).getProjectId());
        billManagementVo.setContractCode(vo.getContractCode());
        if (!"1".equals(isDetails)) {
            //新增和修改的时候
            billManagementVo.setBillStatus("02,03");
            billManagementVo.setStatus("01");
        }
        //01企业，02个人
        billManagementVo.setChargeOwner("02");

        //先查个人再查企业
        AppReply<List<BbpmBillManagementVo>> listAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
        if (!checkAppReply(listAppReply) || listAppReply.getData().size() == 0) {
            billManagementVo.setChargeOwner("01");
            listAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
        } else {
            //后加
            billManagementVo.setChargeOwner("01");
            AppReply<List<BbpmBillManagementVo>> compAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
            if (checkAppReply(compAppReply) && compAppReply.getData().size() > 0) {
                //把compAppReply追加到listAppReply中
                listAppReply.getData().addAll(compAppReply.getData());
            }
        }

        if (!checkAppReply(listAppReply) || listAppReply.getData().size() == 0) {
            return;
        }

        //先查表bbs_change_payable_date
        List<BbsChangePayableDateVo> dbPayableDateVoList = null;
        if (StringUtils.isNotBlank(vo.getCcId())) {
            dbPayableDateVoList = bbsChangePayableDateMapper.selectByCcId(vo.getCcId());
            vo.setBillPayableDateVoList(dbPayableDateVoList);
        }

        List<BillHouseNameGroupVo> billHouseNameGroupVoList = new ArrayList<>();
        List<BbpmBillManagementVo> gyBillList = listAppReply.getData();
        //查看详情时
        if ("1".equals(isDetails)) {
            if (dbPayableDateVoList == null || dbPayableDateVoList.size() == 0) {
                return;
            }
            //本地账单数据按 房屋分组
            Map<String, List<BbsChangePayableDateVo>> payableDateByHouseNameMap = dbPayableDateVoList.stream()
                    .collect(Collectors.groupingBy(BbsChangePayableDateVo::getHouseName));
            for (Map.Entry<String, List<BbsChangePayableDateVo>> entry : payableDateByHouseNameMap.entrySet()) {
                String houseName = entry.getKey();
                List<BbsChangePayableDateVo> payableDateVoList = entry.getValue();
                BillHouseNameGroupVo billHouseNameGroupVo = new BillHouseNameGroupVo();
                List<BbpmBillManagementVo> groupBillList = new ArrayList<>();
                billHouseNameGroupVo.setHouseName(houseName);
                //先循环本地库数据，按照billId找工银返回
                for (BbsChangePayableDateVo payableDate : payableDateVoList) {
                    String billId = payableDate.getBillId();
                    BbpmBillManagementVo bill = gyBillList.stream().filter(b -> b.getBillId().equals(billId)).findFirst().orElse(null);
                    if (bill != null) {
                        bill.setPayableDate(payableDate.getPayableDate());
                        bill.setPayableDateOld(payableDate.getPayableDateOld());
                    }
                    groupBillList.add(bill);
                }
                billHouseNameGroupVo.setBillManagementVoList(groupBillList);
                billHouseNameGroupVoList.add(billHouseNameGroupVo);
            }
        } else {
            //工银账单数据按 房屋分组
            Map<String, List<BbpmBillManagementVo>> billByHouseNameMap = gyBillList.stream()
                    .collect(Collectors.groupingBy(BbpmBillManagementVo::getHouseName));
            //先循环工银返回数据，按照billId找本地库数据
            for (Map.Entry<String, List<BbpmBillManagementVo>> entry : billByHouseNameMap.entrySet()) {
                String houseName = entry.getKey();
                List<BbpmBillManagementVo> billList = entry.getValue();
                BillHouseNameGroupVo billHouseNameGroupVo = new BillHouseNameGroupVo();
                List<BbpmBillManagementVo> groupBillList = new ArrayList<>();
                billHouseNameGroupVo.setHouseName(houseName);
                for (BbpmBillManagementVo bill : billList) {
                    String payableDate = bill.getPayableDate();
                    String billId = bill.getBillId();
                    //把账单上的日期赋值到 变更前的字段
                    bill.setPayableDateOld(payableDate);
//                    //变更后的设置为空  变更后的也要显示内容不用设置为空fk
//                    bill.setPayableDate(null);
                    if (dbPayableDateVoList != null && dbPayableDateVoList.size() > 0) {
                        //在dbPayableDateVoList中找到billId一样的数据
                        BbsChangePayableDateVo dbPayableDateVo = dbPayableDateVoList.stream().filter(d -> d.getBillId().equals(billId)).findFirst().orElse(null);
                        if (dbPayableDateVo != null) {
                            //本地表里变更后的日期赋值到bill上
                            bill.setPayableDate(dbPayableDateVo.getPayableDate());
                        }
                    }
                    groupBillList.add(bill);
                }
                billHouseNameGroupVo.setBillManagementVoList(groupBillList);
                billHouseNameGroupVoList.add(billHouseNameGroupVo);
            }
        }
        vo.setBillHouseNameGroupVoList(billHouseNameGroupVoList);
    }

    @Override
    public List<BillHouseNameGroupVo> payableDateInformations(String ccId) {
        BbsiContractChangeVo vo = new BbsiContractChangeVo();
        BbsiContractChangeEntity entity = iBbsiContractChangeV2Service.selectById(ccId);
        BeanUtils.copyProperties(entity, vo);

        BbctContractManagementVo bbctContractManagementVo = selectContractByIdNo(vo.getContractCode());
        //合同中产品信息
        List<BbctContractSubjectMatterVo> subjectMatterList = bbctContractManagementVo.getSubjectMatterList();
        if (subjectMatterList == null || subjectMatterList.size() == 0) {
            return null;
        }

        //按照合同编号查工银账单
        BbpmBillManagementVo billManagementVo = new BbpmBillManagementVo();
        billManagementVo.setProjectId(subjectMatterList.get(0).getProjectId());
        billManagementVo.setContractCode(vo.getContractCode());
        //01企业，02个人
        billManagementVo.setChargeOwner("02");
        //先查个人再查企业
        AppReply<List<BbpmBillManagementVo>> listAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
        if (!checkAppReply(listAppReply) || listAppReply.getData().size() == 0) {
            billManagementVo.setChargeOwner("01");
            listAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
        } else {
            //后加
            billManagementVo.setChargeOwner("01");
            AppReply<List<BbpmBillManagementVo>> compAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
            if (checkAppReply(compAppReply) && compAppReply.getData().size() > 0) {
                //把compAppReply追加到listAppReply中
                listAppReply.getData().addAll(compAppReply.getData());
            }
        }
        if (!checkAppReply(listAppReply) || listAppReply.getData().size() == 0) {
            return null;
        }
        //先查表bbs_change_payable_date
        List<BbsChangePayableDateVo> dbPayableDateVoList = bbsChangePayableDateMapper.selectByCcId(vo.getCcId());
        List<BillHouseNameGroupVo> billHouseNameGroupVoList = new ArrayList<>();
        List<BbpmBillManagementVo> gyBillList = listAppReply.getData();
        //查看详情时
        if (dbPayableDateVoList == null || dbPayableDateVoList.size() == 0) {
            return null;
        }
        //本地账单数据按 房屋分组
        Map<String, List<BbsChangePayableDateVo>> payableDateByHouseNameMap = dbPayableDateVoList.stream()
                .collect(Collectors.groupingBy(BbsChangePayableDateVo::getHouseName));
        for (Map.Entry<String, List<BbsChangePayableDateVo>> entry : payableDateByHouseNameMap.entrySet()) {
            String houseName = entry.getKey();
            List<BbsChangePayableDateVo> payableDateVoList = entry.getValue();
            BillHouseNameGroupVo billHouseNameGroupVo = new BillHouseNameGroupVo();
            List<BbpmBillManagementVo> groupBillList = new ArrayList<>();
            billHouseNameGroupVo.setHouseName(houseName);
            //先循环本地库数据，按照billId找工银返回
            for (BbsChangePayableDateVo payableDate : payableDateVoList) {
                String billId = payableDate.getBillId();
                BbpmBillManagementVo bill = gyBillList.stream().filter(b -> b.getBillId().equals(billId)).findFirst().orElse(null);
                if (bill != null) {
                    bill.setPayableDate(payableDate.getPayableDate());
                    bill.setPayableDateOld(payableDate.getPayableDateOld());
                }
                groupBillList.add(bill);
            }
            billHouseNameGroupVo.setBillManagementVoList(groupBillList);
            billHouseNameGroupVoList.add(billHouseNameGroupVo);
        }
        return billHouseNameGroupVoList;
    }

    /**
     * 试算账单预览
     *
     * @param vo
     * @return
     */
    @Override
    public ChangeCalculationBillVo getPreviewBillByVo(BbsiContractChangeVo vo) {

        if (vo == null) {
            return null;
        }

        if (StringUtils.isNotBlank(vo.getRentEffectiveExecutionDate())
                || StringUtils.isNotBlank(vo.getReduceEffectiveExecutionDate())
                || StringUtils.isNotBlank(vo.getCashEffectiveExecutionDate())) {

        } else {
            log.info("第一步暂存不用调用试算,直接返回");
            return null;
        }

        String changeTypeItem = vo.getChangeTypeItem();
        if(StringUtils.isBlank(changeTypeItem)){
            throw new McpException("changeTypeItem为空");
        }
        ChangeCalculationBillVo resultVo = new ChangeCalculationBillVo();
        log.info(vo.getContractCode() + "前端传入试算参数:" + JSONObject.toJSONString(vo));


        // 判断是否为缴费周期变更或免租期变更
        boolean isPaymentCycleChange = changeTypeItem.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode());
        boolean isFreeRentPeriodChange = changeTypeItem.contains(ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode());

        if (isPaymentCycleChange || isFreeRentPeriodChange) {
            //TODO 改造1
            //获取工银或本地试算结果
            BbsChangePreviewBillResultVo bills = AbstractContractChangeFactory.getInstance(vo).getPreviewBills();
            log.info(vo.getContractCode() + "工银试算结果:" + JSONObject.toJSONString(bills));
            resultVo.setPreviewBillResultVo(bills);
            // 构建费用汇总列表

            return resultVo;
        } else {
             //获取试算结果前处理一下字段
            //应收或应退金额处理 这个字段按逗号分割（  租金金额1000,押金金额3000 ）
            if(StringUtils.isNotBlank(vo.getRentAmountReceivable()) && vo.getRentAmountReceivable().contains("金额")){
                String[] split1 = vo.getRentAmountReceivable().split(",");
                for (String s : split1) {
                    if(s.contains("押金金额")){
                        vo.setRentAmountReceivable(s.split("押金金额")[1]);
                        vo.setReduceAmountReceivable(s.split("押金金额")[1]);
                        vo.setCashAmountReceivable(s.split("押金金额")[1]);
                    }else if(s.contains("租金金额")){
                        vo.setRentAmountReceivableLease(s.split("租金金额")[1]);
                        vo.setReduceAmountReceivableLease(s.split("租金金额")[1]);
                    }else if(s.contains("物业费")){
                        vo.setPropertyAmountReceivable(s.split("物业费")[1]);
                    }
                }
            }
            if(StringUtils.isNotBlank(vo.getPropertyAmountReceivable()) && vo.getPropertyAmountReceivable().contains("物业费")){
                String[] split1 = vo.getPropertyAmountReceivable().split(",");
                for (String s : split1) {
                    if(s.contains("物业费")){
                        vo.setPropertyAmountReceivable(s.split("物业费")[1]);
                    }
                }
            }
            // 生效执行日期
            if (vo != null && StringUtils.isNotBlank(vo.getRentEffectiveExecutionDate())) {
                vo.setReduceEffectiveExecutionDate(vo.getRentEffectiveExecutionDate());
                vo.setCashEffectiveExecutionDate(vo.getRentEffectiveExecutionDate());
            }

            // 退/缴方式
            if (StringUtils.isNotBlank(vo.getRentRefundPaymentMethod())) {
                vo.setReduceRefundPaymentMethod(vo.getRentRefundPaymentMethod());
                vo.setCashRefundPaymentMethod(vo.getRentRefundPaymentMethod());
            }
        
            //获取工银或本地试算结果
            BbsChangePreviewBillResultVo bills = AbstractContractChangeFactory.getInstance(vo).getPreviewBills();
            log.info(vo.getContractCode() + "工银试算结果:" + JSONObject.toJSONString(bills));
            resultVo.setPreviewBillResultVo(bills);
            if (bills != null && bills.getDeductedAmountList() != null && bills.getDeductedAmountList().size() > 0) {
                List<PreviewBillsResultDeductedAmount> deductedAmountList = bills.getDeductedAmountList();
                //把deductedAmountList按照chargeSubjectNo排序
                deductedAmountList.sort(Comparator.comparing(PreviewBillsResultDeductedAmount::getChargeSubjectNo));

                StringBuffer amountReceivable = new StringBuffer();
                StringBuffer propertyAmountReceivable = new StringBuffer();
                BigDecimal amountReceivableAmount = BigDecimal.ZERO;

                //可抵扣金额累加  租金和押金一起算，物业费单算
                BigDecimal rentDeductionSum = BigDecimal.ZERO;
                BigDecimal propertyDeductionSum = BigDecimal.ZERO;

                boolean flag = false;
                resultVo.setRefundPaymentMethod(RefundMethodEnum.SUPPLEMENTARY_PAYMENT.getCode());
                resultVo.setIsSelectRentDeduction(WhetherEnum.NO.getCode());
                for (PreviewBillsResultDeductedAmount amount : deductedAmountList) {
                    if (StringUtils.isNotBlank(amount.getRentRefundPaymentMethod())) {
                        //审批通过后查看的时候，从本地数据库取出的数据
                        resultVo.setRefundPaymentMethod(amount.getRentRefundPaymentMethod());
                    } else {
                        BigDecimal deductedAmount = amount.getDeductedAmount()==null?BigDecimal.ZERO:amount.getDeductedAmount();
                        if (deductedAmount.compareTo(BigDecimal.ZERO) >= 0) {
                            // 只有正数时进行退/抵扣
                            flag = true;
                        }
                    }
                    if ("01".equals(amount.getChargeSubjectNo())) {
                        if (changeTypeItem.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())
                                || changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())
                                || changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())){
                            amountReceivable.append("租金金额").append(amount.getDeductedAmount()==null? BigDecimal.ZERO:amount.getDeductedAmount().setScale(2, BigDecimal.ROUND_HALF_UP)).append(",");
                        }
                        rentDeductionSum = rentDeductionSum.add(amount.getDeductedAmount()==null?BigDecimal.ZERO:amount.getDeductedAmount());
                    } else if ("02".equals(amount.getChargeSubjectNo()))  {
                        amountReceivable.append("押金金额").append(amount.getDeductedAmount()==null? BigDecimal.ZERO:amount.getDeductedAmount().setScale(2, BigDecimal.ROUND_HALF_UP)).append(",");
                        rentDeductionSum = rentDeductionSum.add(amount.getDeductedAmount()==null?BigDecimal.ZERO:amount.getDeductedAmount());
                    }else if ("07".equals(amount.getChargeSubjectNo())) {
                        propertyAmountReceivable.append("物业费").append(amount.getDeductedAmount()==null? BigDecimal.ZERO:amount.getDeductedAmount().setScale(2, BigDecimal.ROUND_HALF_UP)).append(",");
                        propertyDeductionSum = propertyDeductionSum.add(amount.getDeductedAmount()==null?BigDecimal.ZERO:amount.getDeductedAmount());
                    }
                }
                resultVo.setAmountReceivable((amountReceivable==null || amountReceivable.length()==0) ? null : amountReceivable.substring(0, amountReceivable.length() - 1).toString());
                resultVo.setPropertyAmountReceivable((propertyAmountReceivable==null || propertyAmountReceivable.length()==0) ? null : propertyAmountReceivable.substring(0, propertyAmountReceivable.length() - 1).toString());
                if (flag) {
                    resultVo.setRefundPaymentMethod(RefundMethodEnum.RETURN_BANK_CARD.getCode());
                    resultVo.setIsSelectRentDeduction(WhetherEnum.YES.getCode());
                }
                if (propertyDeductionSum.compareTo(BigDecimal.ZERO) <= 0) {
                    resultVo.setIsSelectPropertyDeduction(WhetherEnum.NO.getCode());
                }else{
                    resultVo.setIsSelectPropertyDeduction(WhetherEnum.YES.getCode());
                }
            } else {
                StringBuffer amountReceivable = new StringBuffer();
                amountReceivable.append("租金金额0").append(",");
                amountReceivable.append("押金金额0");
                resultVo.setAmountReceivable(amountReceivable.toString());
                if("1-0".equals(vo.getIsProperty())){
                    resultVo.setPropertyAmountReceivable("物业费0");
                }
                resultVo.setRefundPaymentMethod("2");

                resultVo.setIsSelectRentDeduction(WhetherEnum.NO.getCode());
                resultVo.setIsSelectPropertyDeduction(WhetherEnum.NO.getCode());
            }

            // 押金金额累加
            if (bills != null && bills.getCashPledgeList() != null && bills.getCashPledgeList().size() > 0) {
                List<BbsChangePreviewBillVo> cashPledgeList = bills.getCashPledgeList();
                BigDecimal cashPledgeAmount = cashPledgeList.stream().map(item -> new BigDecimal(StringUtils.isNotBlank(item.getPayableMoney()) ? item.getPayableMoney() : "0")).reduce(BigDecimal.ZERO, BigDecimal::add);
                resultVo.setCashPledgeValue(cashPledgeAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            }
            // 租金+押金的未缴和部分缴纳账单，过滤掉"押金"费项的账单
            List<BbsChangePreviewBillVo> deductibleBillList = bills.getDeductibleBillList();
            if (CollectionUtils.isNotEmpty(deductibleBillList)) {
                List<BbsChangePreviewBillVo> resultList = new ArrayList<>();
                for (BbsChangePreviewBillVo deductibleBill : deductibleBillList) {
                    if ("01".equals(deductibleBill.getChargeSubjectNo())) {
                        resultList.add(deductibleBill);
                    }
                }
                bills.setDeductibleBillList(resultList);
            }

            // 构建费用汇总列表
            resultVo.setFeesSummaryList(buildFeesSummaryList(bills, changeTypeItem, resultVo.getRefundPaymentMethod(), vo.getCcId()));

            return resultVo;
        }
        
    }


    /**
     * 抵扣账单-点击确定
     *        功能点：选择抵扣账期
     *         功能点描述：支持账期多选
     *         1、缩租面积变更后的试算账单
     *         2、取账单缴费状态为"未缴"和"已缴部分支付"的数据
     *         3、支持跳账单选择
     *         4、选中的账单全部抵满应缴金额，例如：可抵扣总额4000元，勾选"账期1"的应缴金额3000元，默认抵扣全部应缴金额3000元。
     *         剩余可抵扣总额1000元，勾选"账期2"，抵扣应缴金额1000元。
     *         5、情形1:勾选账单未抵满，有剩余可抵扣金额，点击弹框提交时，提示：未全部抵扣不可提交
     *         情形2:未勾选账单，点击弹框提交时，提示：未全部抵扣不可提交
     *         6、勾选账单的应缴金额超出可抵扣总额，提示：可抵扣金额不足，不可提交
     *         7、选择并提交后，回显已选数据到"抵扣账期"表
     * @param vo
     * @return
     */
    @Override
    public List<BbsChangeDetermineDeductionPeriodVo> deductionBillDetermination(ChangeCalculationBillVo vo) {
        if (vo == null || vo.getPreviewBillResultVo() == null) {
            throw new McpException("无试算信息");
        }
        //工银可能这个节点不返回
        if(vo.getPreviewBillResultVo().getDeductedAmountList() == null || vo.getPreviewBillResultVo().getDeductedAmountList().size() == 0){
            throw new McpException("可抵扣金额列表为空,无需抵扣");
        }

        //可抵扣账单 租金、押金
        List<BbsChangePreviewBillVo> deductibleBillList = vo.getPreviewBillResultVo().getDeductibleBillList();
        //物业费的
        if("4".equals(vo.getType())){
            deductibleBillList = vo.getPreviewBillResultVo().getDeductiblePropertyBillList();
        }

        if(deductibleBillList == null || deductibleBillList.size() == 0){
            if("4".equals(vo.getType())){
                throw new McpException("无可抵扣物业费账单列表deductibleBillList");
            }else{
                throw new McpException("无可抵扣账单列表deductibleBillList");
            }
        }
        // 校验账单次号(只有多房源才校验)
        if (CollectionUtils.isNotEmpty(vo.getPreviewBillResultVo().getAllDeductibleBillList()) &&
                checkMultipleRoom(vo.getPreviewBillResultVo().getAllDeductibleBillList())) {
            checkBatchOrder(vo.getPreviewBillResultVo().getAllDeductibleBillList(), deductibleBillList);
        }
        //按照 chargePeriod缴费次数 正序排列并砍掉chargeStatusStr=已缴足额支付的数据
        deductibleBillList = deductibleBillList.stream().filter(b -> !"已缴足额支付".equals(b.getChargeStatusStr())).collect(Collectors.toList());
        if(deductibleBillList.size()==0){
            throw new McpException("无可抵扣账单列表deductibleBillList");
        }
        deductibleBillList = deductibleBillList.stream().sorted(Comparator.comparing(BbsChangePreviewBillVo::getChargePeriod)).collect(Collectors.toList());
        //抵扣账单上的 toBePaidMoney待缴金额转BigDecimal,并累加
        BigDecimal toBePaidMoneySum = deductibleBillList.stream()
                .map(b -> StringUtils.isBlank(b.getToBePaidMoney()) ? BigDecimal.ZERO : new BigDecimal(b.getToBePaidMoney()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //抵扣金额列表 租金、押金、物业费
        List<PreviewBillsResultDeductedAmount> deductedAmountList = vo.getPreviewBillResultVo().getDeductedAmountList();
        //可抵扣金额累加  租金和押金一起算，物业费单算
        BigDecimal amountListSum = BigDecimal.ZERO;
        for(PreviewBillsResultDeductedAmount amount : deductedAmountList){
            if("4".equals(vo.getType())){
                if("07".equals(amount.getChargeSubjectNo())){
                    amountListSum = amountListSum.add(amount.getDeductedAmount()==null?BigDecimal.ZERO:amount.getDeductedAmount());
                }
            }else{
                if("01".equals(amount.getChargeSubjectNo()) || "02".equals(amount.getChargeSubjectNo())){
                    if (!Objects.isNull(amount.getDeductedAmount()) &&
                            amount.getDeductedAmount().compareTo(BigDecimal.ZERO) >= 0) {
                        // 应退合计金额规则: 只取正数
                        amountListSum = amountListSum.add(amount.getDeductedAmount());
                    }
                }
            }
        }
//        amountListSum = deductedAmountList.stream()
//                .map(PreviewBillsResultDeductedAmount::getDeductedAmount)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //如果amountListSum小于等于0
        if (amountListSum.compareTo(BigDecimal.ZERO) <= 0) {
            throw new McpException("可抵扣金额为0,无需抵扣");
        }

        // 计算全部可抵扣账单的待缴金额总和，判断是否支持盈余退款
        List<BbsChangePreviewBillVo> allDeductibleBillList;
        if("4".equals(vo.getType())){
            // 物业费抵扣：使用全部可抵扣物业费账单
            allDeductibleBillList = vo.getPreviewBillResultVo().getAllDeductiblePropertyBillList();
        } else {
            // 租金押金抵扣：使用全部可抵扣账单（租金、押金）
            allDeductibleBillList = vo.getPreviewBillResultVo().getAllDeductibleBillList();
        }
        
        // 计算全部可抵扣账单的待缴金额总和
        BigDecimal allToBePaidMoneySum = BigDecimal.ZERO;
        boolean hasAllDeductibleBillData = false;
        
        if (allDeductibleBillList != null && allDeductibleBillList.size() > 0) {
            //TODO  hasAllDeductibleBillData = true; 这期盈余退款功能工银不支持，先不做。
            allToBePaidMoneySum = allDeductibleBillList.stream()
                    .filter(b -> !"已缴足额支付".equals(b.getChargeStatusStr())) // 过滤掉已缴足额支付的账单
                    .map(b -> StringUtils.isBlank(b.getToBePaidMoney()) ? BigDecimal.ZERO : new BigDecimal(b.getToBePaidMoney()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        
        log.info("可抵扣金额总和: {}, 当前选择账单待缴金额总和: {}, 全部可抵扣账单待缴金额总和: {}, 是否有全部账单数据: {}", 
                amountListSum, toBePaidMoneySum, allToBePaidMoneySum, hasAllDeductibleBillData);
        
        // 判断是否支持盈余退款：需要有全部账单数据且可抵扣金额总和超过全部可抵扣账单待缴金额总和
        boolean supportSurplusRefund = hasAllDeductibleBillData && amountListSum.compareTo(allToBePaidMoneySum) > 0;
        
        if (!supportSurplusRefund) {
            // 不支持盈余退款时，保持原来的验证逻辑
            if (toBePaidMoneySum.compareTo(amountListSum) < 0) {
                log.error("未全部抵扣不可提交toBePaidMoneySum:{},amountListSum:{}", toBePaidMoneySum, amountListSum);
                throw new McpException("未全部抵扣不可提交");
            }
        }
        
        List<BbsChangeDetermineDeductionPeriodVo> periodVoList = new ArrayList<>();
        BigDecimal originalAmountListSum = amountListSum; // 保存原始可抵扣金额用于计算盈余
        for (BbsChangePreviewBillVo b : deductibleBillList) {
            BbsChangeDetermineDeductionPeriodVo periodVo = new BbsChangeDetermineDeductionPeriodVo();
            BeanUtils.copyProperties(b, periodVo);
            //特殊处理
            periodVo.setChargePeriod(String.valueOf(b.getChargePeriod()));

            BigDecimal toBePaidMoney = StringUtils.isBlank(periodVo.getToBePaidMoney()) ? BigDecimal.ZERO : new BigDecimal(periodVo.getToBePaidMoney());
            //可抵扣金额 - 待缴金额
            BigDecimal surplus = amountListSum.subtract(toBePaidMoney);
            int lessThanOrEqual = surplus.compareTo(BigDecimal.ZERO);
            //小于0
            if (lessThanOrEqual == -1) {
                periodVo.setDeductionAmount(String.valueOf(amountListSum));
                periodVoList.add(periodVo);
                break;
            } else if (lessThanOrEqual == 0) {
                //等于0
                periodVo.setDeductionAmount(String.valueOf(toBePaidMoney));
                periodVoList.add(periodVo);
                break;
            } else if (lessThanOrEqual == 1) {
                //大于0
                periodVo.setDeductionAmount(String.valueOf(toBePaidMoney));
                periodVoList.add(periodVo);
            }
            amountListSum = surplus;
        }

        // 计算盈余金额，只有在支持盈余退款时才记录
        if (supportSurplusRefund) {
            BigDecimal surplusAmount = originalAmountListSum.subtract(originalAmountListSum.subtract(amountListSum));
            if (surplusAmount.compareTo(BigDecimal.ZERO) > 0) {
                String deductionType = "4".equals(vo.getType()) ? "物业费" : "租金押金";
                log.info("{}抵扣完成后盈余金额: {}，支持线上退款", deductionType, surplusAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }

        return periodVoList;
    }

    /**
     * 协议其他信息项
     * @param contractCode
     * @return
     */
    @Override
    public List<BbsTemplateSeatEntity> seatInfoVoList(String contractCode,String isShow) {
        if(StringUtils.isBlank(isShow)){
            isShow = "1";
        }
        //查询签约信息
        BbsSignInfoEntity signInfoEntity = new LambdaQueryChainWrapper<>(signInfoMapper)
                .eq(BbsSignInfoEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(StrUtil.isNotEmpty(contractCode), BbsSignInfoEntity::getContractCode, contractCode)
                .one();

        //合同其他信息项
        List<BbsTemplateSeatEntity> list = new LambdaQueryChainWrapper<>(templateSeatMapper)
                .eq(BbsTemplateSeatEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsTemplateSeatEntity::getParentId, signInfoEntity.getSignId())
                .eq(BbsTemplateSeatEntity::getIsShow, isShow)
                .orderByAsc(BbsTemplateSeatEntity::getSort)
                .list();

        return list;
    }

    /**
     * 判断能否进行计组面积变更
     * @param contractCode
     * @param changeTypeItem
     * @return
     */
    @Override
    public String verifyChangeCalculated(String contractCode, String changeTypeItem) {
        if(StringUtils.isBlank(contractCode)){
            throw new McpException("合同编号为空");
        }
        if(StringUtils.isBlank(changeTypeItem)){
            throw new McpException("变更类型小项为空");
        }

        BbctContractManagementVo bbctContractManagementVo = selectContractByIdNo(contractCode);
        //合同中产品信息
        List<BbctContractSubjectMatterVo> subjectMatterList = bbctContractManagementVo.getSubjectMatterList();
        if (subjectMatterList == null || subjectMatterList.size() == 0) {
            throw new McpException(contractCode + "合同信息中subjectMatterList为空");
        }
        //合同中产品编号集合
        List<String> productNoList = subjectMatterList.stream().map(BbctContractSubjectMatterVo::getProductNo).collect(Collectors.toList());
        //查房态临时表面积
        List<LastedAreaVo> lastedAreaVoList =  selectLastedAreaByCodes(productNoList);
        if(lastedAreaVoList == null || lastedAreaVoList.size() == 0){
            //房态没查到数据
            throw new McpException("当前合同下商铺无面积变更,无法申请计租面积变更");
        }

        boolean isChange = false;
        //和房态查面积对比
        for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
            String productNo = subjectMatterVo.getProductNo();
            String houseStructArea = subjectMatterVo.getHouseStructArea();
            //根据面积类型判断是否重新赋值
            if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                houseStructArea = subjectMatterVo.getInnerSleeveArea();//套内建筑面积
            }
            //java8用productNo 和 lastedAreaVoList中houseCode对比找到一个记录
            LastedAreaVo lastedAreaVo = lastedAreaVoList.stream().filter(areaVo -> areaVo.getHouseCode().equals(productNo)).findFirst().orElse(null);
            if(lastedAreaVo == null){
                continue;
            }
            //房态建筑面积
            String buildArea = lastedAreaVo.getBuildArea();
            //根据面积类型判断是否重新赋值
            if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                buildArea = lastedAreaVo.getInnerSleeveArea();
            }
            log.info("{}合同面积:{},房态面积:{}",contractCode,houseStructArea,buildArea);
            //合同里的面积 和 房态不一致
            if(!(
                    (StringUtils.isBlank(houseStructArea) && StringUtils.isNotBlank(buildArea))
                            || (StringUtils.isNotBlank(houseStructArea) && StringUtils.isBlank(buildArea))
                            || (new BigDecimal(houseStructArea).compareTo(new BigDecimal(buildArea)) == 0)
            )){
                //上面还有类似代码，修改时注意！！！！！！
                isChange = true;
                break;
            }
        }
        if(isChange){
            //有面积变更
            return "true";
        }else{
            throw new McpException("当前合同下商铺无面积变更,无法申请计租面积变更");
        }
    }


    private void setPartyChangeInformation(BbsiContractChangeVo vo, BbctContractSignerVo signerVo) {
        vo.setCustomerNameOld(signerVo.getCustomerName());
        vo.setCustomerNoOld(signerVo.getCustomerNo());
//        vo.setCustomerIdNumberOld(signerVo.getCustomerIdNumber());
        vo.setCustomerIdTypeOld(signerVo.getCustomerIdType());
        vo.setLegalRepresentativeOld(signerVo.getLegalName());
        vo.setMailAddressOld(signerVo.getMailAddress());
        vo.setCustomerTelOld(signerVo.getCustomerTel());
        vo.setBankCardOld(signerVo.getBankCard());
        vo.setBankNameCodeOld(signerVo.getBankNameCode());
        vo.setBankNameOld(signerVo.getBankName());
        vo.setBankSubbranchCodeOld(signerVo.getBankSubbranchCode());
        vo.setBankSubbranchNameOld(signerVo.getBankSubbranchName());
        vo.setBankPhoneOld(signerVo.getBankPhone());
        vo.setBankCardNameOld(signerVo.getBankUserName());

        if("00".equals(signerVo.getCustomerType())){
            vo.setCustomerIdNumberOld(signerVo.getCustomerIdNumber());
        }else {
            vo.setCustomerIdNumberOld(signerVo.getCustomerCreditCode());
        }

        //省市  存的是json串，待解析
        String customerExtend = signerVo.getCustomerExtend();
        if (StringUtils.isNotBlank(customerExtend)) {
            // 解析json串
            Map<String, String> map = JSON.parseObject(customerExtend, Map.class);
            vo.setProCodeOld(map.get("proCode"));
            vo.setProNameOld(map.get("proName"));
            vo.setCityCodeOld(map.get("cityCode"));
            vo.setCityNameOld(map.get("cityName"));
        }
        vo.setCustomerType(signerVo.getCustomerType());
        vo.setCustomerTypeOld(signerVo.getCustomerType());
    }

    private void setBusinessFormatChangeInformation(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo) {
        vo.setBusinessFormatOld(bbctContractManagementVo.getBusinessFormat());
        vo.setBusinessFormatNameOld(bbctContractManagementVo.getBusinessFormatName());

    }

    private void setLeasePeriodChangeInformation(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo) {
        vo.setContractBeginTimeOld(bbctContractManagementVo.getContractBeginTime());
        vo.setContractEndTimeOld(bbctContractManagementVo.getContractEndTime());
    }

    private void setDepositChangeInformation(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo) {
        vo.setCashPledgeCodeOld(bbctContractManagementVo.getCashPledgeCode());
        vo.setCashPledgeValueOld("0.00");
        // 这个注释废弃 推到10月份了 calculateDeposit  、  参考BbsSignInfoExtServiceImpl的ChargeRespondVo<PreviewBillsResultVo> previewBills = this.getPreviewBills(signInfoEntity);
        //新逻辑 老的押金取账单上的押金
        //按照合同编号查工银账单
        BbpmBillManagementVo billManagementVo = new BbpmBillManagementVo();
        if(StringUtils.isBlank(vo.getProjectId())){
            throw new RuntimeException("查询老合同保证金时项目编号为空");
        }
        billManagementVo.setProjectId(vo.getProjectId());
        billManagementVo.setContractCode(vo.getContractCode());
        billManagementVo.setChargeSubjectNo("02");
        //01企业，02个人
        billManagementVo.setChargeOwner("02");
        //先查个人再查企业
        AppReply<List<BbpmBillManagementVo>> listAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
        if (!checkAppReply(listAppReply) || listAppReply.getData().size() == 0) {
            billManagementVo.setChargeOwner("01");
            listAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
        }
        if(checkAppReply(listAppReply) && listAppReply.getData().size() > 0){
            BigDecimal sum = listAppReply.getData().stream().map(BbpmBillManagementVo::getShouldPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setCashPledgeValueOld(sum.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        }
//        vo.setCashPledgeValueOld(String.valueOf(bbctContractManagementVo.getDeposit()));
    }

    private void setRentStandardChangeInformation(BbsiContractChangeVo vo,BbctContractManagementVo bbctContractManagementVo, List<BbctContractSubjectMatterVo> subjectMatterList) {
        List<BbsChangeResultProductVo> productVoList = new ArrayList<>();

        for (BbctContractSubjectMatterVo subjectMatterVo : subjectMatterList) {
            BbsChangeResultProductVo productVo = new BbsChangeResultProductVo();
            String rentUnit = subjectMatterVo.getRentUnit();
            String unit = "1";
            if (StringUtils.isNotBlank(rentUnit)) {
                if (rentUnit.contains("元/月")) {
                    unit = "2";
                } else if (rentUnit.contains("元/年")) {
                    unit = "3";
                }
            }
            productVo.setProductNoOld(subjectMatterVo.getProductNo());
            productVo.setProductNameOld(subjectMatterVo.getProductName());
            if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                productVo.setHouseStructAreaOld(subjectMatterVo.getInnerSleeveArea());//套内建筑面积
            }else{
                productVo.setHouseStructAreaOld(subjectMatterVo.getHouseStructArea());//建筑面积
            }
            productVo.setRentStandardUnitNameOld(rentUnit);
            productVo.setRentStandardOld(subjectMatterVo.getRent() != null ? Double.valueOf(subjectMatterVo.getRent()) : 0);
            productVo.setRentStandardUnitOld(unit);

            productVo.setProductNo(subjectMatterVo.getProductNo());
            productVo.setProductName(subjectMatterVo.getProductName());
            if(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(bbctContractManagementVo.getAreaType())) {
                productVo.setHouseStructArea(subjectMatterVo.getInnerSleeveArea());//套内建筑面积
            }else{
                productVo.setHouseStructArea(subjectMatterVo.getHouseStructArea());//建筑面积
            }
            productVo.setRentStandardUnitName(rentUnit);
            productVo.setRentStandard(null);
            productVo.setRentStandardUnit(unit);

            productVoList.add(productVo);
        }
        vo.setProductVoList(productVoList);
    }

    private void setRentIncrementChangeInformation(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo) {
        String increaseJson = bbctContractManagementVo.getIncreaseJson();
        if (StringUtils.isNotBlank(increaseJson)) {
            JsonRootBean jsonRootBean = new JsonRootBean();
            List<IncrementalInfoArray> incrementalInfoArrays =  JsonToObjectUtil.jsonToIncrementalInfoArray(increaseJson);
            if (incrementalInfoArrays != null && incrementalInfoArrays.size() > 0) {
                IncrementalInfo incrementalInfo = new IncrementalInfo();
                incrementalInfo.setIncrementalInfoArray(incrementalInfoArrays);
                jsonRootBean.setIncrementalInfo(incrementalInfo);
                vo.setRentStandardJsonOld(jsonRootBean);
            }
//            JsonRootBean jsonRootBean = new JsonRootBean();
//            List<IncrementalInfoArray> incrementalInfoArrays = JSON.parseObject(increaseJson, new TypeReference<List<IncrementalInfoArray>>() {
//            });
//            if (incrementalInfoArrays != null && incrementalInfoArrays.size() > 0) {
//                IncrementalInfo incrementalInfo = new IncrementalInfo();
//                incrementalInfo.setIncrementalInfoArray(incrementalInfoArrays);
//                jsonRootBean.setIncrementalInfo(incrementalInfo);
//                vo.setRentStandardJsonOld(jsonRootBean);
//            }
        }
    }



    /**
     * 获取并设置变更前账单周期描述
     * 
     * @param vo 合同变更VO
     * @param bbctContractManagementVo 合同管理信息
     */
    private void setBillCycleDescriptionOld(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo) {
        try {
            // 获取项目ID
            List<BbctContractSubjectMatterVo> subjectMatterList = bbctContractManagementVo.getSubjectMatterList();
            if (subjectMatterList == null || subjectMatterList.isEmpty()) {
                log.warn("合同 {} 的产品信息为空，无法获取账单周期信息", vo.getContractCode());
                return;
            }
            
            String projectId = subjectMatterList.get(0).getProjectId();
            
            // 查询账单信息获取billCycle
            BbpmBillManagementVo billManagementVo = new BbpmBillManagementVo();
            billManagementVo.setProjectId(projectId);
            billManagementVo.setContractCode(vo.getContractCode());
            billManagementVo.setChargeSubjectNo("01");
            
            // 先查询个人账单
            billManagementVo.setChargeOwner("02");
            AppReply<List<BbpmBillManagementVo>> listAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
            
            // 如果个人账单为空，再查询企业账单
            if (!checkAppReply(listAppReply) || listAppReply.getData().isEmpty()) {
                billManagementVo.setChargeOwner("01");
                listAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
            } else {
                // 同时查询企业账单并合并
                billManagementVo.setChargeOwner("01");
                AppReply<List<BbpmBillManagementVo>> compAppReply = bbctPaymentV2FeignClient.selectMyBillList(billManagementVo);
                if (checkAppReply(compAppReply) && !compAppReply.getData().isEmpty()) {
                    listAppReply.getData().addAll(compAppReply.getData());
                }
            }
            
            if (!checkAppReply(listAppReply) || listAppReply.getData().isEmpty()) {
                log.info("合同 {} 未查询到账单信息，无法获取账单周期", vo.getContractCode());
                return;
            }
            
            // 提取账单中的billCycle字段并去重，按chargeSubjectPeriod排序
            List<String> billCycleList = listAppReply.getData().stream()
                    .sorted(Comparator.comparing(BbpmBillManagementVo::getChargeSubjectPeriod, Comparator.nullsLast(Comparator.naturalOrder())))
                    .map(BbpmBillManagementVo::getBillCycle)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .map(this::convertBillCycleCodeToName)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            
            if (!billCycleList.isEmpty()) {
                // 将账单周期列表转换为JSON数组格式存储
                String billCycleDescOld = JSON.toJSONString(billCycleList);
                vo.setBillCycleOld(billCycleDescOld);
                
                // 同时设置转换后的List供前端使用
                vo.setBillCycleOldList(billCycleList);
                
                log.info("合同 {} 的变更前账单周期描述: {}", vo.getContractCode(), billCycleDescOld);
            } else {
                log.info("合同 {} 未获取到有效的账单周期信息", vo.getContractCode());
            }
            
        } catch (Exception e) {
            log.error("获取合同 {} 的账单周期描述失败", vo.getContractCode(), e);
        }
    }
    
    /**
     * 将账单周期代码转换为中文描述
     * 
     * @param billCycleCode 账单周期代码
     * @return 账单周期中文描述
     */
    private String convertBillCycleCodeToName(String billCycleCode) {
        if (StringUtils.isBlank(billCycleCode)) {
            return "";
        }
        
        // 根据账单周期代码转换为中文描述
        switch (billCycleCode) {
            case "01":
                return "月缴";
            case "02":
                return "季缴";
            case "03":
                return "半年缴";
            case "04":
                return "年缴";
            case "05":
                return "两月缴";
            case "06":
                return "四月缴";
            default:
                // 如果是未知代码，尝试通过字典转换
                try {
                    McpDictEntity dictEntity = mcpDictSession.getMcpDictUtil().getDictInfoByCode("PAYMENT_CYCLE_CODE", billCycleCode);
                    if (dictEntity != null && StringUtils.isNotBlank(dictEntity.getMeaning())) {
                        return dictEntity.getMeaning();
                    }
                } catch (Exception e) {
                    log.warn("通过字典转换账单周期代码 {} 失败", billCycleCode, e);
                }
                log.warn("未知的账单周期代码: {}", billCycleCode);
                return billCycleCode; // 如果无法转换，返回原代码
        }
    }

    private void setPaymentCycleContractTimeInformation(BbsiContractChangeVo vo, BbctContractManagementVo bbctContractManagementVo) {
        // 缴费周期变更时，从老合同信息中获取合同开始和结束时间
        vo.setContractBeginTimeOld(bbctContractManagementVo.getContractBeginTime());
        vo.setContractEndTimeOld(bbctContractManagementVo.getContractEndTime());
    }


    /**
     * 验证合同类型接口
     */
    @Override
    public String verifySameType(String contractCode, String changeTypeItem,String ccId) {
        //nacos配置是否验证
        if(!isOk){
            return "false";
        }

        if(StringUtils.isBlank(changeTypeItem)){
            throw new McpException("变更类型小项不能为空");
        }

        //基本信息变更不验证这个
        if(changeTypeItem.contains("11") || changeTypeItem.contains("12")
                || changeTypeItem.contains("13") || changeTypeItem.contains("14")){
            return "false";
        }

        // 按合同号和变更类型小项 查询合同变更记录表， 看是否存在交叉的协议，
        // 存在交叉协议，再看这个协议是否完成 或者 终止 ，如果是完成或者终止状态，就可以发起
        List<BbsiContractChangeVo> contractChangeVoList = baseMapper.selectByChangeTypeItems(contractCode, changeTypeItem);
        if (contractChangeVoList != null && contractChangeVoList.size() > 0) {
            //有交叉
            for (BbsiContractChangeVo contractChangeVo : contractChangeVoList) {
                if(StringUtils.isNotBlank(ccId) && changeTypeItem.equals(contractChangeVo.getChangeTypeItem())){
                    //修改时 一模一样为修改
                    continue;
                }
                //当前选择的合同存在暂存的合同变更数据不能选择
                if("0".equals(contractChangeVo.getChangeStatus())){
                    throw new RuntimeException("当前选择的合同存在暂存的合同变更数据不能选择");
                }
                BbsSignInfoVo vo = new BbsSignInfoVo();
                vo.setContractCode(contractChangeVo.getAgreementCode());
                List<BbsSignInfoVo> signInfoList = bbsSignInfoMapper.selectByContractCodeV2(vo);
                if (signInfoList != null && signInfoList.size() > 0) {
                    BbsSignInfoVo signInfo = signInfoList.get(0);
                    if (!(SignStatusEnum.SIGNED.getCode().equals(signInfo.getSignStatus()) || SignStatusEnum.STOP.getCode().equals(signInfo.getSignStatus()))) {
                        String msg = "有在途的合同变更,禁止新增合同变更";
                        if(StringUtils.isNotBlank(contractChangeVo.getAgreementCode())){
                            msg =msg +"[在途合同的协议号为" + contractChangeVo.getAgreementCode() + "]";
                        }
                        throw new RuntimeException(msg);
                    }
                }
            }
        }else{
            //无交叉
            contractChangeVoList = baseMapper.selectByChangeTypeItems(contractCode, null);
            if (contractChangeVoList != null && contractChangeVoList.size() > 0) {
                for (BbsiContractChangeVo contractChangeVo : contractChangeVoList) {
                    //当前选择的合同存在暂存的合同变更数据不能选择
                    if("0".equals(contractChangeVo.getChangeStatus())){
                        throw new RuntimeException("当前选择的合同存在暂存的合同变更数据不能选择");
                    }
                }
            }
        }
        return "false";
    }

    /**
     * 验证是否欠费
     *
     * @param contractCode
     * @return
     */
    @Override
    public String verifyArrears(String contractCode) {
        BbctContractManagementVo contractManagementVo = selectContractByIdNo(contractCode);
        String signType = contractManagementVo.getBusinessTypeCode2();
        List<BbctContractSubjectMatterVo> productList = contractManagementVo.getSubjectMatterList();
        String projectId = productList.get(0).getProjectId();
        BbpmBillManagementPageVo managementPageVo = new BbpmBillManagementPageVo();
        managementPageVo.setPageSize(10);
        managementPageVo.setPageNumber(1);
        managementPageVo.setFindAll(true);
        managementPageVo.setProjectId(projectId);
        managementPageVo.setContractCode(contractCode);
        managementPageVo.setChargeOwner(SignTypeEnum.WHOLESALE_RENTAL.getCode().equals(signType) ? "01" : "02");
        managementPageVo.setBillStatus("02,03");
        managementPageVo.setStatus("01");
        managementPageVo.setPayableStartDate(DateUtil.getMinDate());
        managementPageVo.setPayableEndDate(DateUtil.getDate());
        log.info("verifyArrears查询账单请求参数:" + JSONObject.toJSONString(managementPageVo));
        AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> appReply = bbpaymentFeignClient.selectByPageRecord(managementPageVo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            log.error("查询账单失败:" + (Objects.isNull(appReply) ? null : appReply.toString()));
            throw new McpException("查询账单失败," + JSONObject.toJSONString(appReply));
        }
        if (CollectionUtils.isNotEmpty(appReply.getData().getRows())) {
            return "true";
        }
        return "false";
    }

    /**
     * 协议其他信息
     *
     * @param contractTemplateId
     * @return
     */
    @Override
    public List<BbctContractTemplateSeatVo> selectContractTemplateSeatByTemplateId(String contractTemplateId) {
        AppReply<BbctContractTemplateVo> appReply = contractFeignClient.selectContractTemplateById(contractTemplateId);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("查询合同信息模板失败[详情: %s, contractTemplateId: %s]", appReply, contractTemplateId));
        }
        List<BbctContractTemplateSeatVo> seatList = appReply.getData().getSeatList();
        //选择seatList中isShow为1的
        seatList = seatList.stream().filter(seat -> seat.getIsShow().equals("1")).collect(Collectors.toList());
        return seatList;
    }


    /**
     * 查询合同信息
     *
     * @param contractCode
     * @return
     */
    private BbctContractManagementVo selectContractByIdNo(String contractCode) {
        AppReply<BbctContractManagementVo> appReply = contractFeignClient.selectByIdNo(contractCode, null);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    /**
     * 查拆分的房源
     *
     * @param houseCode
     * @return
     */
    private RoomBedByHouseCodeVo selectRoomBedByHouseCode(String houseCode) {
        AppReply<RoomBedByHouseCodeVo> appReply = bbHousingFeignClient.selectRoomBedByHouseCode(houseCode);
        log.info(houseCode + "selectRoomBedByHouseCode查询房源拆分信息返回参数:" + JSONObject.toJSONString(appReply));
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("根据房源标识查询房间床位信息失败[详情: %s, houseCode: %s]", appReply, houseCode));
        }
        if (appReply.getData() != null && appReply.getData().getSplitVos() != null && appReply.getData().getSplitVos().size() > 0) {
            return appReply.getData();
        }
        return null;
    }

    /**
     * 查房态临时表面积
     * @param houseCodeList
     * @return
     */
    private List<LastedAreaVo> selectLastedAreaByCodes(List<String> houseCodeList) {
        log.info("selectLastedAreaByCodes查房态临时表面积请求参数:" + JSONObject.toJSONString(houseCodeList));
        AppReply<List<LastedAreaVo>> appReply = bbHousingFeignClient.selectLastedAreaByCodes(houseCodeList);
        log.info("selectLastedAreaByCodes查房态临时表面积返回参数:" + JSONObject.toJSONString(appReply));
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("根据房源标识查询房态临时表面积失败[详情: %s, houseCodeList: %s]", appReply, houseCodeList));
        }
        if (appReply.getData() != null   && appReply.getData().size() > 0) {
            return appReply.getData();
        }
        return null;
    }

    /**
     * 拼接租金json
     */
    public static JsonRootBean getRentStandardJson(String type, String ccId, BbsChangeIncrementalConfigMapper bbsChangeIncrementalConfigMapper) {
        JsonRootBean jsonRootBean = new JsonRootBean();
        IncrementalInfo incrementalInfo = new IncrementalInfo();
//        incrementalInfo.setIncrementalFlag(signInfoEntity.getRentIncrementalFlag());
//        incrementalInfo.setIncrementalType(signInfoEntity.getRentIncrementalType());
//        if ("1".equals(signInfoEntity.getRentIncrementalFlag())){
        incrementalInfo.setIncrementalInfoArray(getIncremental(type, ccId, bbsChangeIncrementalConfigMapper));
//        }
        jsonRootBean.setIncrementalInfo(incrementalInfo);
        if (incrementalInfo.getIncrementalInfoArray() == null || incrementalInfo.getIncrementalInfoArray().size() == 0) {
            jsonRootBean = null;
        }
        return jsonRootBean;
    }


    public static List<IncrementalInfoArray> getIncremental(String type, String ccId, BbsChangeIncrementalConfigMapper bbsChangeIncrementalConfigMapper) {
        List<IncrementalInfoArray> list = new ArrayList<>();
        List<BbsChangeIncrementalConfigEntity> incrementalConfigEntities = new LambdaQueryChainWrapper<>(bbsChangeIncrementalConfigMapper)
                .eq(BbsChangeIncrementalConfigEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsChangeIncrementalConfigEntity::getCcId, ccId)
                .eq(BbsChangeIncrementalConfigEntity::getType, type)
                .eq(BbsChangeIncrementalConfigEntity::getStandardType, "rent")
                .list().stream().sorted(Comparator.comparing(BbsChangeIncrementalConfigEntity::getCreateTime)).collect(Collectors.toList());
        incrementalConfigEntities.forEach(item -> {
            IncrementalInfoArray array = new IncrementalInfoArray();
            BeanUtils.copyProperties(item, array);
            list.add(array);
        });
        return list;
    }

    @Override
    public List<BbsChangeCalculatedProductVo> selectByCcId(String ccId){
        List<BbsChangeCalculatedProductVo> calculatedProductVoList = bbsChangeCalculatedProductMapper.selectByCcId(ccId);
        return calculatedProductVoList;
    }

    @Override
    public AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> selectByPageRecordBill(BbpmBillManagementPageVo vo) {
//        if (StringUtils.isNotBlank(vo.getTenantCode())) {
//            log.info("前台传的用户id" + vo.getTenantCode());
//            vo.setTenantCode(iBbcustomerService.getCustomerIdByUserId());
//        }
        //app请求接口必须有这个参数，不选择也带着这个参数，默认就是0。后台处理
        if(vo.getChargeSubjectPeriod() != null && vo.getChargeSubjectPeriod().intValue() == 0){
            vo.setChargeSubjectPeriod(null);
        }

        if(StringUtils.isBlank(vo.getHouseName())){
            vo.setHouseName(vo.getHouseName1());
        }

        if(StringUtils.isBlank(vo.getChargeOwner())){
            //01企业，02个人
            vo.setChargeOwner("02");
        }

        if(StringUtils.isBlank(vo.getChargeSubjectNo())){
            vo.setChargeSubjectNo("01");
        }

        AppReply<PageResult<List<BbpmBillManagementPageResultVo>>> pageResultAppReply = bbpaymentFeignClient.selectByPageRecord(vo);

        //如果pageResultAppReply结果为空，需要再查询一下企业账单
        if(pageResultAppReply.getData().getRows().isEmpty()){
            vo.setChargeOwner("01");
            pageResultAppReply = bbpaymentFeignClient.selectByPageRecord(vo);
        }

        return pageResultAppReply;
    }

    /**
     * 校验账单次号
     *
     * @param allDeductibleBillList 所有抵扣账单列表
     * @param deductibleBillList    勾选的抵扣账单列表
     */
    private void checkBatchOrder(List<BbsChangePreviewBillVo> allDeductibleBillList,
                                 List<BbsChangePreviewBillVo> deductibleBillList) {
        // 获取用户勾选的最大账单次号
        int maxBatch = deductibleBillList.stream()
                .mapToInt(BbsChangePreviewBillVo::getChargePeriod)
                .max()
                .orElse(0);
        // 检查所有小于maxBatch的账单次号是否全部勾选
        for (int batch = 1; batch < maxBatch; batch++) {
            // 计算当前账单次号在全集和勾选集中的数量
            long total = countBatchInList(allDeductibleBillList, batch);
            long selected = countBatchInList(deductibleBillList, batch);
            if (selected == 0) {
                continue;
            }
            if (selected != total) {
                throw new McpException("多房源同一账期未全部抵扣，不可跳期");
            }
        }
    }

    /**
     * 统计列表中指定账单次号的数量
     *
     * @param list         列表
     * @param chargePeriod 账单次号
     * @return 列表中指定账单次号的数量
     */
    private long countBatchInList(List<BbsChangePreviewBillVo> list, int chargePeriod) {
        return list.stream()
                .filter(vo -> vo.getChargePeriod() == chargePeriod)
                .count();
    }

    /**
     * 检查是否多条房源
     *
     * @param allDeductibleBillList
     * @return
     */
    private boolean checkMultipleRoom(List<BbsChangePreviewBillVo> allDeductibleBillList) {
        Set<String> houseIdSet = new HashSet<>();
        for (BbsChangePreviewBillVo previewBillVo : allDeductibleBillList) {
            houseIdSet.add(previewBillVo.getHouseId());
        }
        if (houseIdSet.size() > 1) {
            return true;
        }
        return false;
    }

    /**
     * 构建费用汇总列表
     *
     * @param bills 试算结果
     * @param changeTypeItem 变更类型
     * @param refundPaymentMethod 退/缴方式 (1补缴, 2退回银行卡, 3抵扣租金)
     * @param ccId 合同变更主键ID，用于查询抵扣数据
     * @return 费用汇总列表
     */
    private List<FeesSummaryVo> buildFeesSummaryList(BbsChangePreviewBillResultVo bills, String changeTypeItem, String refundPaymentMethod, String ccId) {
        List<FeesSummaryVo> feesSummaryList = new ArrayList<>();
        
        if (bills == null) {
            return feesSummaryList;
        }

        int sequenceCounter = 1; // 用于生成连续编号

        // 根据变更类型决定显示哪些费用项目
        boolean showRent = false;
        boolean showBond = false;
        boolean showProperty = false;

        // 保证金变更：只有押金
        if (changeTypeItem.contains(ContractChangeTypeEnum.BOND_CHANGE.getCode())) {
            showBond = true;
        }
        
        // 租金变更：有租金和押金
        if (changeTypeItem.contains(ContractChangeTypeEnum.RENT_CHANGE.getCode())) {
            showRent = true;
            showBond = true;
        }
        
        // 计租面积变更和缩租面积变更：有租金、押金、物业费
        if (changeTypeItem.contains(ContractChangeTypeEnum.CALCULATED_RENTAL_AREA_CHANGE.getCode())
                || changeTypeItem.contains(ContractChangeTypeEnum.LEASE_AREA_REDUCTION_CHANGE.getCode())) {
            showRent = true;
            showBond = true;
            showProperty = true;
        }

        // 获取抵扣金额列表，用于计算各项费用
        List<PreviewBillsResultDeductedAmount> deductedAmountList = bills != null ? bills.getDeductedAmountList() : new ArrayList<>();
        
        // 计算各项费用的金额
        BigDecimal rentAmount = getAmountByChargeSubject(deductedAmountList, "01"); // 租金
        BigDecimal bondAmount = getAmountByChargeSubject(deductedAmountList, "02"); // 押金
        BigDecimal propertyAmount = getAmountByChargeSubject(deductedAmountList, "07"); // 物业费
        
        // 计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (showRent) totalAmount = totalAmount.add(rentAmount);
        if (showBond) totalAmount = totalAmount.add(bondAmount);
        if (showProperty) totalAmount = totalAmount.add(propertyAmount);

        // 构建房屋租金汇总
        if (showRent) {
            FeesSummaryVo rentSummary = new FeesSummaryVo();
            rentSummary.setSequenceNumber(String.format("%02d", sequenceCounter++));
            rentSummary.setFeeItem("房屋租金");
            
            // 根据退/缴方式设置不同的字段
            if ("1".equals(refundPaymentMethod)) {
                // 补缴方式：应收金额合计、应退/抵金额合计
                rentSummary.setReceivableAmount(rentAmount.abs());
                rentSummary.setRefundAmount(BigDecimal.ZERO);
                rentSummary.setDeductionAmount(null);
                rentSummary.setRefundTotalAmount(null);
            } else {
                // 退回银行卡或抵扣租金：应退/抵金额合计、预计抵扣金额合计、预计退款金额合计
                rentSummary.setReceivableAmount(BigDecimal.ZERO);
                rentSummary.setRefundAmount(rentAmount);
                rentSummary.setDeductionAmount(BigDecimal.ZERO);
                rentSummary.setRefundTotalAmount(rentAmount.compareTo(BigDecimal.ZERO) > 0 ? rentAmount : BigDecimal.ZERO);
            }
            feesSummaryList.add(rentSummary);
        }

        // 构建押金汇总
        if (showBond) {
            FeesSummaryVo bondSummary = new FeesSummaryVo();
            bondSummary.setSequenceNumber(String.format("%02d", sequenceCounter++));
            bondSummary.setFeeItem("押金");
            
            // 根据退/缴方式设置不同的字段
            if ("1".equals(refundPaymentMethod)) {
                // 补缴方式：应收金额合计、应退/抵金额合计
                bondSummary.setReceivableAmount(bondAmount.abs());
                bondSummary.setRefundAmount(BigDecimal.ZERO);
                bondSummary.setDeductionAmount(null);
                bondSummary.setRefundTotalAmount(null);
            } else {
                // 退回银行卡或抵扣租金：应退/抵金额合计、预计抵扣金额合计、预计退款金额合计
                bondSummary.setReceivableAmount(BigDecimal.ZERO);
                bondSummary.setRefundAmount(bondAmount);
                bondSummary.setDeductionAmount(BigDecimal.ZERO);
                bondSummary.setRefundTotalAmount(bondAmount.compareTo(BigDecimal.ZERO) > 0 ? bondAmount : BigDecimal.ZERO);
            }
            feesSummaryList.add(bondSummary);
        }

        // 构建物业费汇总
        if (showProperty) {
            FeesSummaryVo propertySummary = new FeesSummaryVo();
            propertySummary.setSequenceNumber(String.format("%02d", sequenceCounter++));
            propertySummary.setFeeItem("物业费");
            
            // 根据退/缴方式设置不同的字段
            if ("1".equals(refundPaymentMethod)) {
                // 补缴方式：应收金额合计、应退/抵金额合计
                propertySummary.setReceivableAmount(propertyAmount.abs());
                propertySummary.setRefundAmount(BigDecimal.ZERO);
                propertySummary.setDeductionAmount(null);
                propertySummary.setRefundTotalAmount(null);
            } else {
                // 退回银行卡或抵扣租金：应退/抵金额合计、预计抵扣金额合计、预计退款金额合计
                propertySummary.setReceivableAmount(BigDecimal.ZERO);
                propertySummary.setRefundAmount(propertyAmount);
                propertySummary.setDeductionAmount(BigDecimal.ZERO);
                propertySummary.setRefundTotalAmount(propertyAmount.compareTo(BigDecimal.ZERO) > 0 ? propertyAmount : BigDecimal.ZERO);
            }
            feesSummaryList.add(propertySummary);
        }

        // 构建合计行
        if (!feesSummaryList.isEmpty()) {
            FeesSummaryVo totalSummary = new FeesSummaryVo();
            totalSummary.setSequenceNumber(String.format("%02d", sequenceCounter));
            totalSummary.setFeeItem("合计");
            
            // 根据退/缴方式设置不同的字段
            if ("1".equals(refundPaymentMethod)) {
                // 补缴方式：应收金额合计、应退/抵金额合计
                totalSummary.setReceivableAmount(totalAmount.abs());
                totalSummary.setRefundAmount(BigDecimal.ZERO);
                totalSummary.setDeductionAmount(null);
                totalSummary.setRefundTotalAmount(null);
            } else {
                // 退回银行卡或抵扣租金：应退/抵金额合计、预计抵扣金额合计、预计退款金额合计
                totalSummary.setReceivableAmount(BigDecimal.ZERO);
                totalSummary.setRefundAmount(totalAmount);
                totalSummary.setDeductionAmount(BigDecimal.ZERO);
                totalSummary.setRefundTotalAmount(totalAmount.compareTo(BigDecimal.ZERO) > 0 ? totalAmount : BigDecimal.ZERO);
            }
            feesSummaryList.add(totalSummary);
        }

        // 如果是抵扣租金方式且有ccId，则查询抵扣数据并应用到费用汇总
        if ("3".equals(refundPaymentMethod) && StringUtils.isNotBlank(ccId)) {
            try {
                log.info("查询抵扣数据并应用到费用汇总，ccId: {}", ccId);
                
                // 查询租金押金抵扣数据（type=1）
                List<BbsChangeDetermineDeductionPeriodVo> rentAndBondDeductionList = bbsChangeDetermineDeductionPeriodMapper.selectByCcId(ccId, "1");
                
                // 查询物业费抵扣数据（type=3）
                List<BbsChangeDetermineDeductionPeriodVo> propertyDeductionList = bbsChangeDetermineDeductionPeriodMapper.selectByCcId(ccId, "3");
                
                // 如果有抵扣数据，则应用到费用汇总
                if ((rentAndBondDeductionList != null && !rentAndBondDeductionList.isEmpty()) ||
                    (propertyDeductionList != null && !propertyDeductionList.isEmpty())) {
                    
                    log.info("找到抵扣数据，租金押金抵扣: {}, 物业费抵扣: {}", 
                            rentAndBondDeductionList != null ? rentAndBondDeductionList.size() : 0,
                            propertyDeductionList != null ? propertyDeductionList.size() : 0);
                    
                    // 使用现有的updateFeesSummaryWithDeduction方法来更新费用汇总
                    feesSummaryList = updateFeesSummaryWithDeduction(feesSummaryList, rentAndBondDeductionList, propertyDeductionList, refundPaymentMethod);
                }
            } catch (Exception e) {
                log.error("查询并应用抵扣数据失败，ccId: {}", ccId, e);
                // 如果查询抵扣数据失败，不影响费用汇总列表的返回，继续返回原始列表
            }
        }

        return feesSummaryList;
    }

    /**
     * 根据费用科目编号获取对应的金额
     *
     * @param deductedAmountList 抵扣金额列表
     * @param chargeSubjectNo 费用科目编号 (01:租金, 02:押金, 07:物业费)
     * @return 对应的金额，找不到时返回0
     */
    private BigDecimal getAmountByChargeSubject(List<PreviewBillsResultDeductedAmount> deductedAmountList, String chargeSubjectNo) {
        if (deductedAmountList == null || deductedAmountList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return deductedAmountList.stream()
                .filter(amount -> chargeSubjectNo.equals(amount.getChargeSubjectNo()))
                .map(PreviewBillsResultDeductedAmount::getDeductedAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 根据抵扣账单动态更新费用汇总列表
     *
     * @param originalFeesSummaryList 原始费用汇总列表
     * @param rentAndBondDeductionBillList 押金和租金抵扣账单列表
     * @param propertyDeductionBillList 物业费抵扣账单列表
     * @param refundPaymentMethod 退/缴方式
     * @return 更新后的费用汇总列表
     */
    @Override
    public List<FeesSummaryVo> updateFeesSummaryWithDeduction(List<FeesSummaryVo> originalFeesSummaryList,
                                                             List<BbsChangeDetermineDeductionPeriodVo> rentAndBondDeductionBillList,
                                                             List<BbsChangeDetermineDeductionPeriodVo> propertyDeductionBillList,
                                                             String refundPaymentMethod) {
        if (originalFeesSummaryList == null || originalFeesSummaryList.isEmpty()) {
            return new ArrayList<>();
        }

        // 合并两种类型的抵扣账单列表
        List<BbsChangeDetermineDeductionPeriodVo> allDeductionBillList = new ArrayList<>();
        if (rentAndBondDeductionBillList != null && !rentAndBondDeductionBillList.isEmpty()) {
            allDeductionBillList.addAll(rentAndBondDeductionBillList);
        }
        if (propertyDeductionBillList != null && !propertyDeductionBillList.isEmpty()) {
            allDeductionBillList.addAll(propertyDeductionBillList);
        }

        // 如果没有抵扣账单，直接返回原始列表
        if (allDeductionBillList.isEmpty()) {
            return new ArrayList<>(originalFeesSummaryList);
        }

        // 按费用科目编号累计抵扣金额
        Map<String, BigDecimal> deductionAmountMap = calculateDeductionAmountByChargeSubject(allDeductionBillList);

        // 创建新的费用汇总列表
        List<FeesSummaryVo> updatedFeesSummaryList = new ArrayList<>();
        BigDecimal totalDeductionAmount = BigDecimal.ZERO;
        BigDecimal totalRefundAmount = BigDecimal.ZERO;

        for (FeesSummaryVo originalSummary : originalFeesSummaryList) {
            // 跳过合计行，最后单独处理
            if ("合计".equals(originalSummary.getFeeItem())) {
                continue;
            }

            FeesSummaryVo updatedSummary = new FeesSummaryVo();
            updatedSummary.setSequenceNumber(originalSummary.getSequenceNumber());
            updatedSummary.setFeeItem(originalSummary.getFeeItem());
            updatedSummary.setReceivableAmount(originalSummary.getReceivableAmount());
            updatedSummary.setRefundAmount(originalSummary.getRefundAmount());

            // 根据费用项目类型获取对应的抵扣金额
            String chargeSubjectNo = getChargeSubjectNoByFeeItem(originalSummary.getFeeItem());
            BigDecimal deductionAmount = deductionAmountMap.getOrDefault(chargeSubjectNo, BigDecimal.ZERO);

            // 只有在退回银行卡或抵扣租金方式下才更新抵扣相关字段
            if (!"1".equals(refundPaymentMethod)) {
                updatedSummary.setDeductionAmount(deductionAmount);

                // 计算预计退款金额合计 = 应退/抵金额合计 - 预计抵扣金额合计
                BigDecimal refundAmount = originalSummary.getRefundAmount() != null ? originalSummary.getRefundAmount() : BigDecimal.ZERO;
                BigDecimal refundTotalAmount = refundAmount.subtract(deductionAmount);
                updatedSummary.setRefundTotalAmount(refundTotalAmount.compareTo(BigDecimal.ZERO) > 0 ? refundTotalAmount : BigDecimal.ZERO);

                totalDeductionAmount = totalDeductionAmount.add(deductionAmount);
                totalRefundAmount = totalRefundAmount.add(updatedSummary.getRefundTotalAmount());
            } else {
                updatedSummary.setDeductionAmount(originalSummary.getDeductionAmount());
                updatedSummary.setRefundTotalAmount(originalSummary.getRefundTotalAmount());
            }

            updatedFeesSummaryList.add(updatedSummary);
        }

        // 添加更新后的合计行
        addUpdatedTotalSummary(updatedFeesSummaryList, originalFeesSummaryList, totalDeductionAmount, totalRefundAmount, refundPaymentMethod);

        return updatedFeesSummaryList;
    }

    /**
     * 按费用科目编号累计抵扣金额
     *
     * @param deductionBillList 抵扣账单列表
     * @return 按费用科目编号分组的抵扣金额
     */
    private Map<String, BigDecimal> calculateDeductionAmountByChargeSubject(List<BbsChangeDetermineDeductionPeriodVo> deductionBillList) {
        Map<String, BigDecimal> deductionAmountMap = new HashMap<>();

        for (BbsChangeDetermineDeductionPeriodVo deductionBill : deductionBillList) {
            String chargeSubjectNo = deductionBill.getChargeSubjectNo();
            String deductionAmountStr = deductionBill.getDeductionAmount();

            if (StringUtils.isNotBlank(chargeSubjectNo) && StringUtils.isNotBlank(deductionAmountStr)) {
                try {
                    BigDecimal deductionAmount = new BigDecimal(deductionAmountStr);
                    deductionAmountMap.merge(chargeSubjectNo, deductionAmount, BigDecimal::add);
                } catch (NumberFormatException e) {
                    log.warn("抵扣金额格式错误: chargeSubjectNo={}, deductionAmount={}", chargeSubjectNo, deductionAmountStr, e);
                }
            } else {
                log.warn("抵扣账单数据不完整: chargeSubjectNo={}, deductionAmount={}", chargeSubjectNo, deductionAmountStr);
            }
        }

        return deductionAmountMap;
    }

    /**
     * 从抵扣账单中获取费用科目编号
     *
     * @param deductionBill 抵扣账单
     * @return 费用科目编号
     */
    private String getChargeSubjectNoFromDeductionBill(BbsChangeDetermineDeductionPeriodVo deductionBill) {
        // 直接使用抵扣账单中的 chargeSubjectNo
        return deductionBill.getChargeSubjectNo();
    }

    /**
     * 根据费用项目名称获取对应的费用科目编号
     *
     * @param feeItem 费用项目名称
     * @return 费用科目编号
     */
    private String getChargeSubjectNoByFeeItem(String feeItem) {
        if ("房屋租金".equals(feeItem)) {
            return "01"; // 租金
        } else if ("押金".equals(feeItem)) {
            return "02"; // 押金
        } else if ("物业费".equals(feeItem)) {
            return "07"; // 物业费
        }
        return "";
    }

    /**
     * 添加更新后的合计行
     *
     * @param updatedFeesSummaryList 更新后的费用汇总列表
     * @param originalFeesSummaryList 原始费用汇总列表
     * @param totalDeductionAmount 总抵扣金额
     * @param totalRefundAmount 总退款金额
     * @param refundPaymentMethod 退/缴方式
     */
    private void addUpdatedTotalSummary(List<FeesSummaryVo> updatedFeesSummaryList,
                                       List<FeesSummaryVo> originalFeesSummaryList,
                                       BigDecimal totalDeductionAmount,
                                       BigDecimal totalRefundAmount,
                                       String refundPaymentMethod) {
        // 查找原始合计行
        FeesSummaryVo originalTotalSummary = originalFeesSummaryList.stream()
                .filter(summary -> "合计".equals(summary.getFeeItem()))
                .findFirst()
                .orElse(null);

        if (originalTotalSummary != null) {
            FeesSummaryVo updatedTotalSummary = new FeesSummaryVo();
            updatedTotalSummary.setSequenceNumber(originalTotalSummary.getSequenceNumber());
            updatedTotalSummary.setFeeItem("合计");
            updatedTotalSummary.setReceivableAmount(originalTotalSummary.getReceivableAmount());
            updatedTotalSummary.setRefundAmount(originalTotalSummary.getRefundAmount());

            if (!"1".equals(refundPaymentMethod)) {
                updatedTotalSummary.setDeductionAmount(totalDeductionAmount);
                updatedTotalSummary.setRefundTotalAmount(totalRefundAmount);
            } else {
                updatedTotalSummary.setDeductionAmount(originalTotalSummary.getDeductionAmount());
                updatedTotalSummary.setRefundTotalAmount(originalTotalSummary.getRefundTotalAmount());
            }

            updatedFeesSummaryList.add(updatedTotalSummary);
        }
    }



    /**
     * 从JSON转换选择的账单
     */
    private void convertSelectedBillsFromJson(BbsiContractChangeVo vo) {
        try {
            if (StringUtils.isNotBlank(vo.getSelectedBillsJson())) {
                List<BbpmBillManagementPageResultVo> bills = 
                    PaymentCycleBillConverter.convertJsonToFullBillInfo(vo.getSelectedBillsJson());
                vo.setSelectedBills(bills);
                log.debug("从JSON转换选择的账单，数量: {}", bills != null ? bills.size() : 0);
            }
        } catch (Exception e) {
            log.error("从JSON转换选择的账单失败", e);
            vo.setSelectedBills(Collections.emptyList());
        }
    }

    /**
     * 判断是否为缴费周期变更类型
     */
    private boolean isPaymentCycleChangeType(String changeTypeItem) {
        if (StringUtils.isBlank(changeTypeItem)) {
            return false;
        }
        // 使用枚举的代码值来判断是否为缴费周期变更
        return changeTypeItem.contains(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode());
    }

    /**
     * 计算选择账单的时间范围
     *
     * @param selectedBills 选择的账单列表
     * @return 账单时间范围字符串，格式如：2024-07-25至2026-08-24
     */
    @Override
    public String calculateSelectedBillsDateRange(List<BbpmBillManagementPageResultVo> selectedBills) {
        if (selectedBills == null || selectedBills.isEmpty()) {
            log.warn("选择的账单列表为空");
            return "";
        }
        
        log.info("开始计算选择账单时间范围，账单数量: {}", selectedBills.size());
        
        // 按chargeSubjectPeriod排序
        List<BbpmBillManagementPageResultVo> sortedBills = selectedBills.stream()
                .sorted(Comparator.comparing(BbpmBillManagementPageResultVo::getChargeSubjectPeriod, 
                        Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        
        // 取第一个账单的开始时间和最后一个账单的结束时间
        String earliestBeginDate = sortedBills.get(0).getChargeSubjectBeginDate();
        String latestEndDate = sortedBills.get(sortedBills.size() - 1).getChargeSubjectEndDate();
        
        // 检查时间是否有效
        if (StringUtils.isBlank(earliestBeginDate) || StringUtils.isBlank(latestEndDate)) {
            log.warn("账单时间信息不完整: 开始时间={}, 结束时间={}", earliestBeginDate, latestEndDate);
            return "";
        }
        
        // 返回格式化的时间字符串
        String result = earliestBeginDate + "至" + latestEndDate;
        
        log.info("计算账单时间范围完成: {}", result);
        
        return result;
    }

 
 
}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/9 10:21
 */
@Data
public class PayRequestPo {
    /**
     * 交易类型
     */
    @ApiModelProperty(value = "交易类型")
    private String trantp;
    /**
     * 交易金额
     */
    @ApiModelProperty(value = "交易金额")
    private String amount;
    /**
     * MIS流水号
     */
    @ApiModelProperty(value = "MIS流水号")
    private String mistno;
    /**
     * 特色打印数据
     */
    @ApiModelProperty(value = "特色打印数据")
    private String data59;
    /**
     * 外附加在对账单上的信息
     */
    @ApiModelProperty(value = "外附加在对账单上的信息")
    private String pospbak;
    /**
     * 付款码
     */
    @ApiModelProperty(value = "付款码")
    private String qrcard;

    /**
     * 子商户应用号
     */
    @ApiModelProperty(value = "子商户应用号")
    private String subappid;

    /**
     * 二维码消费商品信息
     */
    @ApiModelProperty(value = "二维码消费商品信息")
    private GoodsDetailBO gsdetail;

    /**
     * 分行特色脚本ID号
     */
    @ApiModelProperty(value = "分行特色脚本ID号")
    private String funcid;

    /**
     * 小费金额
     */
    @ApiModelProperty(value = "小费金额")
    private String tipsum;

    /**
     * 交易日期
     */
    @ApiModelProperty(value = "交易日期")
    private String oauthd;

    /**
     * 交易卡号
     */
    @ApiModelProperty(value = "交易卡号")
    private String cardno;

    /**
     * 卡片有效期
     */
    @ApiModelProperty(value = "卡片有效期")
    private String expdat;
    /**
     * 系统检索号
     */
    @ApiModelProperty(value = "系统检索号")
    private String refeno;
    /**
     * 授权号
     */
    @ApiModelProperty(value = "授权号")
    private String authNo;
    /**
     * 商户号
     */
    @ApiModelProperty(value = "商户号")
    private String mercid;

    /**
     * 交易终端号
     */
    @ApiModelProperty(value = "交易终端号")
    private String termid;

    /**
     * 分期期数
     */
    @ApiModelProperty(value = "分期期数")
    private String istltm;

    /**
     * 预输入项
     */
    @ApiModelProperty(value = "预输入项")
    private String preinput;

    /**
     * 固定输入项
     */
    @ApiModelProperty(value = "固定输入项")
    private String adddatas;

//	/**
//	 * 二维码支付号
//	 */
//	private String qrcard;

    /**
     * 二维码订单号
     */
    @ApiModelProperty(value = "二维码订单号")
    private String qrordno;

//	/**
//	 * 子商户应用号
//	 */
//	private String subappid;

    /**
     * 商户外部订单号
     */
    @ApiModelProperty(value = "商户外部订单号")
    private String tag969;

    /**
     * 原交易类型
     */
    @ApiModelProperty(value = "原交易类型")
    private String tag970;

    /**
     * 收银台号 （20位，不足右补空格）
     */
    @ApiModelProperty(value = "收银台号")
    private String platId;

    /**
     * 收银员号 （20为，不足右补空格）
     */
    @ApiModelProperty(value = "收银员号")
    private String operId;

//	/**
//	 * 需要前置保存的业务关联数据
//	 */
//	private String pospbak;
}

package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 续签申请信息表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-21
 * @change 2023-09-21 by King-Y for init
 */
@ApiModel(value="BbsRenewalApplyInfoVo对象", description="续签申请信息表")
public class BbsRenewalApplyInfoVo extends BaseVo implements Serializable{


    /**
     * 申请信息id
     */
    @ApiModelProperty(value = "申请信息id")
    @NotBlank(message = "申请信息id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String renewalApplyInfoId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 小区名称
     */
    @ApiModelProperty(value = "小区名称")
                            private String communityName;

    /**
     * 商铺地址
     */
    @ApiModelProperty(value = "商铺地址")
                            private String productName;

    /**
     * 租户名称
     */
    @ApiModelProperty(value = "租户名称")
                            private String tenantName;

    /**
     * 租户性质
     */
    @ApiModelProperty(value = "租户性质")
                            private String customerType;

    /**
     * 租户性质名称
     */
    @ApiModelProperty(value = "租户性质名称")
                            private String customerTypeName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
                            private String customerTel;

    /**
     * 业态
     */
    @ApiModelProperty(value = "业态")
                            private String businessFormat;

    /**
     * 业态名称
     */
    @ApiModelProperty(value = "业态名称")
                            private String businessFormatName;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核)")
                            private String approveStatus;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
                            private String submitUserTel;

    /**
     * 申请说明
     */
    @ApiModelProperty(value = "申请说明")
                            private String submitIllustrate;

    /**
     * 附件id(多个以逗号分割)
     */
    @ApiModelProperty(value = "附件id(多个以逗号分割)")
                            private String fileId;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
                            private Integer delFlag;

    /**
     * @return 申请信息id
     */
    public String getRenewalApplyInfoId() {
        return renewalApplyInfoId;
    }

    public void setRenewalApplyInfoId(String renewalApplyInfoId) {
        this.renewalApplyInfoId = renewalApplyInfoId;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 小区名称
     */
    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    /**
     * @return 商铺地址
     */
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * @return 租户名称
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 租户性质
     */
    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    /**
     * @return 租户性质名称
     */
    public String getCustomerTypeName() {
        return customerTypeName;
    }

    public void setCustomerTypeName(String customerTypeName) {
        this.customerTypeName = customerTypeName;
    }

    /**
     * @return 联系电话
     */
    public String getCustomerTel() {
        return customerTel;
    }

    public void setCustomerTel(String customerTel) {
        this.customerTel = customerTel;
    }

    /**
     * @return 业态
     */
    public String getBusinessFormat() {
        return businessFormat;
    }

    public void setBusinessFormat(String businessFormat) {
        this.businessFormat = businessFormat;
    }

    /**
     * @return 业态名称
     */
    public String getBusinessFormatName() {
        return businessFormatName;
    }

    public void setBusinessFormatName(String businessFormatName) {
        this.businessFormatName = businessFormatName;
    }

    /**
     * @return 审批状态(1.通过 2.未通过 3.待审核)
     */
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    /**
     * @return 联系电话
     */
    public String getSubmitUserTel() {
        return submitUserTel;
    }

    public void setSubmitUserTel(String submitUserTel) {
        this.submitUserTel = submitUserTel;
    }

    /**
     * @return 申请说明
     */
    public String getSubmitIllustrate() {
        return submitIllustrate;
    }

    public void setSubmitIllustrate(String submitIllustrate) {
        this.submitIllustrate = submitIllustrate;
    }

    /**
     * @return 附件id(多个以逗号分割)
     */
    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    /**
     * @return 删除标识(1.未删除 0.已删除)
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsRenewalApplyInfoVo{" +
            "renewalApplyInfoId=" + renewalApplyInfoId +
            ", contractCode=" + contractCode +
            ", communityName=" + communityName +
            ", productName=" + productName +
            ", tenantName=" + tenantName +
            ", customerType=" + customerType +
            ", customerTypeName=" + customerTypeName +
            ", customerTel=" + customerTel +
            ", businessFormat=" + businessFormat +
            ", businessFormatName=" + businessFormatName +
            ", approveStatus=" + approveStatus +
            ", submitUserTel=" + submitUserTel +
            ", submitIllustrate=" + submitIllustrate +
            ", fileId=" + fileId +
            ", delFlag=" + delFlag +
        "}";
    }
}

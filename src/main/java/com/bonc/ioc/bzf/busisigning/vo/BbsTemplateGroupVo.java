package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 通知模板组表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-05
 * @change 2023-09-05 by <PERSON><PERSON><PERSON><PERSON><PERSON> for init
 */
@ApiModel(value = "BbsTemplateGroupVo对象", description = "通知模板组表")
public class BbsTemplateGroupVo extends McpBaseVo implements Serializable {


    /**
     * 模板组id
     */
    @ApiModelProperty(value = "模板组id")
    @NotBlank(message = "模板组id不能为空", groups = {UpdateValidatorGroup.class})
    private String templateGroupId;

    /**
     * 模板类型(1.站内信 2.短信)
     */
    @ApiModelProperty(value = "模板类型(1.站内信 2.短信)")
    private String templateType;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    private String templateId;

    /**
     * 成功模板id
     */
    @ApiModelProperty(value = "成功模板id")
    private String successTemplateId;

    /**
     * 失败模板id
     */
    @ApiModelProperty(value = "失败模板id")
    private String failedTemplateId;

    /**
     * 删除标识(0.否 1.是)
     */
    @ApiModelProperty(value = "删除标识(0.否 1.是)")
    private Integer delFlag;

    /**
     * @return 模板组id
     */
    public String getTemplateGroupId() {
        return templateGroupId;
    }

    public void setTemplateGroupId(String templateGroupId) {
        this.templateGroupId = templateGroupId;
    }

    /**
     * @return 模板类型(1.站内信 2.短信)
     */
    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    /**
     * @return 模板id
     */
    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * @return 成功模板id
     */
    public String getSuccessTemplateId() {
        return successTemplateId;
    }

    public void setSuccessTemplateId(String successTemplateId) {
        this.successTemplateId = successTemplateId;
    }

    /**
     * @return 失败模板id
     */
    public String getFailedTemplateId() {
        return failedTemplateId;
    }

    public void setFailedTemplateId(String failedTemplateId) {
        this.failedTemplateId = failedTemplateId;
    }

    /**
     * @return 删除标识(0.否 1.是)
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "BbsTemplateGroupVo{" +
                "templateGroupId=" + templateGroupId +
                ", templateType=" + templateType +
                ", templateId=" + templateId +
                ", successTemplateId=" + successTemplateId +
                ", failedTemplateId=" + failedTemplateId +
                ", delFlag=" + delFlag +
                "}";
    }
}

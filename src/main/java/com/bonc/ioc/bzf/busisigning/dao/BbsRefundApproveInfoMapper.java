package com.bonc.ioc.bzf.busisigning.dao;

import com.bonc.ioc.bzf.busisigning.entity.BbsRefundApproveInfoEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.busisigning.vo.*;

import java.util.List;

/**
 * 退款审批表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
@Mapper
public interface BbsRefundApproveInfoMapper extends McpBaseMapper<BbsRefundApproveInfoEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * <AUTHOR>
     * @date 2024-08-28
     * @change 2024-08-28 by pyj for init
     * @since 1.0.0
     */
    List<BbsRefundApproveInfoPageResultVo> selectByPageCustom(@Param("vo") BbsRefundApproveInfoPageVo vo);

    /**
     * 退款审核分页查询
     *
     * @param vo 退款审核分页查询请求参数
     * @return 退款审核分页结果
     */
    List<BbsRefundApplyPageResultVo> selectRefundApplyApproveByPage(@Param("vo") BbsRefundApplyPageVo vo);
}

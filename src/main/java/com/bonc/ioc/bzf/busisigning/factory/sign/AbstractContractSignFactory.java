package com.bonc.ioc.bzf.busisigning.factory.sign;

import cn.hutool.extra.spring.SpringUtil;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.consts.ContractTemplateConst;
import com.bonc.ioc.bzf.busisigning.consts.SymbolConst;
import com.bonc.ioc.bzf.busisigning.enums.ContractTypeEnum;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSignerVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.utils.MapUtil;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalCustomerVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalIncrementalConfigVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalProductVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalRelationVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultProductVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultRelationVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignIncrementalConfigVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 合同签约 抽象工厂类
 *
 * <AUTHOR>
 * @since 2024/10/22
 */
@Slf4j
public abstract class AbstractContractSignFactory {

    /**
     * feign服务 配置实例
     */
    protected FeignServiceConfiguration feignServiceConfiguration;

    /**
     * 业务服务 配置实例
     */
    protected BusinessServiceConfiguration businessServiceConfiguration;

    /**
     * 签约信息 vo实体实例
     */
    protected BbsSignInfoVo signInfoVo;

    /**
     * 续签信息 vo实体实例
     */
    protected BbsRenewalInfoVo renewalInfoVo;

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param signInfoVo                   签约信息 vo实体
     * @param renewalInfoVo                续签信息 vo实体
     */
    protected AbstractContractSignFactory(FeignServiceConfiguration feignServiceConfiguration,
                                          BusinessServiceConfiguration businessServiceConfiguration,
                                          BbsSignInfoVo signInfoVo,
                                          BbsRenewalInfoVo renewalInfoVo) {
        this.feignServiceConfiguration = feignServiceConfiguration;
        this.businessServiceConfiguration = businessServiceConfiguration;
        this.signInfoVo = signInfoVo;
        this.renewalInfoVo = renewalInfoVo;
    }

    /**
     * 获取实例
     *
     * @param signInfoVo 签约信息 vo实体
     * @return 实例
     */
    public static AbstractContractSignFactory getInstance(BbsSignInfoVo signInfoVo) {
        // 获取bean
        FeignServiceConfiguration feignServiceConfiguration = SpringUtil.getBean(FeignServiceConfiguration.class);
        BusinessServiceConfiguration businessServiceConfiguration = SpringUtil.getBean(BusinessServiceConfiguration.class);
        if (ContractTypeEnum.COMMON_CONTRACT.getCode().equals(signInfoVo.getContractType())) {
            return new CommonContractSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo,
                    null);
        } else if (ContractTypeEnum.LESSEE_CHANGE.getCode().equals(signInfoVo.getContractType())) {
            return new LesseeChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo,
                    null);
        } else if (ContractTypeEnum.OTHER_CHANGE.getCode().equals(signInfoVo.getContractType())) {
            return new OtherChangeSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo,
                    null);
        } else {
            return new CommonContractSignFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    signInfoVo,
                    null);
        }
    }

    /**
     * 获取实例
     *
     * @param renewalInfoVo 续签信息 vo实体
     * @return 实例
     */
    public static AbstractContractSignFactory getInstance(BbsRenewalInfoVo renewalInfoVo) {
        // 获取bean
        FeignServiceConfiguration feignServiceConfiguration = SpringUtil.getBean(FeignServiceConfiguration.class);
        BusinessServiceConfiguration businessServiceConfiguration = SpringUtil.getBean(BusinessServiceConfiguration.class);
        return new ReletContractSignFactory(feignServiceConfiguration,
                businessServiceConfiguration,
                null,
                renewalInfoVo);
    }

    /**
     * 获取递增规则
     *
     * @param standardType 标准类型
     * @return 递增规则列表
     */
    protected List<BbsSignIncrementalConfigVo> getIncrementalConfigList(String standardType) {
        return businessServiceConfiguration
                .getSignIncrementalConfigService()
                .selectBySignIdAndStandardType(signInfoVo.getSignId(), standardType);
    }

    /**
     * 获取递增规则(续签)
     *
     * @param standardType 标准类型
     * @return 递增规则列表
     */
    protected List<BbsRenewalIncrementalConfigVo> getRenewalIncrementalConfigList(String standardType) {
        return businessServiceConfiguration
                .getRenewalIncrementalConfigService()
                .selectBySignIdAndStandardType(renewalInfoVo.getSignId(), standardType);
    }

    /**
     * 获取人房关系信息
     *
     * @return 人房关系信息
     */
    protected BbsResultRelationVo getRelationInfo() {
        return businessServiceConfiguration
                .getResultRelationService()
                .selectBySignId(signInfoVo.getSignId());
    }

    /**
     * 获取人房关系信息(续签)
     *
     * @return 人房关系信息
     */
    protected BbsRenewalRelationVo getRenewalRelationInfo() {
        return businessServiceConfiguration
                .getRenewalRelationService()
                .selectBySignId(renewalInfoVo.getSignId());
    }

    /**
     * 获取客户信息
     *
     * @param rrId 人房关系id
     * @return 客户信息
     */
    protected BbsResultCustomerVo getCustomerInfo(String rrId) {
        return businessServiceConfiguration
                .getResultCustomerService()
                .selectByRrId(rrId);
    }

    /**
     * 获取客户信息(续签)
     *
     * @param rrId 人房关系id
     * @return 客户信息
     */
    protected BbsRenewalCustomerVo getRenewalCustomerInfo(String rrId) {
        return businessServiceConfiguration
                .getRenewalCustomerService()
                .selectByRrId(rrId);
    }

    /**
     * 获取产品列表
     *
     * @param rrId 人房关系id
     * @return 产品列表
     */
    protected List<BbsResultProductVo> getProductList(String rrId) {
        return businessServiceConfiguration
                .getResultProductService()
                .selectByRrId(rrId);
    }

    /**
     * 获取产品列表(续签)
     *
     * @param rrId 人房关系id
     * @return 产品列表
     */
    protected List<BbsRenewalProductVo> getRenewalProductList(String rrId) {
        return businessServiceConfiguration
                .getRenewalProductService()
                .selectByRrId(rrId);
    }

    /**
     * 获取模板属性map集
     *
     * @return 模板属性map集
     */
    protected Map<String, String> getTemplateSeatMap() {
        return businessServiceConfiguration
                .getTemplateSeatService()
                .selectMapByParentId(signInfoVo.getSignId());
    }

    /**
     * 获取模板属性map集(续签)
     *
     * @return 模板属性map集
     */
    protected Map<String, String> getRenewalTemplateSeatMap() {
        return businessServiceConfiguration
                .getRenewalTemplateSeatService()
                .selectMapByParentId(renewalInfoVo.getSignId());
    }

    /**
     * 预览信息map集赋值默认签约信息
     *
     * @param previewInfoMap 预览信息map集
     * @param customerVo     客户信息 vo实体
     * @param productList    产品信息列表
     */
    protected void setDefaultSignInfoToPreviewInfo(Map<String, Object> previewInfoMap,
                                                   BbsResultCustomerVo customerVo,
                                                   List<BbsResultProductVo> productList) {
        // 签约信息
        MapUtil.objectToMap(previewInfoMap, signInfoVo);
        // 签约客户信息
        if (!Objects.isNull(customerVo)) {
            MapUtil.objectToMap(previewInfoMap, customerVo, ContractTemplateConst.CONTRACT_CUSTOMER);
        }
        // 签约产品信息
        if (CollectionUtils.isNotEmpty(productList)) {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < productList.size(); i++) {
                MapUtil.objectToMap(previewInfoMap, productList.get(i), ContractTemplateConst.CONTRACT_PRODUCT, i);
                if (StringUtils.isNotBlank(productList.get(i).getProductName())) {
                    stringBuilder.append(productList.get(i).getProductName());
                    stringBuilder.append(SymbolConst.COMMA);
                }
            }
            String productNameArrayStr = stringBuilder.toString();
            if (StringUtils.isNotBlank(productNameArrayStr)) {
                previewInfoMap.put(ContractTemplateConst.CONTRACT_PRODUCT_PRODUCT_NAME_ARR_STR,
                        productNameArrayStr.substring(0, productNameArrayStr.length() - 1));
            }
        }
    }

    /**
     * 预览信息map集赋值默认签约信息(续签)
     *
     * @param previewInfoMap 预览信息map集
     * @param customerVo     客户信息 vo实体
     * @param productList    产品信息列表
     */
    protected void setDefaultRenewalInfoToPreviewInfo(Map<String, Object> previewInfoMap,
                                                      BbsRenewalCustomerVo customerVo,
                                                      List<BbsRenewalProductVo> productList) {
        // 签约信息
        MapUtil.objectToMap(previewInfoMap, renewalInfoVo);
        // 签约客户信息
        if (!Objects.isNull(customerVo)) {
            MapUtil.objectToMap(previewInfoMap, customerVo, ContractTemplateConst.CONTRACT_CUSTOMER);
        }
        // 签约产品信息
        if (CollectionUtils.isNotEmpty(productList)) {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < productList.size(); i++) {
                MapUtil.objectToMap(previewInfoMap, productList.get(i), ContractTemplateConst.CONTRACT_PRODUCT, i);
                if (StringUtils.isNotBlank(productList.get(i).getProductName())) {
                    stringBuilder.append(productList.get(i).getProductName());
                    stringBuilder.append(SymbolConst.COMMA);
                }
            }
            String productNameArrayStr = stringBuilder.toString();
            if (StringUtils.isNotBlank(productNameArrayStr)) {
                previewInfoMap.put(ContractTemplateConst.CONTRACT_PRODUCT_PRODUCT_NAME_ARR_STR,
                        productNameArrayStr.substring(0, productNameArrayStr.length() - 1));
            }
        }
    }

    /**
     * 预览信息map集赋值协议信息
     *
     * @param previewInfoMap 预览信息map集
     * @param customerVo     客户信息 vo实体
     * @param productList    产品信息列表
     */
    protected void setAgreementInfoToPreviewInfo(Map<String, Object> previewInfoMap,
                                                 BbsResultCustomerVo customerVo,
                                                 List<BbsResultProductVo> productList) {
        // 协议签约信息
        MapUtil.objectToMap(previewInfoMap, signInfoVo, ContractTemplateConst.AGREEMENT_SIGN);
        // 协议客户信息
        if (!Objects.isNull(customerVo)) {
            MapUtil.objectToMap(previewInfoMap, customerVo, ContractTemplateConst.AGREEMENT_CUSTOMER);
        }
        // 协议产品信息
        if (CollectionUtils.isNotEmpty(productList)) {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < productList.size(); i++) {
                MapUtil.objectToMap(previewInfoMap, productList.get(i), ContractTemplateConst.AGREEMENT_PRODUCT, i);
                if (StringUtils.isNotBlank(productList.get(i).getProductName())) {
                    stringBuilder.append(productList.get(i).getProductName());
                    stringBuilder.append(SymbolConst.COMMA);
                }
            }
            String productNameArrayStr = stringBuilder.toString();
            if (StringUtils.isNotBlank(productNameArrayStr)) {
                previewInfoMap.put(ContractTemplateConst.CONTRACT_PRODUCT_PRODUCT_NAME_ARR_STR,
                        productNameArrayStr.substring(0, productNameArrayStr.length() - 1));
            }
        }
    }

    /**
     * 预览信息map集赋值上级合同信息
     *
     * @param previewInfoMap   预览信息map集
     * @param parentContractVo 上级合同信息 vo实体
     */
    protected void setParentContractInfoToPreviewInfo(Map<String, Object> previewInfoMap,
                                                      BbctContractManagementVo parentContractVo) {
        // 上级合同信息
        MapUtil.objectToMap(previewInfoMap, parentContractVo, ContractTemplateConst.CONTRACT_SIGN);
        // 上级合同客户信息
        List<BbctContractSignerVo> parentUserList = parentContractVo.getUserList();
        if (CollectionUtils.isNotEmpty(parentUserList)) {
            MapUtil.objectToMap(previewInfoMap, parentUserList.get(0), ContractTemplateConst.CONTRACT_CUSTOMER);
        }
        // 上级合同产品信息
        List<BbctContractSubjectMatterVo> parentProductList = parentContractVo.getSubjectMatterList();
        if (CollectionUtils.isNotEmpty(parentProductList)) {
            for (int i = 0; i < parentProductList.size(); i++) {
                MapUtil.objectToMap(previewInfoMap, parentProductList.get(i), ContractTemplateConst.CONTRACT_PRODUCT, i);
            }
        }
    }

    /**
     * 合同中心 查询合同信息
     *
     * @param contractCode 合同编号
     * @return 合同信息
     */
    protected BbctContractManagementVo getContractInfoByContractCode(String contractCode) {
        AppReply<BbctContractManagementVo> appReply = feignServiceConfiguration.getContractFeignClient().selectByIdNo(contractCode, null);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    /**
     * 合同中心 生成合同pdf文件
     *
     * @param contractManagementVo 合同信息 vo实体
     * @return 合同pdf文件信息 vo实体
     */
    protected FIleResultVo createPdf(BbctContractManagementVo contractManagementVo) {
        AppReply<FIleResultVo> appReply = feignServiceConfiguration.getContractFeignClient().createPdf(contractManagementVo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("合同中心生成合同pdf失败[详情: %s, contractCode: %s]", appReply, signInfoVo.getContractCode()));
        }
        return appReply.getData();
    }

    /**
     * 预览或下载
     *
     * @param appFlag       app标签
     * @param watermarkType 水印类型
     * @return 合同文件信息 vo实体
     */
    public abstract FIleResultVo previewAndDownload(boolean appFlag, String watermarkType);

    /**
     * 创建预览信息 vo实体
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     * @return 预览信息 vo实体
     */
    public abstract BbctContractManagementVo createPreviewInfoVo(BbctPreviewInfoParamsVo previewInfoParamsVo);
}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * pos预检验
 *
 * <AUTHOR>
 * @date 2022/12/26 19:33
 * @change 2022/12/26 19:33 by l<PERSON><PERSON><PERSON> for init
 */
@Data
public class BankpreCheckBillVo {
    @ApiModelProperty(value = "批次总金额（单位：元）")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "批次总笔数")
    private Long totalNum;
    @ApiModelProperty(value = "收款账单列表")
    private List<BillIcbcBankVo> receiptBillList;
    @ApiModelProperty(value = "项目ID（业务中台）")
    private String projectId;
}

package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 签约状态 枚举类
 *
 * <AUTHOR>
 * @since 2023/5/6
 */
public enum SignStatusEnum {

    /**
     * 通过
     */
    TEMPORARY("1", "暂存"),

    /**
     * 待签约
     */
    WAIT_SIGN("2", "待签约"),

    /**
     * 已签约
     */
    SIGNED("3", "已签约"),

    /**
     * 未签约
     */
    UNSIGNED("4", "未签约"),

    /**
     * 待审核
     */
    WAIT_AUDIT("5", "待审核"),

    /**
     * 未通过
     */
    NO_PASS("6", "未通过"),

    /**
     * 租户已签
     */
    ALREADY_SIGN("7", "租户已签"),

    /**
     * 终止
     */
    STOP("8", "终止");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    SignStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    SignStatusEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        SignStatusEnum[] enums = values();
        for (SignStatusEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}

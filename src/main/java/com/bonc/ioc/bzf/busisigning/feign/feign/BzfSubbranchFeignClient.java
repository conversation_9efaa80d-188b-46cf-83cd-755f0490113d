package com.bonc.ioc.bzf.busisigning.feign.feign;


import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 描述内容
 *
 * <AUTHOR>
 * @date 2022/2/21 16:55
 * @change 2022/2/21 16:55 by z<PERSON><PERSON><PERSON>@bonc.com.cn for init
 */
@FeignClient(name = "feignCustomerService", url = "${serviceApi.subbankUrl}", configuration = FeignExceptionConfiguration.class)
public interface BzfSubbranchFeignClient {


    /**
     * 根据bankType获取支行列表
     *
     * @param bankParam
     * @return  SubbankResultVo
     * <AUTHOR>
     * @date 2022/3/16 15:42
     */
    @PostMapping(value = "/service/syncBanktypetoBankdocService", produces = "application/json;charset=UTF-8")
    @FeignExceptionCheck
    String syncBanktypetoBankdocService(@RequestBody List<Map<String,String>> bankParam);
}

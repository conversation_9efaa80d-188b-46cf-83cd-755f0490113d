package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 上传文件参数对象
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/8/17 17:39
 */

@Data
public class FileUploadFIleParmsVo {

    @ApiModelProperty(value = "项目编码")
    private String project;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "文件二进制的base64编码")
    private String fileBase64Code;

    @ApiModelProperty(value = "上传文件访问级别 PUBLIC_READ 未登录可读 默认PRIVATE 未登录不可访问")
    private String accessLevel;

}

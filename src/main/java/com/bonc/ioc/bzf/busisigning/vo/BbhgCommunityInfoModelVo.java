package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 小区信息表 实体类
 *
 * <AUTHOR>
 * @date 2022-07-29
 * @change 2022-07-29 by ccc for init
 */
@ApiModel(value = "BbhgCommunityInfoModelVo", description = "小区信息表")
public class BbhgCommunityInfoModelVo extends McpBaseVo implements Serializable {


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long infoId;

    /**
     * 小区名称
     */
    @ApiModelProperty(value = "小区名称")
    private String infoName;

    /**
     * 小区编码
     */
    @ApiModelProperty(value = "小区编码")
    private String infoCode;

    /**
     * 小区曾用名
     */
    @ApiModelProperty(value = "小区曾用名")
    private String infoFormerName;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String infoRegion;

    /**
     * 小区地址
     */
    @ApiModelProperty(value = "小区地址")
    private String infoAddr;

    /**
     * 建成年份
     */
    @ApiModelProperty(value = "建成年份")
    private String infoBuiltYear;

    /**
     * 小区总面积
     */
    @ApiModelProperty(value = "小区总面积")
    private String infoArea;

    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
    private String infoBuildArea;

    /**
     * 物业公司
     */
    @ApiModelProperty(value = "物业公司")
    private String infoProperty;

    /**
     * 楼栋数
     */
    @ApiModelProperty(value = "楼栋数")
    private Integer infoBuildNum;

    /**
     * 房屋数
     */
    @ApiModelProperty(value = "房屋数")
    private Integer infoHousing;

    /**
     * 运营主体
     */
    @ApiModelProperty(value = "运营主体")
    private String infoOperate;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String infoLat;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String infoLng;

    /**
     * 地上车位
     */
    @ApiModelProperty(value = "地上车位")
    private Integer infoOutdoorPark;

    /**
     * 地下车位
     */
    @ApiModelProperty(value = "地下车位")
    private Integer infoUndergroundPark;

    /**
     * 绿化率
     */
    @ApiModelProperty(value = "绿化率")
    private String infoAfforestationRate;

    /**
     * 容积率
     */
    @ApiModelProperty(value = "容积率")
    private String infoPlotRate;

    /**
     * 小区开发商
     */
    @ApiModelProperty(value = "小区开发商")
    private String infoDevelopers;

    /**
     * 物业费
     */
    @ApiModelProperty(value = "物业费")
    private String infoPropertyFee;

    /**
     * 供暖方式
     */
    @ApiModelProperty(value = "供暖方式")
    private String infoHeating;

    /**
     * 取暖费
     */
    @ApiModelProperty(value = "取暖费")
    private String infoHeatingWay;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String infoSource;

    /**
     * 配套设施
     */
    @ApiModelProperty(value = "配套设施")
    private String infoMatching;

    /**
     * 住宅数
     */
    @ApiModelProperty(value = "住宅数")
    private Integer infoResidential;

    /**
     * 商业数
     */
    @ApiModelProperty(value = "商业数")
    private Integer infoBusiness;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String infoIntroduce;


    /**
     * @return 主键
     */
    public Long getInfoId() {
        return infoId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    /**
     * @return 小区名称
     */
    public String getInfoName() {
        return infoName;
    }

    public void setInfoName(String infoName) {
        this.infoName = infoName;
    }

    /**
     * @return 小区编码
     */
    public String getInfoCode() {
        return infoCode;
    }

    public void setInfoCode(String infoCode) {
        this.infoCode = infoCode;
    }

    /**
     * @return 小区曾用名
     */
    public String getInfoFormerName() {
        return infoFormerName;
    }

    public void setInfoFormerName(String infoFormerName) {
        this.infoFormerName = infoFormerName;
    }

    /**
     * @return 区域
     */
    public String getInfoRegion() {
        return infoRegion;
    }

    public void setInfoRegion(String infoRegion) {
        this.infoRegion = infoRegion;
    }

    /**
     * @return 小区地址
     */
    public String getInfoAddr() {
        return infoAddr;
    }

    public void setInfoAddr(String infoAddr) {
        this.infoAddr = infoAddr;
    }

    /**
     * @return 建成年份
     */
    public String getInfoBuiltYear() {
        return infoBuiltYear;
    }

    public void setInfoBuiltYear(String infoBuiltYear) {
        this.infoBuiltYear = infoBuiltYear;
    }

    /**
     * @return 小区总面积
     */
    public String getInfoArea() {
        return infoArea;
    }

    public void setInfoArea(String infoArea) {
        this.infoArea = infoArea;
    }

    /**
     * @return 建筑面积
     */
    public String getInfoBuildArea() {
        return infoBuildArea;
    }

    public void setInfoBuildArea(String infoBuildArea) {
        this.infoBuildArea = infoBuildArea;
    }

    /**
     * @return 物业公司
     */
    public String getInfoProperty() {
        return infoProperty;
    }

    public void setInfoProperty(String infoProperty) {
        this.infoProperty = infoProperty;
    }

    /**
     * @return 楼栋数
     */
    public Integer getInfoBuildNum() {
        return infoBuildNum;
    }

    public void setInfoBuildNum(Integer infoBuildNum) {
        this.infoBuildNum = infoBuildNum;
    }

    /**
     * @return 房屋数
     */
    public Integer getInfoHousing() {
        return infoHousing;
    }

    public void setInfoHousing(Integer infoHousing) {
        this.infoHousing = infoHousing;
    }

    /**
     * @return 运营主体
     */
    public String getInfoOperate() {
        return infoOperate;
    }

    public void setInfoOperate(String infoOperate) {
        this.infoOperate = infoOperate;
    }

    /**
     * @return 纬度
     */
    public String getInfoLat() {
        return infoLat;
    }

    public void setInfoLat(String infoLat) {
        this.infoLat = infoLat;
    }

    /**
     * @return 经度
     */
    public String getInfoLng() {
        return infoLng;
    }

    public void setInfoLng(String infoLng) {
        this.infoLng = infoLng;
    }

    /**
     * @return 地上车位
     */
    public Integer getInfoOutdoorPark() {
        return infoOutdoorPark;
    }

    public void setInfoOutdoorPark(Integer infoOutdoorPark) {
        this.infoOutdoorPark = infoOutdoorPark;
    }

    /**
     * @return 地下车位
     */
    public Integer getInfoUndergroundPark() {
        return infoUndergroundPark;
    }

    public void setInfoUndergroundPark(Integer infoUndergroundPark) {
        this.infoUndergroundPark = infoUndergroundPark;
    }

    /**
     * @return 绿化率
     */
    public String getInfoAfforestationRate() {
        return infoAfforestationRate;
    }

    public void setInfoAfforestationRate(String infoAfforestationRate) {
        this.infoAfforestationRate = infoAfforestationRate;
    }

    /**
     * @return 容积率
     */
    public String getInfoPlotRate() {
        return infoPlotRate;
    }

    public void setInfoPlotRate(String infoPlotRate) {
        this.infoPlotRate = infoPlotRate;
    }

    /**
     * @return 小区开发商
     */
    public String getInfoDevelopers() {
        return infoDevelopers;
    }

    public void setInfoDevelopers(String infoDevelopers) {
        this.infoDevelopers = infoDevelopers;
    }

    /**
     * @return 物业费
     */
    public String getInfoPropertyFee() {
        return infoPropertyFee;
    }

    public void setInfoPropertyFee(String infoPropertyFee) {
        this.infoPropertyFee = infoPropertyFee;
    }

    /**
     * @return 供暖方式
     */
    public String getInfoHeating() {
        return infoHeating;
    }

    public void setInfoHeating(String infoHeating) {
        this.infoHeating = infoHeating;
    }

    /**
     * @return 取暖费
     */
    public String getInfoHeatingWay() {
        return infoHeatingWay;
    }

    public void setInfoHeatingWay(String infoHeatingWay) {
        this.infoHeatingWay = infoHeatingWay;
    }

    /**
     * @return 来源
     */
    public String getInfoSource() {
        return infoSource;
    }

    public void setInfoSource(String infoSource) {
        this.infoSource = infoSource;
    }

    /**
     * @return 配套设施
     */
    public String getInfoMatching() {
        return infoMatching;
    }

    public void setInfoMatching(String infoMatching) {
        this.infoMatching = infoMatching;
    }

    /**
     * @return 住宅数
     */
    public Integer getInfoResidential() {
        return infoResidential;
    }

    public void setInfoResidential(Integer infoResidential) {
        this.infoResidential = infoResidential;
    }

    /**
     * @return 商业数
     */
    public Integer getInfoBusiness() {
        return infoBusiness;
    }

    public void setInfoBusiness(Integer infoBusiness) {
        this.infoBusiness = infoBusiness;
    }

    /**
     * @return 简介
     */
    public String getInfoIntroduce() {
        return infoIntroduce;
    }

    public void setInfoIntroduce(String infoIntroduce) {
        this.infoIntroduce = infoIntroduce;
    }

    @Override
    public String toString() {
        return "BbhgCommunityInfoVo{" +
                "infoId=" + infoId +
                ", infoName=" + infoName +
                ", infoCode=" + infoCode +
                ", infoFormerName=" + infoFormerName +
                ", infoRegion=" + infoRegion +
                ", infoAddr=" + infoAddr +
                ", infoBuiltYear=" + infoBuiltYear +
                ", infoArea=" + infoArea +
                ", infoBuildArea=" + infoBuildArea +
                ", infoProperty=" + infoProperty +
                ", infoBuildNum=" + infoBuildNum +
                ", infoHousing=" + infoHousing +
                ", infoOperate=" + infoOperate +
                ", infoLat=" + infoLat +
                ", infoLng=" + infoLng +
                ", infoOutdoorPark=" + infoOutdoorPark +
                ", infoUndergroundPark=" + infoUndergroundPark +
                ", infoAfforestationRate=" + infoAfforestationRate +
                ", infoPlotRate=" + infoPlotRate +
                ", infoDevelopers=" + infoDevelopers +
                ", infoPropertyFee=" + infoPropertyFee +
                ", infoHeating=" + infoHeating +
                ", infoHeatingWay=" + infoHeatingWay +
                ", infoSource=" + infoSource +
                ", infoMatching=" + infoMatching +
                ", infoResidential=" + infoResidential +
                ", infoBusiness=" + infoBusiness +
                ", infoIntroduce=" + infoIntroduce +
                "}";
    }
}

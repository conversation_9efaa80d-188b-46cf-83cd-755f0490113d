package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 签约安排表v2.0 实体类
 *
 * <AUTHOR>
 * @date 2023-06-13
 * @change 2023-06-13 by 宋鑫 for init
 */
@ApiModel(value="BbsiBbciRuleArrangePageResultVo对象", description="签约安排表v2.0")
public class BbsiBbciRuleArrangePageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 安排id
     */
    @ApiModelProperty(value = "安排id")
    @NotBlank(message = "安排id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String arrangeId;

    /**
     * 规则id
     */
    @ApiModelProperty(value = "规则id")
                            private String ruleId;

    /**
     * 入住开始时间
     */
    @ApiModelProperty(value = "入住开始时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date checkInBeginTime;

    /**
     * 入住结束时间
     */
    @ApiModelProperty(value = "入住结束时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date checkInEndTime;

    /**
     * 入住地址
     */
    @ApiModelProperty(value = "入住地址")
                            private String checkInAddress;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
                            private String delFlag;

    /**
     * 签约计划id
     */
    @ApiModelProperty(value = "签约计划id")
                            private String signRuleId;

    /**
     * @return 安排id
     */
    public String getArrangeId() {
        return arrangeId;
    }

    public void setArrangeId(String arrangeId) {
        this.arrangeId = arrangeId;
    }

    /**
     * @return 规则id
     */
    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    /**
     * @return 入住开始时间
     */
    public Date getCheckInBeginTime(){
        if(checkInBeginTime!=null){
            return (Date)checkInBeginTime.clone();
        }else{
            return null;
        }
    }

    public void setCheckInBeginTime(Date checkInBeginTime) {
        if(checkInBeginTime==null){
            this.checkInBeginTime = null;
        }else{
            this.checkInBeginTime = (Date)checkInBeginTime.clone();
        }
    }

    /**
     * @return 入住结束时间
     */
    public Date getCheckInEndTime(){
        if(checkInEndTime!=null){
            return (Date)checkInEndTime.clone();
        }else{
            return null;
        }
    }

    public void setCheckInEndTime(Date checkInEndTime) {
        if(checkInEndTime==null){
            this.checkInEndTime = null;
        }else{
            this.checkInEndTime = (Date)checkInEndTime.clone();
        }
    }

    /**
     * @return 入住地址
     */
    public String getCheckInAddress() {
        return checkInAddress;
    }

    public void setCheckInAddress(String checkInAddress) {
        this.checkInAddress = checkInAddress;
    }

    /**
     * @return 删除标识(1.未删除 0.已删除)
     */
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 签约计划id
     */
    public String getSignRuleId() {
        return signRuleId;
    }

    public void setSignRuleId(String signRuleId) {
        this.signRuleId = signRuleId;
    }

      @Override
    public String toString() {
        return "BbsiBbciRuleArrangePageResultVo{" +
            "arrangeId=" + arrangeId +
            ", ruleId=" + ruleId +
            ", checkInBeginTime=" + checkInBeginTime +
            ", checkInEndTime=" + checkInEndTime +
            ", checkInAddress=" + checkInAddress +
            ", delFlag=" + delFlag +
            ", signRuleId=" + signRuleId +
        "}";
    }
}

package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName DateVo
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2023-08-03 9:27
 **/
@Data
@ApiModel(value="日期 vo实体", description="日期 vo实体")
public class DateVo extends McpBaseVo implements Serializable {

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date date;
}

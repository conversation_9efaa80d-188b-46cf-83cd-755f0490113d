package com.bonc.ioc.bzf.busisigning.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsResultCustomerMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultCustomerEntity;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsResultCustomerService;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultCustomerVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 人-产品：客户表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by liwenqiang for init
 */
@Slf4j
@Service
public class BbsResultCustomerServiceImpl extends McpBaseServiceImpl<BbsResultCustomerEntity> implements IBbsResultCustomerService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsResultCustomerMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsResultCustomerService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsResultCustomerVo vo) {
        if(vo == null) {
            return null;
        }

        BbsResultCustomerEntity entity = new BbsResultCustomerEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setRcId(null);
        if(!baseService.insert(entity)) {
            log.error("人-产品：客户表新增失败:" + entity.toString());
            throw new McpException("人-产品：客户表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getRcId(),1)) {
                log.error("人-产品：客户表新增后保存历史失败:" + entity.toString());
                throw new McpException("人-产品：客户表新增后保存历史失败");
            }

            log.debug("人-产品：客户表新增成功:"+entity.getRcId());
            return entity.getRcId();
        }
     }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsResultCustomerEntity entity) {
        if(entity == null) {
            return null;
        }

//        BbsResultCustomerEntity entity = new BbsResultCustomerEntity();
//        BeanUtils.copyProperties(vo, entity);

        entity.setRcId(null);
        if(!baseService.insert(entity)) {
            log.error("人-产品：客户表新增失败:" + entity.toString());
            throw new McpException("人-产品：客户表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getRcId(),1)) {
                log.error("人-产品：客户表新增后保存历史失败:" + entity.toString());
                throw new McpException("人-产品：客户表新增后保存历史失败");
            }

            log.debug("人-产品：客户表新增成功:"+entity.getRcId());
            return entity.getRcId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsResultCustomerVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsResultCustomerEntity> entityList = new ArrayList<>();
        for (BbsResultCustomerVo item:voList) {
            BbsResultCustomerEntity entity = new BbsResultCustomerEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsResultCustomerEntity item:entityList){
            item.setRcId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("人-产品：客户表新增失败");
            throw new McpException("人-产品：客户表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsResultCustomerEntity::getRcId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("人-产品：客户表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("人-产品：客户表批量新增后保存历史失败");
            }

            log.debug("人-产品：客户表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param rcId 需要删除的
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String rcId) {
        if(!StringUtils.isEmpty(rcId)) {
            if(!baseService.saveOperationHisById(rcId,3)) {
                log.error("人-产品：客户表删除后保存历史失败:" + rcId);
                throw new McpException("人-产品：客户表删除后保存历史失败");
            }

            if(!baseService.removeById(rcId)) {
                log.error("人-产品：客户表删除失败");
                throw new McpException("人-产品：客户表删除失败"+rcId);
            }
        } else {
            throw new McpException("人-产品：客户表删除失败为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rcIdList 需要删除的
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> rcIdList) {
        if(!CollectionUtils.isEmpty(rcIdList)) {
            int oldSize = rcIdList.size();
            rcIdList = rcIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(rcIdList) || oldSize != rcIdList.size()) {
                throw new McpException("人-产品：客户表批量删除失败 存在主键id为空的记录"+StringUtils.join(rcIdList));
            }

            if(!baseService.saveOperationHisByIds(rcIdList,3)) {
                log.error("人-产品：客户表批量删除后保存历史失败:" + StringUtils.join(rcIdList));
                throw new McpException("人-产品：客户表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(rcIdList)) {
                log.error("人-产品：客户表批量删除失败");
                throw new McpException("人-产品：客户表批量删除失败"+StringUtils.join(rcIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsResultCustomerVo vo) {
        if(vo != null) {
            BbsResultCustomerEntity entity = new BbsResultCustomerEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getRcId())) {
                throw new McpException("人-产品：客户表更新失败传入为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("人-产品：客户表更新失败");
                throw new McpException("人-产品：客户表更新失败"+entity.getRcId());
            } else {
                if(!baseService.saveOperationHisById(entity.getRcId(),2)) {
                    log.error("人-产品：客户表更新后保存历史失败:" + entity.getRcId());
                    throw new McpException("人-产品：客户表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("人-产品：客户表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsResultCustomerVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsResultCustomerEntity> entityList = new ArrayList<>();

            for (BbsResultCustomerVo item:voList){
                BbsResultCustomerEntity entity = new BbsResultCustomerEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getRcId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("人-产品：客户表批量更新失败 存在为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("人-产品：客户表批量更新失败");
                throw new McpException("人-产品：客户表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getRcId())).map(BbsResultCustomerEntity::getRcId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("人-产品：客户表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("人-产品：客户表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsResultCustomerVo vo) {
        if(vo != null) {
            BbsResultCustomerEntity entity = new BbsResultCustomerEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("人-产品：客户表保存失败");
                throw new McpException("人-产品：客户表保存失败"+entity.getRcId());
            } else {
                if(!baseService.saveOperationHisById(entity.getRcId(),4)) {
                    log.error("人-产品：客户表保存后保存历史失败:" + entity.getRcId());
                    throw new McpException("人-产品：客户表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("人-产品：客户表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsResultCustomerVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsResultCustomerEntity> entityList = new ArrayList<>();

            for (BbsResultCustomerVo item:voList){
                BbsResultCustomerEntity entity = new BbsResultCustomerEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("人-产品：客户表批量保存失败");
                throw new McpException("人-产品：客户表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getRcId())).map(BbsResultCustomerEntity::getRcId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("人-产品：客户表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("人-产品：客户表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param rcId 需要查询的
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsResultCustomerVo selectByIdRecord(String rcId) {
        BbsResultCustomerVo vo = new BbsResultCustomerVo();

        if(!StringUtils.isEmpty(rcId)) {
            BbsResultCustomerEntity entity = baseService.selectById(rcId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change
     * 2023-08-29 by liwenqiang for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsResultCustomerPageResultVo>> selectByPageRecord(BbsResultCustomerPageVo vo) {
        List<BbsResultCustomerPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * countByProductNoes 查询产品是否存在
     *
     * @param productNoes 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-06
     */
    public List<Map<String,Object>> countByProductNoes(List<String> productNoes) {
        return baseMapper.countByProductNoes(productNoes);
    }

    /**
     * countByContractCode 查询产品是否存在
     *
     * @param contractCode 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-06
     */
    public List<Map<String,Object>> countByContractCode(String contractCode) {
        return baseMapper.countByContractCode(contractCode);
    }

    /**
     * 根据人房关系id查询
     *
     * @param rrId 人房关系id
     * @return 客户信息 vo实体
     */
    @Override
    public BbsResultCustomerVo selectByRrId(String rrId) {
        BbsResultCustomerEntity entity = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsResultCustomerEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsResultCustomerEntity::getRrId, rrId)
                .one();
        if (Objects.isNull(entity)) {
            return null;
        }
        BbsResultCustomerVo vo = new BbsResultCustomerVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}

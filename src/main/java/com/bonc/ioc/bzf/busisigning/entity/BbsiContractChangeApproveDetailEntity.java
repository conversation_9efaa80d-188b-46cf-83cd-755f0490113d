package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 合并变更审核detail表
 *
 * <AUTHOR> @date
 * @change
 */
@TableName("bbs_contract_change_approve_detail")
@ApiModel(value = "BbsiContractChangeEntity对象", description = "合同变更审批表")
public class BbsiContractChangeApproveDetailEntity extends McpBaseEntity implements Serializable {


    @TableId(value = "approve_detail_id", type = IdType.ASSIGN_UUID)
    private String approveId;

    @ApiModelProperty(value = "合同id")
    private String ccId;


    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;


    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverUserId;


    /**
     * 审核人（模糊搜索）
     */
    @ApiModelProperty(value = "审核人姓名")
    private String approverUserName;

    /**
     * 意见说明
     */
    @ApiModelProperty(value = "意见说明")
    private String commentExplanation;

    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    @TableField(exist = false)
    private Integer delFlag;

    public String getApproveId() {
        return approveId;
    }

    public void setApproveId(String approveId) {
        this.approveId = approveId;
    }

    /**
     * @return
     */
    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }



    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getApproverUserId() {
        return approverUserId;
    }


    public void setApproverUserId(String approverUserId) {
        this.approverUserId = approverUserId;
    }

    public String getApproverUserName() {
        return approverUserName;
    }

    public void setApproverUserName(String approverUserName) {
        this.approverUserName = approverUserName;
    }

    public Date getApproveTime() {
        if (approveTime != null) {
            return (Date) approveTime.clone();
        } else {
            return null;
        }
    }

    public void setApproveTime(Date approveTime) {
        if (approveTime == null) {
            this.approveTime = null;
        } else {
            this.approveTime = (Date) approveTime.clone();
        }
    }

    public String getCommentExplanation() {
        return commentExplanation;
    }

    public void setCommentExplanation(String commentExplanation) {
        this.commentExplanation = commentExplanation;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "BbsiContractChangeApproveEntity{" +
                "approveId='" + approveId + '\'' +
                ", ccId='" + ccId + '\'' +
                ", approveStatus='" + approveStatus + '\'' +
                ", approveTime=" + approveTime +
                ", approverUserId='" + approverUserId + '\'' +
                ", approverUserName='" + approverUserName + '\'' +
                '}';
    }
}
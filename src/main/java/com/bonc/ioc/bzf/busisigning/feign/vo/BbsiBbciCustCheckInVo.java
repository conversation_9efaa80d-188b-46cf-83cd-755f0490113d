package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 签约入住表v2.0 实体类
 *
 * <AUTHOR>
 * @date 2023-06-13
 * @change 2023-06-13 by 宋鑫 for init
 */
@ApiModel(value="BbsiBbciCustCheckInVo对象", description="签约入住表v2.0")
public class BbsiBbciCustCheckInVo extends McpBaseVo implements Serializable{


    /**
     * 入住id
     */
    @ApiModelProperty(value = "入住id")
    @NotBlank(message = "入住id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String checkInId;

    /**
     * 安排id
     */
    @ApiModelProperty(value = "安排id")
                            private String arrangeId;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
                            private String planId;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id")
                            private String communityId;

    /**
     * 签约结果id
     */
    @ApiModelProperty(value = "签约结果id")
                            private String arrangeCustomerId;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date signTime;

    /**
     * 入住时间
     */
    @ApiModelProperty(value = "入住时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                    private Date checkInTime;

    /**
     * 楼栋号
     */
    @ApiModelProperty(value = "楼栋号")
                            private String buildingNo;

                            private String unitNo;

    /**
     * 房源id
     */
    @ApiModelProperty(value = "房源id")
                            private String houseResourceId;

    /**
     * 房源
     */
    @ApiModelProperty(value = "房源")
                            private String houseResource;

    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
                            private String houseRent;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
                            private String businessType;

    /**
     * 办理状态0.未办理 1.已办理
     */
    @ApiModelProperty(value = "办理状态0.未办理 1.已办理")
                            private Boolean processingState;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
                            private String customerId;

    /**
     * 短信通知id
     */
    @ApiModelProperty(value = "短信通知id")
                            private String messageNoticeId;

    /**
     * 短信通知结果1成功2失败
     */
    @ApiModelProperty(value = "短信通知结果1成功2失败")
                            private String messageNoticeResult;

    /**
     * 站内信通知id
     */
    @ApiModelProperty(value = "站内信通知id")
                            private String mailNoticeId;

    /**
     * 站内信通知结果1成功2失败
     */
    @ApiModelProperty(value = "站内信通知结果1成功2失败")
                            private String mailNoticeResult;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 1-未发送 2已发送
     */
    @ApiModelProperty(value = "1-未发送 2已发送")
                            private String sendState;

    /**
     * 入住状态;1:待入住;2:已入住;3:未入住4已退租
     */
    @ApiModelProperty(value = "入住状态;1:待入住;2:已入住;3:未入住4已退租")
                            private String checkInState;

    /**
     * 1未生效2已生效
     */
    @ApiModelProperty(value = "1未生效2已生效")
                            private String assertState;

    /**
     * 重新入住状态(1:未设置重新入住;2:已设置重新入住)
     */
    @ApiModelProperty(value = "重新入住状态(1:未设置重新入住;2:已设置重新入住)")
                            private String reCheckInState;

    /**
     * 签约计划id
     */
    @ApiModelProperty(value = "签约计划id")
                            private String signId;

    /**
     * 配租计划id
     */
    @ApiModelProperty(value = "配租计划id")
                            private String peizuPlanId;

    /**
     * 配置计划名称
     */
    @ApiModelProperty(value = "配置计划名称")
                            private String peizuPlanName;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
                            private String delFlag;
    @ApiModelProperty(value = "项目id 用于控制权限")
    private String projectId;



    @ApiModelProperty(value = "人房关系id")
    private String relationId;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }
    /**
     * @return 入住id
     */
    public String getCheckInId() {
        return checkInId;
    }

    public void setCheckInId(String checkInId) {
        this.checkInId = checkInId;
    }

    /**
     * @return 安排id
     */
    public String getArrangeId() {
        return arrangeId;
    }

    public void setArrangeId(String arrangeId) {
        this.arrangeId = arrangeId;
    }

    /**
     * @return 计划id
     */
    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    /**
     * @return 小区id
     */
    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    /**
     * @return 签约结果id
     */
    public String getArrangeCustomerId() {
        return arrangeCustomerId;
    }

    public void setArrangeCustomerId(String arrangeCustomerId) {
        this.arrangeCustomerId = arrangeCustomerId;
    }

    /**
     * @return 签约时间
     */
    public Date getSignTime(){
        if(signTime!=null){
            return (Date)signTime.clone();
        }else{
            return null;
        }
    }

    public void setSignTime(Date signTime) {
        if(signTime==null){
            this.signTime = null;
        }else{
            this.signTime = (Date)signTime.clone();
        }
    }

    /**
     * @return 入住时间
     */
    public Date getCheckInTime(){
        if(checkInTime!=null){
            return (Date)checkInTime.clone();
        }else{
            return null;
        }
    }

    public void setCheckInTime(Date checkInTime) {
        if(checkInTime==null){
            this.checkInTime = null;
        }else{
            this.checkInTime = (Date)checkInTime.clone();
        }
    }

    /**
     * @return 楼栋号
     */
    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    /**
     * @return 
     */
    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    /**
     * @return 房源id
     */
    public String getHouseResourceId() {
        return houseResourceId;
    }

    public void setHouseResourceId(String houseResourceId) {
        this.houseResourceId = houseResourceId;
    }

    /**
     * @return 房源
     */
    public String getHouseResource() {
        return houseResource;
    }

    public void setHouseResource(String houseResource) {
        this.houseResource = houseResource;
    }

    /**
     * @return 租金
     */
    public String getHouseRent() {
        return houseRent;
    }

    public void setHouseRent(String houseRent) {
        this.houseRent = houseRent;
    }

    /**
     * @return 业务类型
     */
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * @return 办理状态0.未办理 1.已办理
     */
    public Boolean getProcessingState() {
        return processingState;
    }

    public void setProcessingState(Boolean processingState) {
        this.processingState = processingState;
    }

    /**
     * @return 客户id
     */
    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * @return 短信通知id
     */
    public String getMessageNoticeId() {
        return messageNoticeId;
    }

    public void setMessageNoticeId(String messageNoticeId) {
        this.messageNoticeId = messageNoticeId;
    }

    /**
     * @return 短信通知结果1成功2失败
     */
    public String getMessageNoticeResult() {
        return messageNoticeResult;
    }

    public void setMessageNoticeResult(String messageNoticeResult) {
        this.messageNoticeResult = messageNoticeResult;
    }

    /**
     * @return 站内信通知id
     */
    public String getMailNoticeId() {
        return mailNoticeId;
    }

    public void setMailNoticeId(String mailNoticeId) {
        this.mailNoticeId = mailNoticeId;
    }

    /**
     * @return 站内信通知结果1成功2失败
     */
    public String getMailNoticeResult() {
        return mailNoticeResult;
    }

    public void setMailNoticeResult(String mailNoticeResult) {
        this.mailNoticeResult = mailNoticeResult;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 1-未发送 2已发送
     */
    public String getSendState() {
        return sendState;
    }

    public void setSendState(String sendState) {
        this.sendState = sendState;
    }

    /**
     * @return 入住状态;1:待入住;2:已入住;3:未入住4已退租
     */
    public String getCheckInState() {
        return checkInState;
    }

    public void setCheckInState(String checkInState) {
        this.checkInState = checkInState;
    }

    /**
     * @return 1未生效2已生效
     */
    public String getAssertState() {
        return assertState;
    }

    public void setAssertState(String assertState) {
        this.assertState = assertState;
    }

    /**
     * @return 重新入住状态(1:未设置重新入住;2:已设置重新入住)
     */
    public String getReCheckInState() {
        return reCheckInState;
    }

    public void setReCheckInState(String reCheckInState) {
        this.reCheckInState = reCheckInState;
    }

    /**
     * @return 签约计划id
     */
    public String getSignId() {
        return signId;
    }

    public void setSignId(String signId) {
        this.signId = signId;
    }

    /**
     * @return 配租计划id
     */
    public String getPeizuPlanId() {
        return peizuPlanId;
    }

    public void setPeizuPlanId(String peizuPlanId) {
        this.peizuPlanId = peizuPlanId;
    }

    /**
     * @return 配置计划名称
     */
    public String getPeizuPlanName() {
        return peizuPlanName;
    }

    public void setPeizuPlanName(String peizuPlanName) {
        this.peizuPlanName = peizuPlanName;
    }

    /**
     * @return 删除标识(1.未删除 0.已删除)
     */
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsiBbciCustCheckInVo{" +
            "checkInId=" + checkInId +
            ", arrangeId=" + arrangeId +
            ", planId=" + planId +
            ", communityId=" + communityId +
            ", arrangeCustomerId=" + arrangeCustomerId +
            ", signTime=" + signTime +
            ", checkInTime=" + checkInTime +
            ", buildingNo=" + buildingNo +
            ", unitNo=" + unitNo +
            ", houseResourceId=" + houseResourceId +
            ", houseResource=" + houseResource +
            ", houseRent=" + houseRent +
            ", businessType=" + businessType +
            ", processingState=" + processingState +
            ", customerId=" + customerId +
            ", messageNoticeId=" + messageNoticeId +
            ", messageNoticeResult=" + messageNoticeResult +
            ", mailNoticeId=" + mailNoticeId +
            ", mailNoticeResult=" + mailNoticeResult +
            ", contractCode=" + contractCode +
            ", sendState=" + sendState +
            ", checkInState=" + checkInState +
            ", assertState=" + assertState +
            ", reCheckInState=" + reCheckInState +
            ", signId=" + signId +
            ", peizuPlanId=" + peizuPlanId +
            ", peizuPlanName=" + peizuPlanName +
            ", delFlag=" + delFlag +
        "}";
    }
}

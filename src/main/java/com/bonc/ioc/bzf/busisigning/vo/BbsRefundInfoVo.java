package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 退款申请表 实体类
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
@Data
@ApiModel(value = "BbsRefundInfoVo对象", description = "退款申请表")
public class BbsRefundInfoVo extends McpBaseVo implements Serializable {

    /**
     * 退款id
     */
    @ApiModelProperty(value = "退款id")
    @NotBlank(message = "退款id不能为空", groups = {UpdateValidatorGroup.class})
    private String refundId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 补充协议编号
     */
    @ApiModelProperty(value = "补充协议编号")
    private String agreementNo;

    /**
     * 签约id
     */
    @ApiModelProperty(value = "签约id")
    private String signId;

    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型")
    private String changeType;

    /**
     * 退款申请状态
     */
    @ApiModelProperty(value = "退款申请状态")
    @McpDictPoint(dictCode = "REFUND_STATUS", overTransCopyTo = "refundStatusName")
    private String refundStatus;

    /**
     * 退款申请状态名称
     */
    @ApiModelProperty(value = "退款申请状态名称")
    private String refundStatusName;

    /**
     * 退款进度
     */
    @ApiModelProperty(value = "退款进度")
    @McpDictPoint(dictCode = "REFUND_PROCESS", overTransCopyTo = "refundProcessName")
    private String refundProcess;

    /**
     * 退款进度名称
     */
    @ApiModelProperty(value = "退款进度名称")
    private String refundProcessName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 承租人编号
     */
    @ApiModelProperty(value = "承租人编号")
    private String customerNo;

    /**
     * 承租人名称
     */
    @ApiModelProperty(value = "承租人名称")
    private String customerName;

    /**
     * 协议文件id
     */
    @ApiModelProperty(value = "协议文件id")
    private String agreementFileId;

    /**
     * 退回路径
     */
    @ApiModelProperty(value = "退回路径")
    @McpDictPoint(dictCode = "BACK_TYPE", overTransCopyTo = "backTypeName")
    private String backType;

    /**
     * 退回路径名称
     */
    @ApiModelProperty(value = "退回路径名称")
    private String backTypeName;

    /**
     * 银行卡卡号
     */
    @ApiModelProperty(value = "银行卡卡号")
    private String bankAccountId;

    /**
     * 银行卡户名
     */
    @ApiModelProperty(value = "银行卡户名")
    private String bankAccountName;

    /**
     * 开户行编码
     */
    @ApiModelProperty(value = "开户行编码")
    private String bankCode;

    /**
     * 开户行名称
     */
    @ApiModelProperty(value = "开户行名称")
    private String bankName;

    /**
     * 开户支行编码
     */
    @ApiModelProperty(value = "开户支行编码")
    private String subBankCode;

    /**
     * 开户支行名称
     */
    @ApiModelProperty(value = "开户支行名称")
    private String subBankName;

    /**
     * 开户行所在省编码
     */
    @ApiModelProperty(value = "开户行所在省编码")
    private String bankProvinceCode;

    /**
     * 开户行所在省名称
     */
    @ApiModelProperty(value = "开户行所在省名称")
    private String bankProvinceName;

    /**
     * 开户行所在市编码
     */
    @ApiModelProperty(value = "开户行所在市编码")
    private String bankCityCode;

    /**
     * 开户行所在市名称
     */
    @ApiModelProperty(value = "开户行所在市名称")
    private String bankCityName;

    /**
     * 开户人证件类型
     */
    @ApiModelProperty(value = "开户人证件类型")
    @McpDictPoint(dictCode = "CERTIFICATE_TYPE", overTransCopyTo = "customerIdTypeName")
    private String customerIdType;

    /**
     * 开户人证件类型名称
     */
    @ApiModelProperty(value = "开户人证件类型名称")
    private String customerIdTypeName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 修改原因
     */
    @ApiModelProperty(value = "修改原因")
    private String changeReason;

    /**
     * 相关材料
     */
    @ApiModelProperty(value = "相关材料")
    private String relatedFileId;

    /**
     * 付款单唯一标识
     */
    @ApiModelProperty(value = "付款单唯一标识")
    private String paymentCode;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private Integer delFlag;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;
}

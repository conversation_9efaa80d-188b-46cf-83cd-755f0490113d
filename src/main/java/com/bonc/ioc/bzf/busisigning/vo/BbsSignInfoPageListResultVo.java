package com.bonc.ioc.bzf.busisigning.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 签约表 实体类
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by <PERSON><PERSON><PERSON><PERSON><PERSON> for init
 */
@Data
@ApiModel(value = "BbsSignInfoPageResultVo对象", description = "签约表")
public class BbsSignInfoPageListResultVo extends BasePageResultVo implements Serializable {
    /**
     * 签约id
     */
    @ApiModelProperty(value = "签约id")
    private String signId;
    /**
     * 变更类型，1合同信息
     */
    @ApiModelProperty(value = "变更类型，1合同信息")
    private String changeType;
    /**
     * 小区名称
     */
    @ApiModelProperty(value = "小区名称")
    private String communityBuildingName;

    /**
     * 商铺地址
     */
    @ApiModelProperty(value = "商铺地址")
    private String productName;

    /**
     * 租户
     */
    @ApiModelProperty(value = "租户")
    private String customerName;

    /**
     * 租户性质
     */
    @ApiModelProperty(value = "租户性质")
    private String customerType;

    /**
     * 业态
     */
    @ApiModelProperty(value = "业态")
    private String businessFormatName;

    /**
     * 面积（㎡）
     */
    @ApiModelProperty(value = "面积")
    private String houseStructArea;
    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractStartTime;
    /**
     * 合同结束日期
     * */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;

    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
    private String rentName;

    /**
     * 押金
     */
    @ApiModelProperty(value = "押金")
    private String cashPledge;

    /**
     * 缴费周期
     */
    @ApiModelProperty(value = "缴费周期")
    private String paymentCycle;

    /**
     * 免租期
     */
    @ApiModelProperty(value = "免租期")
    private String rentFree;
    /**
     * 免租期类型
     * */
    @ApiModelProperty(value = "免租期类型")
    private String rentFreePeriodType;
    /**
     * 租金免租期值
     * */
    @ApiModelProperty(value = "租金免租期值")
    private String rentFpFixedValue;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String customerTel;
    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date signTime;

    /**
     * 签约状态 1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止
     */
    @ApiModelProperty(value = "签约状态")
    private String signStatus;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "合同审核状态名称")
    private String approveStatusName;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "合同审核状态")
    private String approveStatus;

    /**
     * 签约状态 1.暂存 2.待签约 3.已签约 4.未签约 5.待审核 6.未通过 7.租户已签 8.终止
     * */
    private String signStatusName;

    /**
     * 合同是否带水印(0.无 1.甲 2.乙)
     */
    @ApiModelProperty(value = "合同是否带水印(0.无 1.甲 2.乙)")
    private String contractWatermark;

    /**
     * 合同审签状态
     */
    @ApiModelProperty(value = "合同审签状态")
    private String contractSQ;

    /**
     * 合同审核状态
     */
    @ApiModelProperty(value = "合同审核状态")
    private String contractSH;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 非标/标准（合同）
     */
    @ApiModelProperty(value = "非标/标准（合同）")
    private String contractType;

    /**
     * 父级合同编号
     */
    @ApiModelProperty(value = "父级合同编号(原合同编号)")
    private String parentContractCode;

    @ApiModelProperty(value = "审批人姓名")
    private String approverUserName;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 面积类型(1.建筑面积 2.套内建筑面积)
     */
    @ApiModelProperty(value = "面积类型(1.建筑面积 2.套内建筑面积)")
    private String areaType;
}

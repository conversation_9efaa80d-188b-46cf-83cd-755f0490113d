1. 需求说明
   
       总的来说就是工银新增了缴费周期变更和免租期变更的试算接口，需要动态的根据类型判断调用工银哪个接口去获取数据，并保存返回的整体json。
   
       我已经列好了要改造的位置， TODO 改造1 、TODO 改造2、TODO 改造3、TODO 改造4。
   
      重新在bbs_contract_change新增一个字段新试算接口返回，回显的时候需要把json转实体；
   
      getPreviewBillByVo接口返回的previewBillResultVo.billInfoVoList是把新试算接口按houseId返回的数据合并在一个list里面
   
       具体说明：
   
   - 缴费周期变更、免租期变更都是其他信息变更中的一种类型，其他信息变更类型code为ChangeTypeEnum.OTHER_CHANGE.getCode()，缴费周期变更类型code为ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode(),免租期变更类型code为ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode()
   
   - 如果changeTypeItem类型为缴费周期变更和免租期变更时，调用BbctPaymentV2FeignClient中changeTrial方法从工银获取数据的，原来都是调用BbctPaymentV2FeignClient中getPreviewBills方法
   
   - 工银请求和返回的接口文档参见[3.64合同变更试算 (qq.com)](https://docs.qq.com/doc/DV1ZBT0RmR2RsRnZa?no_promotion=1)
   
   - 由于我们这边试算接口返回还是ChangeCalculationBillVo，我在里面新增一个返回的试算账单列表previewBillResultVo.billInfoVoList，提取接口中返回的多houseId账单列表汇总成一个，即：getPreviewBillByVo接口主要返回billInfoVoList和rentingOutDetailVOList，还就是feesSummaryList
   
   - BbctPaymentV2FeignClient中changeTrial方法返回的rentingOutDetailVOList
     
     个人或企业字段不会同时有值，要么是个人要么是企业，哪个大于0就取哪个
   
   - 原来审批通过后会调用下面方法保存试算账单AbstractContractChangeFactory.getInstance(preNewContractChangeVo).insertPreviewBills()，现在我打算把缴费周期和免租期试算结果直接用json格式保存在主表bbs_contract_change中

2. 实现步骤
   
   - /getPreviewBillByVo
   
   
